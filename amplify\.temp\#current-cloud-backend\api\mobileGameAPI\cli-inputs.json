{"version": 1, "paths": {"/checkMemberCodeExist": {"name": "/checkMemberCodeExist", "lambdaFunction": "mobileGameAPIFunction", "permissions": {"setting": "open"}}, "/getUserDetail": {"name": "/getUserDetail", "lambdaFunction": "mobileGameAPIFunction", "permissions": {"setting": "open"}}, "/createGameActivity": {"name": "/createGameActivity", "lambdaFunction": "mobileGameAPIFunction", "permissions": {"setting": "open"}}, "/recordMissionTime": {"name": "/recordMissionTime", "lambdaFunction": "mobileGameAPIFunction", "permissions": {"setting": "open"}}, "/gameLogout": {"name": "/gameLogout", "lambdaFunction": "mobileGameAPIFunction", "permissions": {"setting": "open"}}, "/getStudentListWithRank": {"name": "/getStudentListWithRank", "lambdaFunction": "mobileGameAPIFunction", "permissions": {"setting": "open"}}}}