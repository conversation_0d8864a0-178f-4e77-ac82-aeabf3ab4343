# Bedrock Chatbot Test Queries

## GraphQL Query Examples

### Basic Test Query
```graphql
query TestBedrockChat {
  testBedrockChat(input: {
    message: "Hello, this is a test message"
    userId: "test-user-123"
    sessionId: "test-session-456"
  }) {
    statusCode
    message
    data {
      response
      sessionId
      timestamp
      model
      usage {
        inputTokens
        outputTokens
        totalTokens
      }
    }
  }
}
```

### Test with Different Messages
```graphql
query TestBedrockChatHelp {
  testBedrockChat(input: {
    message: "I need help with something"
    userId: "user-789"
  }) {
    statusCode
    message
    data {
      response
      sessionId
      timestamp
      model
      usage {
        inputTokens
        outputTokens
        totalTokens
      }
    }
  }
}
```

### Test Query Variables
```graphql
query TestBedrockChatWithVariables($input: BedrockChatInput!) {
  testBedrockChat(input: $input) {
    statusCode
    message
    data {
      response
      sessionId
      timestamp
      model
      usage {
        inputTokens
        outputTokens
        totalTokens
      }
    }
  }
}
```

#### Variables for the above query:
```json
{
  "input": {
    "message": "What can you tell me about AI and machine learning?",
    "userId": "user-456",
    "sessionId": "session-789"
  }
}
```

## Expected Response Format

```json
{
  "data": {
    "testBedrockChat": {
      "statusCode": 200,
      "message": "Chat processed successfully",
      "data": {
        "response": "Hello! How can I assist you today?",
        "sessionId": "session-1703123456789-abc123def",
        "timestamp": "2023-12-21T10:30:45.123Z",
        "model": "mock-bedrock-model",
        "usage": {
          "inputTokens": 8,
          "outputTokens": 10,
          "totalTokens": 18
        }
      }
    }
  }
}
```

## Testing Instructions

1. Deploy the function using `amplify push`
2. Use GraphQL Playground or your frontend to test the queries
3. Check CloudWatch logs at `/aws/lambda/bedrockchatbot-[env]` for detailed logging
4. Verify the response structure matches the expected format

## CloudWatch Log Groups

After deployment, logs will appear in:
- `/aws/lambda/bedrockchatbot-amplifydev` (or your environment name)

## Next Steps for Real Bedrock Integration

1. Add AWS Bedrock SDK dependency to package.json
2. Replace the mock `processChatMessage` function with real Bedrock API calls
3. Configure specific Bedrock model parameters
4. Add proper error handling for Bedrock-specific errors
5. Implement conversation history and context management
