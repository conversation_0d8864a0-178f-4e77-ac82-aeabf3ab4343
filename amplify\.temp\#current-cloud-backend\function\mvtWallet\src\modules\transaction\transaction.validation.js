const { 
  validateMVTAmount, 
  isNonEmptyString, 
  isValidUserId 
} = require('../../shared/utils/validationUtils');

/**
 * Validate mint input (MVT amounts must be integers)
 * @param {object} input - Mint input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateMintInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { amount, description } = input;

  // Use strict MVT integer validation for minting
  const amountValidation = validateMVTAmount(amount);
  if (!amountValidation.isValid) {
    return amountValidation;
  }

  if (description && !isNonEmptyString(description)) {
    return {
      isValid: false,
      error: "Description must be a non-empty string if provided"
    };
  }

  return { isValid: true };
}

/**
 * Validate transfer input (MVT amounts must be integers)
 * @param {object} input - Transfer input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateTransferInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { userId, amount, description } = input;

  if (!isValidUserId(userId)) {
    return {
      isValid: false,
      error: "Valid user ID is required"
    };
  }

  // Use strict MVT integer validation for transfers
  const amountValidation = validateMVTAmount(amount);
  if (!amountValidation.isValid) {
    return amountValidation;
  }

  if (description && !isNonEmptyString(description)) {
    return {
      isValid: false,
      error: "Description must be a non-empty string if provided"
    };
  }

  return { isValid: true };
}

/**
 * Validate user-to-user transfer input (MVT amounts must be integers)
 * @param {object} input - User transfer input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateUserTransferInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { recipientUserId, amount, description } = input;

  if (!isValidUserId(recipientUserId)) {
    return {
      isValid: false,
      error: "Valid recipient user ID is required"
    };
  }

  // Use strict MVT integer validation for user transfers
  const amountValidation = validateMVTAmount(amount);
  if (!amountValidation.isValid) {
    return amountValidation;
  }

  if (description && !isNonEmptyString(description)) {
    return {
      isValid: false,
      error: "Description must be a non-empty string if provided"
    };
  }

  return { isValid: true };
}

/**
 * Validate transaction ID
 * @param {string} transactionId - Transaction ID to validate
 * @returns {object} - Validation result
 */
function validateTransactionId(transactionId) {
  if (!transactionId || !isNonEmptyString(transactionId)) {
    return {
      isValid: false,
      error: "Valid transaction ID is required"
    };
  }

  return { isValid: true };
}

/**
 * Validate transaction list request parameters
 * @param {object} params - Request parameters
 * @returns {object} - Validation result
 */
function validateTransactionListRequest(params) {
  const { address, isAdmin, limit } = params;

  if (isAdmin !== undefined && typeof isAdmin !== 'boolean') {
    return {
      isValid: false,
      error: "isAdmin must be a boolean value"
    };
  }

  if (limit !== undefined) {
    if (typeof limit !== 'number' || limit <= 0 || !Number.isInteger(limit)) {
      return {
        isValid: false,
        error: "Limit must be a positive integer"
      };
    }

    if (limit > 1000) {
      return {
        isValid: false,
        error: "Limit cannot exceed 1000 transactions"
      };
    }
  }

  if (address && !isNonEmptyString(address)) {
    return {
      isValid: false,
      error: "Address must be a non-empty string if provided"
    };
  }

  return { isValid: true };
}

module.exports = {
  validateMintInput,
  validateTransferInput,
  validateUserTransferInput,
  validateTransactionId,
  validateTransactionListRequest
};
