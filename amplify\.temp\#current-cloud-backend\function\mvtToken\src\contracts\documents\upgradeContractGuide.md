# Upgradeable Smart Contract: Full Guide with Examples (UUPS Proxy)

This document demonstrates a full upgrade flow using OpenZeppelin's UUPS proxy pattern. It includes:
- Initial contract (`V1`)
- Upgrade to `V2` (changing an error message)
- Upgrade to `V3` (adding a new function)
- Steps using Hardhat

---

## 1. Initial Setup

### Install Required Packages
```bash
npm install --save-dev hardhat @openzeppelin/contracts-upgradeable @openzeppelin/hardhat-upgrades
```

### Enable Hardhat Upgrades
```js
// hardhat.config.js
require('@openzeppelin/hardhat-upgrades');
```

---

## 2. Contract V1 - Basic Token

```solidity
// contracts/MVTTokenV1.sol
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";

contract MVTTokenV1 is Initializable, ERC20Upgradeable, OwnableUpgradeable, UUPSUpgradeable {
    bool public mintingFinished;

    event Minted(address indexed to, uint256 amount);

    modifier canMint() {
        require(!mintingFinished, "Minting is permanently disabled");
        _;
    }

    constructor() {
        _disableInitializers();
    }

    function initialize(string memory name, string memory symbol) public initializer {
        __ERC20_init(name, symbol);
        __Ownable_init(msg.sender);
        __UUPSUpgradeable_init();

        mintingFinished = false;
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function mint(uint256 amount) external onlyOwner canMint {
        require(amount > 0, "Amount must be greater than zero");
        _mint(address(this), amount);
        emit Minted(address(this), amount);
    }

    function finishMinting() external onlyOwner {
        mintingFinished = true;
    }
}
```

### Deploy V1
```js
// scripts/deploy-v1.js
const { ethers, upgrades } = require("hardhat");

async function main() {
  const MVTToken = await ethers.getContractFactory("MVTTokenV1");
  const token = await upgrades.deployProxy(MVTToken, ["MyToken", "MVT"], { initializer: "initialize" });
  await token.deployed();
  console.log("Proxy deployed to:", token.address);
}

main();
```

---

## 3. Contract V2 - Change Error Message

```solidity
// contracts/MVTTokenV2.sol
pragma solidity ^0.8.20;

import "./MVTTokenV1.sol";

contract MVTTokenV2 is MVTTokenV1 {
    function mint(uint256 amount) public override onlyOwner canMint {
        require(amount > 0, "MVT token amount must be greater than zero");
        _mint(address(this), amount);
        emit Minted(address(this), amount);
    }
}
```

### Upgrade to V2
```js
// scripts/upgrade-to-v2.js
const { ethers, upgrades } = require("hardhat");

async function main() {
  const V2 = await ethers.getContractFactory("MVTTokenV2");
  const upgraded = await upgrades.upgradeProxy("<PROXY_ADDRESS>", V2);
  console.log("Upgraded to V2 at:", upgraded.address);
}

main();
```

---

## 4. Contract V3 - Add New Function

```solidity
// contracts/MVTTokenV3.sol
pragma solidity ^0.8.20;

import "./MVTTokenV2.sol";

contract MVTTokenV3 is MVTTokenV2 {
    function getContractVersion() public pure returns (string memory) {
        return "V3";
    }
}
```

### Upgrade to V3
```js
// scripts/upgrade-to-v3.js
const { ethers, upgrades } = require("hardhat");

async function main() {
  const V3 = await ethers.getContractFactory("MVTTokenV3");
  const upgraded = await upgrades.upgradeProxy("<PROXY_ADDRESS>", V3);
  console.log("Upgraded to V3 at:", upgraded.address);
}

main();
```

---

## 5. General Guidelines for Upgradeable Contracts

- Always add new state variables at the end.
- Never remove or rename existing variables.
- Avoid constructor logic — use `initialize()`.
- Use `virtual` if you plan to override functions.
- Use `@openzeppelin/hardhat-upgrades` to validate storage compatibility.
- Reserve slots using: `uint256[50] private __gap;`

---

## 6. ABI and Frontend Sync

Each upgrade changes the ABI. After upgrading:
- Regenerate ABI:
  ```bash
  npx hardhat compile
  ```
- Replace ABI in your Node.js frontend/backend app to match the new version.

---

## ✅ Summary
| Version | Change                            | Upgrade Needed? |
|---------|------------------------------------|------------------|
| V1      | Initial deploy                     | No               |
| V2      | Modified error message             | Yes              |
| V3      | Added new `getContractVersion()`   | Yes              |

You can upgrade **unlimited times** as long as you maintain the storage layout.

---

Let me know if you'd like a working GitHub repo for this or scripts to automate the full flow.

