/**
 * Test script to verify central wallet balance updates during swap approval
 * This script simulates the swap approval process and verifies that the central wallet balance increases correctly
 */

const { AWS } = require('./config/aws');
const walletService = require('./modules/wallet/wallet.service');
const swapService = require('./modules/swap/swap.service');

// Configure AWS for local testing
AWS.config.update({
  region: process.env.AWS_REGION || 'us-east-1',
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

/**
 * Test central wallet balance update during MVT transfer
 */
async function testCentralWalletBalanceUpdate() {
  console.log('🧪 Testing Central Wallet Balance Update During Swap Approval');
  console.log('=' .repeat(70));

  try {
    // Test parameters
    const testUserId = 'test-user-' + Date.now();
    const testAmount = 5; // 5 MVT tokens
    
    console.log(`📋 Test Parameters:`);
    console.log(`   User ID: ${testUserId}`);
    console.log(`   Transfer Amount: ${testAmount} MVT`);
    console.log('');

    // Step 1: Get initial central wallet balance
    console.log('📊 Step 1: Getting initial central wallet balance...');
    const initialCentralBalance = await walletService.getCentralWalletBalance();
    console.log(`   Initial Central Balance: ${initialCentralBalance.balance} MVT`);
    console.log(`   Initial Total Received: ${initialCentralBalance.totalReceived} MVT`);
    console.log('');

    // Step 2: Create test user with sufficient balance and locked tokens
    console.log('👤 Step 2: Setting up test user with sufficient balance...');
    
    // First, ensure user has sufficient balance
    await walletService.updateUserBalance(testUserId, testAmount + 10, 0, 0);
    
    // Lock the required amount for the test
    await walletService.lockUserMVTTokens(testUserId, testAmount);
    
    const userBalance = await walletService.getUserBalance(testUserId);
    console.log(`   User Balance: ${userBalance.balance} MVT`);
    console.log(`   User Locked Balance: ${userBalance.lockedBalance} MVT`);
    console.log(`   User Available Balance: ${userBalance.availableBalance} MVT`);
    console.log('');

    // Step 3: Execute the atomic transfer
    console.log('⚡ Step 3: Executing atomic MVT transfer to central wallet...');
    const transferResult = await walletService.transferLockedMVTToCentral(testUserId, testAmount);
    
    console.log(`   Transfer Result:`);
    console.log(`     Success: ${transferResult.success}`);
    console.log(`     Previous Central Balance: ${transferResult.previousCentralBalance} MVT`);
    console.log(`     New Central Balance: ${transferResult.newCentralBalance} MVT`);
    console.log(`     Central Balance Increase: ${transferResult.newCentralBalance - transferResult.previousCentralBalance} MVT`);
    console.log(`     Previous User Balance: ${transferResult.previousUserBalance} MVT`);
    console.log(`     New User Balance: ${transferResult.newUserBalance} MVT`);
    console.log(`     User Balance Decrease: ${transferResult.previousUserBalance - transferResult.newUserBalance} MVT`);
    console.log('');

    // Step 4: Verify balances with consistent reads
    console.log('🔍 Step 4: Verifying balances with consistent reads...');
    
    // Wait a moment for potential eventual consistency
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const verifiedCentralBalance = await walletService.getCentralWalletBalance(true); // Consistent read
    const verifiedUserBalance = await walletService.getUserBalance(testUserId);
    
    console.log(`   Verified Central Balance: ${verifiedCentralBalance.balance} MVT`);
    console.log(`   Verified Central Total Received: ${verifiedCentralBalance.totalReceived} MVT`);
    console.log(`   Verified User Balance: ${verifiedUserBalance.balance} MVT`);
    console.log(`   Verified User Locked Balance: ${verifiedUserBalance.lockedBalance} MVT`);
    console.log('');

    // Step 5: Validate results
    console.log('✅ Step 5: Validating results...');
    
    const expectedCentralBalance = initialCentralBalance.balance + testAmount;
    const expectedCentralTotalReceived = (initialCentralBalance.totalReceived || 0) + testAmount;
    const expectedUserBalance = transferResult.previousUserBalance - testAmount;
    
    const centralBalanceCorrect = Math.abs(verifiedCentralBalance.balance - expectedCentralBalance) < 0.001;
    const centralTotalReceivedCorrect = Math.abs(verifiedCentralBalance.totalReceived - expectedCentralTotalReceived) < 0.001;
    const userBalanceCorrect = Math.abs(verifiedUserBalance.balance - expectedUserBalance) < 0.001;
    const userLockedBalanceCorrect = verifiedUserBalance.lockedBalance === 0; // Should be unlocked after transfer
    
    console.log(`   Central Balance Correct: ${centralBalanceCorrect ? '✅' : '❌'} (Expected: ${expectedCentralBalance}, Actual: ${verifiedCentralBalance.balance})`);
    console.log(`   Central Total Received Correct: ${centralTotalReceivedCorrect ? '✅' : '❌'} (Expected: ${expectedCentralTotalReceived}, Actual: ${verifiedCentralBalance.totalReceived})`);
    console.log(`   User Balance Correct: ${userBalanceCorrect ? '✅' : '❌'} (Expected: ${expectedUserBalance}, Actual: ${verifiedUserBalance.balance})`);
    console.log(`   User Locked Balance Correct: ${userLockedBalanceCorrect ? '✅' : '❌'} (Expected: 0, Actual: ${verifiedUserBalance.lockedBalance})`);
    console.log('');

    // Step 6: Summary
    const allTestsPassed = centralBalanceCorrect && centralTotalReceivedCorrect && userBalanceCorrect && userLockedBalanceCorrect;
    
    console.log('📋 Test Summary:');
    console.log(`   Overall Result: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    console.log(`   Central Wallet Balance Update: ${centralBalanceCorrect ? 'WORKING' : 'FAILED'}`);
    console.log(`   Token Accounting Consistency: ${allTestsPassed ? 'MAINTAINED' : 'BROKEN'}`);
    console.log('');

    if (!allTestsPassed) {
      console.log('❌ CRITICAL ISSUE: Central wallet balance update is not working correctly!');
      console.log('   This will cause token accounting inconsistencies in the system.');
      console.log('   Please review the atomic transaction implementation.');
    } else {
      console.log('🎉 SUCCESS: Central wallet balance update is working correctly!');
      console.log('   Token accounting consistency is maintained.');
    }

    return allTestsPassed;

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testCentralWalletBalanceUpdate()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = {
  testCentralWalletBalanceUpdate
};
