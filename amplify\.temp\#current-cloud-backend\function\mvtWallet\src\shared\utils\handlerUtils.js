const authService = require('../services/authService');
const responseUtils = require('./responseUtils');
const { createLogger, logError, logSuccess } = require('./logger');
const standardizedErrorUtils = require('./standardizedErrorUtils');

/**
 * Common authorization patterns
 */
const AuthorizationTypes = {
  ADMIN_ONLY: 'ADMIN_ONLY',
  USER_AUTHENTICATED: 'USER_AUTHENTICATED',
  USER_OR_ADMIN: 'USER_OR_ADMIN',
  NONE: 'NONE'
};

/**
 * Execute a handler with common patterns (authorization, validation, error handling)
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @param {object} options - Handler options
 * @param {function} businessLogic - Business logic function to execute
 * @returns {Promise<object>} - Response object
 */
async function executeHandler(event, args, options, businessLogic) {
  const logger = createLogger(event.requestContext || {}, {
    operation: options.operationName || 'handler',
    fieldName: event.fieldName
  });

  const startTime = Date.now();

  try {
    logger.info({
      operation: options.operationName || 'handler',
      fieldName: event.fieldName,
      hasInput: !!(args.input || args)
    }, `Starting ${options.operationName || 'handler'} operation`);

    // Step 1: Authorization
    const authResult = await handleAuthorization(event, args, options.authorization);
    if (authResult.error) {
      logger.warn({
        operation: options.operationName || 'handler',
        authorizationType: options.authorization?.type,
        reason: 'authorization_failed'
      }, 'Authorization failed');
      return authResult.error;
    }

    logger.debug({
      operation: options.operationName || 'handler',
      authContext: {
        isAdmin: authResult.context.isAdmin,
        hasUserId: !!authResult.context.currentUserDatabaseId
      }
    }, 'Authorization successful');

    // Step 2: Input validation
    if (options.validation) {
      const validationResult = options.validation(args.input || args);
      if (!validationResult.isValid) {
        logger.warn({
          operation: options.operationName || 'handler',
          validationError: validationResult.error,
          reason: 'validation_failed'
        }, 'Input validation failed');
        return standardizedErrorUtils.createValidationError(
          options.operationName || 'handler',
          validationResult.error,
          'input'
        );
      }
      logger.debug({
        operation: options.operationName || 'handler'
      }, 'Input validation successful');
    }

    // Step 3: Execute business logic with auth context
    const result = await businessLogic(authResult.context);

    // Step 4: Return success response
    const response = responseUtils.createSuccessResponse(
      result.data,
      result.message || options.successMessage || "Operation completed successfully"
    );

    logSuccess(logger, options.operationName || 'handler', result, {
      statusCode: response.statusCode,
      hasData: !!result.data,
      duration: Date.now() - startTime
    });

    return response;

  } catch (error) {
    logError(logger, error, options.operationName || 'handler', {
      fieldName: event.fieldName,
      hasInput: !!(args.input || args),
      duration: Date.now() - startTime
    });
    return standardizedErrorUtils.createAutoDetectedError(
      options.operationName || 'handler',
      error,
      'operation execution'
    );
  }
}

/**
 * Handle authorization based on type and context
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @param {object} authConfig - Authorization configuration
 * @returns {Promise<object>} - Authorization result with context
 */
async function handleAuthorization(event, args, authConfig) {
  const { type, requireUserId = false } = authConfig;

  switch (type) {
    case AuthorizationTypes.ADMIN_ONLY: {
      const isAuthorized = await authService.checkAdminAuthorization(event);
      if (!isAuthorized) {
        return { error: standardizedErrorUtils.createAuthorizationError(
          'handler',
          'Unauthorized: Admin access required'
        ) };
      }

      const context = { isAdmin: true };
      if (requireUserId) {
        context.adminUserId = await authService.getCurrentUserDatabaseId(event);
        if (!context.adminUserId) {
          return { error: standardizedErrorUtils.createAuthorizationError(
            'handler',
            'Admin user not found in database'
          ) };
        }
      }
      return { context };
    }

    case AuthorizationTypes.USER_AUTHENTICATED: {
      const currentUserDatabaseId = await authService.getCurrentUserDatabaseId(event);
      if (!currentUserDatabaseId) {
        return { error: standardizedErrorUtils.createAuthorizationError(
          'handler',
          'Authentication required'
        ) };
      }
      return { context: { currentUserDatabaseId, isAdmin: false } };
    }

    case AuthorizationTypes.USER_OR_ADMIN: {
      const currentUserDatabaseId = await authService.getCurrentUserDatabaseId(event);
      if (!currentUserDatabaseId) {
        return { error: standardizedErrorUtils.createAuthorizationError(
          'handler',
          'Authentication required'
        ) };
      }

      const isAdmin = await authService.checkAdminAuthorization(event);
      const context = { currentUserDatabaseId, isAdmin };

      // Handle admin checking other user's data
      if (args.input?.userId && args.input.userId !== currentUserDatabaseId) {
        if (!isAdmin) {
          return { error: standardizedErrorUtils.createAuthorizationError(
            'handler',
            'Unauthorized: Only admins can access other users\' data'
          ) };
        }
        context.targetUserId = args.input.userId;
      } else {
        context.targetUserId = currentUserDatabaseId;
      }

      return { context };
    }

    case AuthorizationTypes.NONE:
    default:
      return { context: {} };
  }
}

/**
 * Handle user balance authorization (for balance-related endpoints)
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Authorization result with user context
 */
async function handleUserBalanceAuthorization(event, args) {
  const currentUserDatabaseId = await authService.getCurrentUserDatabaseId(event);
  if (!currentUserDatabaseId) {
    return { error: standardizedErrorUtils.createAuthorizationError(
      'handleUserBalanceAuthorization',
      'Authentication required'
    ) };
  }

  const requestedUserId = args.input?.userId;
  
  if (requestedUserId) {
    // Admin is checking another user's balance
    const isAdmin = await authService.checkAdminAuthorization(event);
    if (!isAdmin) {
      return { error: standardizedErrorUtils.createAuthorizationError(
        'handleUserBalanceAuthorization',
        'Unauthorized: Only admins can check other users\' balances'
      ) };
    }
    return { context: { targetUserId: requestedUserId, isAdmin: true } };
  } else {
    // User checking their own balance
    return { context: { targetUserId: currentUserDatabaseId, isAdmin: false } };
  }
}

/**
 * Handle transaction list authorization
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Authorization result with context
 */
async function handleTransactionListAuthorization(event, args) {
  const currentCognitoId = await authService.getUserIdFromEvent(event);
  if (!currentCognitoId) {
    return { error: standardizedErrorUtils.createAuthorizationError(
      'handleTransactionListAuthorization',
      'Authentication required'
    ) };
  }

  const { address, isAdmin = false } = args;

  if (isAdmin) {
    const authResult = await authService.checkAdminAuthorization(event);
    if (!authResult) {
      return { error: responseUtils.createForbiddenResponse("Unauthorized: Admin access required") };
    }
    return { context: { address, isAdmin: true } };
  } else {
    // For non-admin requests, ensure user can only access their own transactions
    const currentUserDatabaseId = await authService.getCurrentUserDatabaseId(event);
    if (!currentUserDatabaseId || address !== currentUserDatabaseId) {
      return { error: responseUtils.createForbiddenResponse("Access denied: You can only view your own transactions") };
    }
    return { context: { address, isAdmin: false } };
  }
}

/**
 * Handle swap request list authorization
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Authorization result with context
 */
async function handleSwapRequestListAuthorization(event, args) {
  const currentCognitoId = await authService.getUserIdFromEvent(event);
  if (!currentCognitoId) {
    return { error: responseUtils.createUnauthorizedResponse("Authentication required") };
  }

  const { isAdmin = false } = args;

  if (isAdmin) {
    const authResult = await authService.checkAdminAuthorization(event);
    if (!authResult) {
      return { error: responseUtils.createForbiddenResponse("Unauthorized: Admin access required") };
    }
    return { context: { userId: null, isAdmin: true } };
  } else {
    // For non-admin requests, only show user's own requests
    const userId = await authService.getCurrentUserDatabaseId(event);
    if (!userId) {
      return { error: responseUtils.createUnauthorizedResponse("User not found in database") };
    }
    return { context: { userId, isAdmin: false } };
  }
}

module.exports = {
  AuthorizationTypes,
  executeHandler,
  handleAuthorization,
  handleUserBalanceAuthorization,
  handleTransactionListAuthorization,
  handleSwapRequestListAuthorization
};
