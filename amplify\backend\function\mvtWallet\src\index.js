/* Amplify Params - DO NOT EDIT
	API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT
	API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT
	AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID
	ENV
	REGION
Amplify Params - DO NOT EDIT */

// Import new modular resolver and utilities
const { routeToResolver } = require('./resolver');
const responseUtils = require('./shared/utils/responseUtils');
const { createLogger, logError, logSuccess } = require('./shared/utils/logger');

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event, context) => {
  const logger = createLogger(context, {
    functionName: 'mvtWallet',
    fieldName: event.fieldName
  });

  const startTime = Date.now();

  try {
    // Extract field name and arguments from GraphQL event
    const fieldName = event.fieldName;
    const args = event.arguments;

    logger.info({
      fieldName,
      hasArguments: !!args,
      operation: 'route_request'
    }, `Processing GraphQL field: ${fieldName}`);

    // Route request to appropriate resolver using new modular system
    const result = await routeToResolver(fieldName, event, args);

    logSuccess(logger, 'mvtWallet_handler', result, {
      fieldName,
      statusCode: result.statusCode,
      duration: Date.now() - startTime
    });

    return result;
  } catch (error) {
    logError(logger, error, 'mvtWallet_handler', {
      fieldName: event.fieldName,
      hasArguments: !!event.arguments,
      duration: Date.now() - startTime
    });
    return responseUtils.createInternalErrorResponse(error.message || "Internal server error");
  }
};