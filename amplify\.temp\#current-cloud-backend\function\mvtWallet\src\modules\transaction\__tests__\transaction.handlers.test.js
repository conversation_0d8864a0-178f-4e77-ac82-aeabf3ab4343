/**
 * Transaction Handlers Test Suite
 * Tests for transaction GraphQL handlers including authentication, validation, and response formatting
 */

const transactionHandlers = require('../transaction.handlers');
const transactionService = require('../transaction.service');
const authService = require('../../../shared/services/authService');
const validationUtils = require('../../../shared/utils/validationUtils');
const responseUtils = require('../../../shared/utils/responseUtils');

// Mock dependencies
jest.mock('../transaction.service');
jest.mock('../../../shared/services/authService');
jest.mock('../../../shared/utils/validationUtils');
jest.mock('../../../shared/utils/responseUtils');

describe('Transaction Handlers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('handleAdminMintMVT', () => {
    const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
    const mockArgs = global.testUtils.createMockArgs({
      amount: 1000,
      description: 'Test mint operation'
    });

    test('should successfully mint MVT tokens', async () => {
      // Arrange
      const mockTransaction = global.testUtils.createMockTransaction('mint-123', 'ADMIN_MINT', 1000);
      
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateMintInput.mockReturnValue({ isValid: true });
      transactionService.mintMVTTokens.mockResolvedValue(mockTransaction);
      responseUtils.createSuccessResponse.mockReturnValue({
        statusCode: 200,
        message: 'Successfully minted 1000 MVT tokens',
        data: mockTransaction
      });

      // Act
      const result = await transactionHandlers.handleAdminMintMVT(mockEvent, mockArgs);

      // Assert
      expect(authService.checkAdminAuthorization).toHaveBeenCalledWith(mockEvent);
      expect(validationUtils.validateMintInput).toHaveBeenCalledWith(mockArgs.input);
      expect(transactionService.mintMVTTokens).toHaveBeenCalledWith(
        1000,
        'Test mint operation',
        'admin-user-123'
      );
      expect(result.statusCode).toBe(200);
    });

    test('should reject non-admin users', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(false);
      responseUtils.createForbiddenResponse.mockReturnValue({
        statusCode: 403,
        message: 'Unauthorized: Admin access required'
      });

      // Act
      const result = await transactionHandlers.handleAdminMintMVT(mockEvent, mockArgs);

      // Assert
      expect(authService.checkAdminAuthorization).toHaveBeenCalledWith(mockEvent);
      expect(transactionService.mintMVTTokens).not.toHaveBeenCalled();
      expect(result.statusCode).toBe(403);
    });

    test('should reject invalid input', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateMintInput.mockReturnValue({
        isValid: false,
        error: 'Amount must be a positive integer'
      });
      responseUtils.createBadRequestResponse.mockReturnValue({
        statusCode: 400,
        message: 'Amount must be a positive integer'
      });

      // Act
      const result = await transactionHandlers.handleAdminMintMVT(mockEvent, mockArgs);

      // Assert
      expect(validationUtils.validateMintInput).toHaveBeenCalledWith(mockArgs.input);
      expect(transactionService.mintMVTTokens).not.toHaveBeenCalled();
      expect(result.statusCode).toBe(400);
    });

    test('should handle service errors', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateMintInput.mockReturnValue({ isValid: true });
      transactionService.mintMVTTokens.mockRejectedValue(new Error('Database error'));
      responseUtils.handleServiceError.mockReturnValue({
        statusCode: 500,
        message: 'Internal server error'
      });

      // Act
      const result = await transactionHandlers.handleAdminMintMVT(mockEvent, mockArgs);

      // Assert
      expect(responseUtils.handleServiceError).toHaveBeenCalled();
      expect(result.statusCode).toBe(500);
    });
  });

  describe('handleAdminTransferMVT', () => {
    const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
    const mockArgs = global.testUtils.createMockArgs({
      userId: 'target-user-123',
      amount: 500,
      description: 'Admin transfer to user'
    });

    test('should successfully transfer MVT tokens to user', async () => {
      // Arrange
      const mockTransaction = global.testUtils.createMockTransaction('transfer-123', 'CENTRAL_TO_USER_TRANSFER', 500);
      
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateTransferInput.mockReturnValue({ isValid: true });
      transactionService.transferMVTToUser.mockResolvedValue(mockTransaction);

      // Act
      const result = await transactionHandlers.handleAdminTransferMVT(mockEvent, mockArgs);

      // Assert
      expect(transactionService.transferMVTToUser).toHaveBeenCalledWith(
        'target-user-123',
        500,
        'Admin transfer to user',
        'admin-user-123'
      );
      expect(result.statusCode).toBe(200);
    });

    test('should validate transfer input', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateTransferInput.mockReturnValue({
        isValid: false,
        error: 'Invalid user ID'
      });

      // Act
      const result = await transactionHandlers.handleAdminTransferMVT(mockEvent, mockArgs);

      // Assert
      expect(validationUtils.validateTransferInput).toHaveBeenCalledWith(mockArgs.input);
      expect(transactionService.transferMVTToUser).not.toHaveBeenCalled();
      expect(result.statusCode).toBe(400);
    });
  });

  describe('handleUserTransferMVT', () => {
    const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
    const mockArgs = global.testUtils.createMockArgs({
      recipientUserId: 'recipient-user-123',
      amount: 100,
      description: 'User to user transfer'
    });

    test('should successfully transfer MVT between users', async () => {
      // Arrange
      const mockTransaction = global.testUtils.createMockTransaction('user-transfer-123', 'USER_TO_USER_TRANSFER', 100);
      
      authService.getCurrentUserDatabaseId.mockResolvedValue('sender-user-123');
      validationUtils.validateUserTransferInput.mockReturnValue({ isValid: true });
      transactionService.transferMVTBetweenUsers.mockResolvedValue(mockTransaction);

      // Act
      const result = await transactionHandlers.handleUserTransferMVT(mockEvent, mockArgs);

      // Assert
      expect(transactionService.transferMVTBetweenUsers).toHaveBeenCalledWith(
        'sender-user-123',
        'recipient-user-123',
        100,
        'User to user transfer'
      );
      expect(result.statusCode).toBe(200);
    });

    test('should reject unauthenticated users', async () => {
      // Arrange
      authService.getCurrentUserDatabaseId.mockResolvedValue(null);

      // Act
      const result = await transactionHandlers.handleUserTransferMVT(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(401);
      expect(transactionService.transferMVTBetweenUsers).not.toHaveBeenCalled();
    });

    test('should validate user transfer input', async () => {
      // Arrange
      authService.getCurrentUserDatabaseId.mockResolvedValue('sender-user-123');
      validationUtils.validateUserTransferInput.mockReturnValue({
        isValid: false,
        error: 'Recipient user ID is required'
      });

      // Act
      const result = await transactionHandlers.handleUserTransferMVT(mockEvent, mockArgs);

      // Assert
      expect(validationUtils.validateUserTransferInput).toHaveBeenCalledWith(mockArgs.input);
      expect(result.statusCode).toBe(400);
    });

    test('should handle insufficient balance errors', async () => {
      // Arrange
      authService.getCurrentUserDatabaseId.mockResolvedValue('sender-user-123');
      validationUtils.validateUserTransferInput.mockReturnValue({ isValid: true });
      transactionService.transferMVTBetweenUsers.mockRejectedValue(new Error('Insufficient balance'));

      // Act
      const result = await transactionHandlers.handleUserTransferMVT(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });
  });

  describe('handleGetMVTWalletTransactionList', () => {
    const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
    const mockArgs = {
      address: 'test-user-123',
      isAdmin: false,
      limit: 10
    };

    test('should successfully retrieve user transaction list', async () => {
      // Arrange
      const mockTransactions = [
        global.testUtils.createMockTransaction('tx-1', 'ADMIN_MINT', 100),
        global.testUtils.createMockTransaction('tx-2', 'USER_TO_USER_TRANSFER', 50)
      ];
      
      authService.getUserIdFromEvent.mockResolvedValue('cognito-user-123');
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
      transactionService.getMVTWalletTransactionList.mockResolvedValue(mockTransactions);

      // Act
      const result = await transactionHandlers.handleGetMVTWalletTransactionList(mockEvent, mockArgs);

      // Assert
      expect(transactionService.getMVTWalletTransactionList).toHaveBeenCalledWith(
        'test-user-123',
        false,
        10
      );
      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(mockTransactions);
    });

    test('should allow admin to view all transactions', async () => {
      // Arrange
      const adminEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const adminArgs = {
        address: null,
        isAdmin: true,
        limit: 20
      };
      
      authService.getUserIdFromEvent.mockResolvedValue('admin-cognito-123');
      authService.checkAdminAuthorization.mockResolvedValue(true);
      transactionService.getMVTWalletTransactionList.mockResolvedValue([]);

      // Act
      const result = await transactionHandlers.handleGetMVTWalletTransactionList(adminEvent, adminArgs);

      // Assert
      expect(transactionService.getMVTWalletTransactionList).toHaveBeenCalledWith(
        null,
        true,
        20
      );
      expect(result.statusCode).toBe(200);
    });

    test('should reject non-admin trying to access admin view', async () => {
      // Arrange
      const userArgs = {
        address: null,
        isAdmin: true,
        limit: 10
      };
      
      authService.getUserIdFromEvent.mockResolvedValue('user-cognito-123');
      authService.checkAdminAuthorization.mockResolvedValue(false);

      // Act
      const result = await transactionHandlers.handleGetMVTWalletTransactionList(mockEvent, userArgs);

      // Assert
      expect(result.statusCode).toBe(403);
      expect(transactionService.getMVTWalletTransactionList).not.toHaveBeenCalled();
    });

    test('should reject user accessing other user transactions', async () => {
      // Arrange
      const unauthorizedArgs = {
        address: 'other-user-123',
        isAdmin: false,
        limit: 10
      };
      
      authService.getUserIdFromEvent.mockResolvedValue('user-cognito-123');
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');

      // Act
      const result = await transactionHandlers.handleGetMVTWalletTransactionList(mockEvent, unauthorizedArgs);

      // Assert
      expect(result.statusCode).toBe(403);
      expect(result.message).toContain('You can only view your own transactions');
    });
  });

  describe('handleGetMVTWalletTransactionById', () => {
    const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
    const mockArgs = { id: 'test-tx-123' };

    test('should successfully retrieve transaction by ID', async () => {
      // Arrange
      const mockTransaction = global.testUtils.createMockTransaction('test-tx-123', 'ADMIN_MINT', 100);
      transactionService.getMVTWalletTransactionById.mockResolvedValue(mockTransaction);

      // Act
      const result = await transactionHandlers.handleGetMVTWalletTransactionById(mockEvent, mockArgs);

      // Assert
      expect(transactionService.getMVTWalletTransactionById).toHaveBeenCalledWith('test-tx-123');
      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(mockTransaction);
    });

    test('should handle transaction not found', async () => {
      // Arrange
      transactionService.getMVTWalletTransactionById.mockResolvedValue(null);

      // Act
      const result = await transactionHandlers.handleGetMVTWalletTransactionById(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(404);
      expect(result.message).toContain('Transaction not found');
    });

    test('should handle service errors', async () => {
      // Arrange
      transactionService.getMVTWalletTransactionById.mockRejectedValue(new Error('Database error'));

      // Act
      const result = await transactionHandlers.handleGetMVTWalletTransactionById(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });
  });

  describe('Security and Race Conditions', () => {
    test('should handle concurrent mint operations', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs1 = global.testUtils.createMockArgs({ amount: 1000, description: 'Mint 1' });
      const mockArgs2 = global.testUtils.createMockArgs({ amount: 2000, description: 'Mint 2' });
      
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateMintInput.mockReturnValue({ isValid: true });
      transactionService.mintMVTTokens
        .mockResolvedValueOnce(global.testUtils.createMockTransaction('mint-1', 'ADMIN_MINT', 1000))
        .mockResolvedValueOnce(global.testUtils.createMockTransaction('mint-2', 'ADMIN_MINT', 2000));

      // Act
      const [result1, result2] = await Promise.all([
        transactionHandlers.handleAdminMintMVT(mockEvent, mockArgs1),
        transactionHandlers.handleAdminMintMVT(mockEvent, mockArgs2)
      ]);

      // Assert
      expect(result1.statusCode).toBe(200);
      expect(result2.statusCode).toBe(200);
      expect(transactionService.mintMVTTokens).toHaveBeenCalledTimes(2);
    });

    test('should prevent unauthorized access to sensitive operations', async () => {
      // Arrange
      const userEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = global.testUtils.createMockArgs({ amount: 1000 });
      
      authService.checkAdminAuthorization.mockResolvedValue(false);

      // Act
      const result = await transactionHandlers.handleAdminMintMVT(userEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(403);
      expect(transactionService.mintMVTTokens).not.toHaveBeenCalled();
    });
  });
});
