const { AWS, ddb } = require('../../config/aws');
const contractService = require('../../shared/blockchain/contractService');
const { TRANSACTION_TYPES, TRANSACTION_STATUS, TOKEN_TYPES } = require('../../shared/constants');
const validationUtils = require('../../shared/utils/validationUtils');
const transactionService = require('../transaction/transaction.service');
const { createDatabaseLogger, logError, logSuccess } = require('../../shared/utils/logger');

/**
 * Get USDC liquidity pool balance with hybrid architecture fallback
 * @returns {Promise<object>} - Liquidity pool data
 */
async function getUSDCLiquidityPool() {
  try {
    // Check if blockchain connection is available
    if (!contractService.isBlockchainConnected()) {
      console.warn("Blockchain not connected, returning fallback values for hybrid architecture");
      return {
        totalReserves: 1000, // Fallback reserve amount for testing
        availableBalance: 1000,
        adminWalletAddress: contractService.getWalletAddress() || "Not connected",
        lastUpdated: new Date().toISOString(),
        status: "FALLBACK_MODE",
        note: "Using fallback values - blockchain not connected"
      };
    }

    // Get USDC balance directly from the contract (with built-in fallback)
    const contractUSDCBalance = await contractService.getContractUSDCBalance();
    const usdcBalance = parseFloat(contractUSDCBalance);

    // If we get 0 balance, it might be a contract issue, but we can still operate
    const status = usdcBalance > 0 ? "AVAILABLE" : "LOW_LIQUIDITY";
    const note = usdcBalance > 0 ? "Connected to blockchain contract" : "Contract balance is 0 or unavailable";

    return {
      totalReserves: usdcBalance, // Contract balance represents total reserves
      availableBalance: usdcBalance, // Available balance is the same as total reserves
      adminWalletAddress: contractService.getWalletAddress(),
      lastUpdated: new Date().toISOString(),
      status: status,
      note: note
    };
  } catch (error) {
    console.error("Error getting USDC liquidity pool from contract:", error);
    console.warn("Using fallback USDC liquidity data for hybrid architecture");

    // Return fallback data that allows the system to continue operating
    return {
      totalReserves: 500, // Conservative fallback amount
      availableBalance: 500,
      adminWalletAddress: contractService.getWalletAddress() || "Error getting address",
      lastUpdated: new Date().toISOString(),
      status: "ERROR_FALLBACK",
      note: `Contract error: ${error.message}. Using fallback values.`
    };
  }
}

/**
 * Deposit USDC to liquidity pool via blockchain contract
 * @param {number} amount - Amount to deposit
 * @param {string} adminUserId - Admin user ID
 * @param {string} description - Transaction description
 * @returns {Promise<object>} - Transaction data
 */
async function depositUSDCToPool(amount, adminUserId, description) {
  const logger = createDatabaseLogger({}, 'depositUSDCToPool', 'USDCLiquidityPool', { amount, adminUserId });

  try {
    logger.info({ amount, adminUserId }, `Starting USDC deposit to liquidity pool`);

    // Validate USDC amount (allows floats)
    const usdcValidation = validationUtils.validateUSDCAmount(amount);
    if (!usdcValidation.isValid) {
      throw new Error(usdcValidation.error);
    }

    if (!contractService.isBlockchainConnected()) {
      throw new Error("Blockchain connection not available. Check environment variables.");
    }

    // Get current liquidity pool data (from contract)
    const currentPool = await getUSDCLiquidityPool();

    // Deposit USDC to the contract
    const depositResult = await contractService.depositUSDCToContract(amount);

    // Create transaction record and persist to database
    const transactionId = transactionService.generateTransactionId('usdc-deposit');
    const now = new Date().toISOString();

    const transactionData = {
      id: transactionId,
      transactionType: TRANSACTION_TYPES.USDC_DEPOSIT,
      tokenType: TOKEN_TYPES.USDC, // Explicitly set token type for USDC transactions
      amount: amount,
      fromWalletId: `admin-wallet-${adminUserId}`,
      toWalletId: "usdc-liquidity-pool",
      fromUserId: adminUserId,
      toUserId: null,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: depositResult.transactionHash,
      internalTxId: transactionId,
      description: description || `Admin deposited ${amount} USDC to liquidity pool`,
      adminUserId: adminUserId,
      gasUsed: parseFloat(depositResult.gasUsed || 0),
      blockNumber: depositResult.blockNumber,
      metadata: {
        operation: "usdc_deposit",
        previousReserves: currentPool.totalReserves,
        contractAddress: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
        adminWallet: contractService.getWalletAddress(),
        blockchainTx: depositResult.transactionHash,
        approvalNeeded: depositResult.approvalNeeded || false,
        approvalTxHash: depositResult.approvalTxHash || null,
        gasUsed: depositResult.gasUsed,
        blockNumber: depositResult.blockNumber
      },
      createdAt: now,
      updatedAt: now
    };

    // ✅ CRITICAL FIX: Actually persist the transaction record to the database
    await transactionService.createMVTWalletTransaction(transactionData);

    logSuccess(logger, 'depositUSDCToPool', transactionData, {
      transactionId,
      amount,
      adminUserId,
      blockchainTx: depositResult.transactionHash
    });

    return transactionData;
  } catch (error) {
    logError(logger, error, 'depositUSDCToPool', { amount, adminUserId });
    throw new Error(error.message || "Failed to deposit USDC to liquidity pool");
  }
}

/**
 * Withdraw USDC from liquidity pool via blockchain contract
 * @param {number} amount - Amount to withdraw
 * @param {string} adminUserId - Admin user ID
 * @param {string} description - Transaction description
 * @returns {Promise<object>} - Transaction data
 */
async function withdrawUSDCFromPool(amount, adminUserId, description) {
  const logger = createDatabaseLogger({}, 'withdrawUSDCFromPool', 'USDCLiquidityPool', { amount, adminUserId });

  try {
    logger.info({ amount, adminUserId }, `Starting USDC withdrawal from liquidity pool`);

    // Validate USDC amount (allows floats)
    const usdcValidation = validationUtils.validateUSDCAmount(amount);
    if (!usdcValidation.isValid) {
      throw new Error(usdcValidation.error);
    }

    if (!contractService.isBlockchainConnected()) {
      throw new Error("Blockchain connection not available. Check environment variables.");
    }

    // Get current liquidity pool data (from contract)
    const currentPool = await getUSDCLiquidityPool();

    if (currentPool.availableBalance < amount) {
      throw new Error(`Insufficient USDC in liquidity pool. Available: ${currentPool.availableBalance}, Requested: ${amount}`);
    }

    // Get admin wallet address for withdrawal
    const adminWalletAddress = contractService.getWalletAddress();
    if (!adminWalletAddress) {
      throw new Error("Admin wallet address not available");
    }

    // Withdraw USDC from the contract using the withdraw method
    const withdrawResult = await contractService.withdrawUSDCFromContract(amount);

    // Create transaction record and persist to database
    const transactionId = transactionService.generateTransactionId('usdc-withdrawal');
    const now = new Date().toISOString();

    const transactionData = {
      id: transactionId,
      transactionType: TRANSACTION_TYPES.USDC_WITHDRAWAL,
      tokenType: TOKEN_TYPES.USDC, // Explicitly set token type for USDC transactions
      amount: amount,
      fromWalletId: "usdc-liquidity-pool",
      toWalletId: `admin-wallet-${adminUserId}`,
      fromUserId: null,
      toUserId: adminUserId,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: withdrawResult.transactionHash,
      internalTxId: transactionId,
      description: description || `Admin withdrew ${amount} USDC from liquidity pool`,
      adminUserId: adminUserId,
      gasUsed: parseFloat(withdrawResult.gasUsed || 0),
      blockNumber: withdrawResult.blockNumber,
      metadata: {
        operation: "usdc_withdrawal",
        previousReserves: currentPool.totalReserves,
        contractAddress: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
        adminWallet: adminWalletAddress,
        blockchainTx: withdrawResult.transactionHash
      },
      createdAt: now,
      updatedAt: now
    };

    // ✅ CRITICAL FIX: Actually persist the transaction record to the database
    await transactionService.createMVTWalletTransaction(transactionData);

    logSuccess(logger, 'withdrawUSDCFromPool', transactionData, {
      transactionId,
      amount,
      adminUserId,
      blockchainTx: withdrawResult.transactionHash
    });

    return transactionData;
  } catch (error) {
    logError(logger, error, 'withdrawUSDCFromPool', { amount, adminUserId });
    throw new Error(error.message || "Failed to withdraw USDC from liquidity pool");
  }
}

module.exports = {
  getUSDCLiquidityPool,
  depositUSDCToPool,
  withdrawUSDCFromPool
};
