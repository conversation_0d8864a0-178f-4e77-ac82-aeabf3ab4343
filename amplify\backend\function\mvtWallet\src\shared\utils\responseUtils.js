const { STATUS_CODES } = require('../constants');
const standardizedErrorUtils = require('./standardizedErrorUtils');

/**
 * Create a standardized success response
 * @param {any} data - Response data
 * @param {string} message - Success message
 * @param {number} statusCode - HTTP status code (default: 200)
 * @returns {object} - Standardized response object
 */
function createSuccessResponse(data, message, statusCode = STATUS_CODES.SUCCESS) {
  return {
    statusCode,
    message,
    data
  };
}

/**
 * Create a standardized error response
 * @param {string} message - Error message
 * @param {number} statusCode - HTTP status code
 * @param {any} data - Optional error data (default: null)
 * @returns {object} - Standardized error response object
 */
function createErrorResponse(message, statusCode, data = null) {
  return {
    statusCode,
    message,
    data
  };
}

/**
 * Create unauthorized response
 * @param {string} message - Custom message (optional)
 * @returns {object} - Unauthorized response
 */
function createUnauthorizedResponse(message = "Unauthorized access") {
  return createErrorResponse(message, STATUS_CODES.UNAUTHORIZED);
}

/**
 * Create forbidden response
 * @param {string} message - Custom message (optional)
 * @returns {object} - Forbidden response
 */
function createForbiddenResponse(message = "Access forbidden") {
  return createErrorResponse(message, STATUS_CODES.FORBIDDEN);
}

/**
 * Create bad request response
 * @param {string} message - Custom message (optional)
 * @returns {object} - Bad request response
 */
function createBadRequestResponse(message = "Bad request") {
  return createErrorResponse(message, STATUS_CODES.BAD_REQUEST);
}

/**
 * Create not found response
 * @param {string} message - Custom message (optional)
 * @returns {object} - Not found response
 */
function createNotFoundResponse(message = "Resource not found") {
  return createErrorResponse(message, STATUS_CODES.NOT_FOUND);
}

/**
 * Create internal server error response
 * @param {string} message - Custom message (optional)
 * @returns {object} - Internal server error response
 */
function createInternalErrorResponse(message = "Internal server error") {
  return createErrorResponse(message, STATUS_CODES.INTERNAL_ERROR);
}

/**
 * Handle service errors and return appropriate response
 * @param {Error} error - Error object
 * @param {string} defaultMessage - Default error message
 * @param {string} functionName - GraphQL function name for standardized errors (optional)
 * @returns {object} - Error response
 */
function handleServiceError(error, defaultMessage = "Service error occurred", functionName = null) {
  console.error("Service error:", error);

  // If functionName is provided, use standardized error format
  if (functionName) {
    return standardizedErrorUtils.createAutoDetectedError(functionName, error, 'service operation');
  }

  // Legacy format for backward compatibility
  if (error.message && error.message.includes("timeout")) {
    return createInternalErrorResponse("Service timeout. Please try again in a few moments.");
  } else if (error.message && error.message.includes("network")) {
    return createInternalErrorResponse("Network error. Please check your connection and try again.");
  } else if (error.message && error.message.includes("validation")) {
    return createBadRequestResponse("Invalid request. Please verify your parameters.");
  } else {
    return createInternalErrorResponse(error.message || defaultMessage);
  }
}

/**
 * Handle swap-specific errors with detailed user-friendly messages
 * Returns GraphQL-compliant SwapProcessingResponse structure
 * @param {Error} error - Error object
 * @param {string} operation - Operation being performed (approve, reject, create)
 * @param {string} functionName - GraphQL function name for standardized errors (optional)
 * @returns {object} - SwapProcessingResponse-compliant error response
 */
function handleSwapError(error, operation = "process", functionName = null) {
  console.error(`Swap ${operation} error:`, error);

  const errorMessage = error.message || "";
  let userFriendlyMessage = "";
  let statusCode = 500;

  // Determine user-friendly message and status code based on error type
  if (errorMessage.includes("Insufficient locked balance")) {
    userFriendlyMessage = `Cannot ${operation} swap: The user's tokens are not properly locked for this swap request. ` +
      "This may indicate the swap was already processed or the tokens were unlocked. " +
      "Please verify the swap request status and user's token balance.";
    statusCode = 400;
  } else if (errorMessage.includes("not pending")) {
    userFriendlyMessage = `Cannot ${operation} swap: This swap request is no longer pending. ` +
      "It may have already been processed or expired.";
    statusCode = 400;
  } else if (errorMessage.includes("expired")) {
    userFriendlyMessage = `Cannot ${operation} swap: This swap request has expired. ` +
      "Expired requests cannot be processed.";
    statusCode = 400;
  } else if (errorMessage.includes("Insufficient USDC liquidity")) {
    userFriendlyMessage = `Cannot ${operation} swap: Insufficient USDC liquidity in the pool. ` +
      "Please ensure adequate USDC reserves before processing swaps.";
    statusCode = 400;
  } else if (errorMessage.includes("not found")) {
    userFriendlyMessage = "Swap request not found";
    statusCode = 404;
  } else if (errorMessage.includes("Insufficient MVT balance")) {
    userFriendlyMessage = `Cannot ${operation} swap: User has insufficient MVT balance. ` +
      "The user may have transferred tokens after creating the swap request.";
    statusCode = 400;
  } else if (errorMessage.includes("Unauthorized") || errorMessage.includes("forbidden")) {
    userFriendlyMessage = "Unauthorized: Admin access required for swap operations";
    statusCode = 403;
  } else if (errorMessage.includes("validation") || errorMessage.includes("invalid input")) {
    userFriendlyMessage = `Invalid input for ${operation} swap operation: ${errorMessage}`;
    statusCode = 400;
  } else {
    userFriendlyMessage = `Failed to ${operation} swap request: ${errorMessage}`;
    statusCode = 500;
  }

  // Return GraphQL-compliant SwapProcessingResponse structure
  return {
    statusCode: statusCode,
    message: userFriendlyMessage,
    data: null // For error responses, data should be null
  };
}

module.exports = {
  createSuccessResponse,
  createErrorResponse,
  createUnauthorizedResponse,
  createForbiddenResponse,
  createBadRequestResponse,
  createNotFoundResponse,
  createInternalErrorResponse,
  handleServiceError,
  handleSwapError,
  // Export standardized error utilities for direct access
  standardizedErrors: standardizedErrorUtils
};
