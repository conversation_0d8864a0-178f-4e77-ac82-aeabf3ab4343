/**
 * Standardized Error Response Utilities for MVT Wallet
 * Provides GraphQL-compliant error formatting across all operations
 */

/**
 * Error categories for standardized error classification
 */
const ERROR_CATEGORIES = {
  VALIDATION: 'Validation',
  AUTHORIZATION: 'Authorization',
  DYNAMODB: 'DynamoDB',
  BL<PERSON>KCHAIN: 'Blockchain',
  NETWORK: 'Network',
  BUSINESS_LOGIC: 'BusinessLogic',
  SYSTEM: 'System',
  EXTERNAL_SERVICE: 'ExternalService'
};

/**
 * Specific error types within categories
 */
const ERROR_TYPES = {
  // Validation errors
  INVALID_INPUT: 'InvalidInput',
  MISSING_REQUIRED_FIELD: 'MissingRequiredField',
  INVALID_FORMAT: 'InvalidFormat',
  OUT_OF_RANGE: 'OutOfRange',
  
  // Authorization errors
  UNAUTHORIZED: 'Unauthorized',
  FORBIDDEN: 'Forbidden',
  INVALID_TOKEN: 'InvalidToken',
  EXPIRED_SESSION: 'ExpiredSession',
  
  // DynamoDB errors
  DYNAMODB_EXCEPTION: 'DynamoDbException',
  RESOURCE_NOT_FOUND: 'ResourceNotFound',
  CONDITIONAL_CHECK_FAILED: 'ConditionalCheckFailed',
  THROTTLING: 'Throttling',
  
  // Blockchain errors
  TRANSACTION_FAILED: 'TransactionFailed',
  INSUFFICIENT_FUNDS: 'InsufficientFunds',
  CONTRACT_ERROR: 'ContractError',
  NETWORK_ERROR: 'NetworkError',
  
  // Business logic errors
  INSUFFICIENT_BALANCE: 'InsufficientBalance',
  INVALID_OPERATION: 'InvalidOperation',
  RESOURCE_LOCKED: 'ResourceLocked',
  OPERATION_NOT_ALLOWED: 'OperationNotAllowed',
  
  // System errors
  TIMEOUT: 'Timeout',
  SERVICE_UNAVAILABLE: 'ServiceUnavailable',
  INTERNAL_ERROR: 'InternalError',
  CONFIGURATION_ERROR: 'ConfigurationError'
};

/**
 * Create a standardized GraphQL error response
 * @param {string} functionName - The GraphQL function/field name
 * @param {string} category - Error category from ERROR_CATEGORIES
 * @param {string} type - Specific error type from ERROR_TYPES
 * @param {string} message - Human-readable error description
 * @param {object} errorInfo - Additional error information (optional)
 * @param {object} locations - GraphQL location information (optional)
 * @returns {object} - Standardized GraphQL error response
 */
function createStandardizedError(functionName, category, type, message, errorInfo = null, locations = null) {
  const defaultLocations = locations || [{ line: 2, column: 3, sourceName: null }];
  
  return {
    data: null,
    errors: [
      {
        path: [functionName],
        data: null,
        errorType: `${category}:${type}`,
        errorInfo: errorInfo,
        locations: defaultLocations,
        message: message
      }
    ]
  };
}

/**
 * Create validation error response
 * @param {string} functionName - GraphQL function name
 * @param {string} message - Error message
 * @param {string} field - Field that failed validation (optional)
 * @returns {object} - Standardized error response
 */
function createValidationError(functionName, message, field = null) {
  const errorInfo = field ? { field } : null;
  return createStandardizedError(
    functionName,
    ERROR_CATEGORIES.VALIDATION,
    ERROR_TYPES.INVALID_INPUT,
    message,
    errorInfo
  );
}

/**
 * Create authorization error response
 * @param {string} functionName - GraphQL function name
 * @param {string} message - Error message
 * @param {string} requiredRole - Required role for access (optional)
 * @returns {object} - Standardized error response
 */
function createAuthorizationError(functionName, message, requiredRole = null) {
  const errorInfo = requiredRole ? { requiredRole } : null;
  return createStandardizedError(
    functionName,
    ERROR_CATEGORIES.AUTHORIZATION,
    ERROR_TYPES.FORBIDDEN,
    message,
    errorInfo
  );
}

/**
 * Create DynamoDB error response
 * @param {string} functionName - GraphQL function name
 * @param {Error} error - DynamoDB error object
 * @param {string} operation - Database operation that failed
 * @returns {object} - Standardized error response
 */
function createDynamoDBError(functionName, error, operation = 'database operation') {
  let errorType = ERROR_TYPES.DYNAMODB_EXCEPTION;
  let message = `Database error during ${operation}`;

  // Map specific DynamoDB errors
  if (error.code === 'ResourceNotFoundException') {
    errorType = ERROR_TYPES.RESOURCE_NOT_FOUND;
    message = `Resource not found during ${operation}`;
  } else if (error.code === 'ConditionalCheckFailedException') {
    errorType = ERROR_TYPES.CONDITIONAL_CHECK_FAILED;
    message = `Conditional check failed during ${operation}`;
  } else if (error.code === 'ProvisionedThroughputExceededException') {
    errorType = ERROR_TYPES.THROTTLING;
    message = `Database throttling during ${operation}. Please try again.`;
  }

  return createStandardizedError(
    functionName,
    ERROR_CATEGORIES.DYNAMODB,
    errorType,
    message,
    { operation, originalError: error.message }
  );
}

/**
 * Create business logic error response
 * @param {string} functionName - GraphQL function name
 * @param {string} message - Error message
 * @param {string} operation - Business operation that failed
 * @param {object} context - Additional context (optional)
 * @returns {object} - Standardized error response
 */
function createBusinessLogicError(functionName, message, operation, context = null) {
  let errorType = ERROR_TYPES.INVALID_OPERATION;

  // Map specific business logic errors
  if (message.includes('insufficient balance') || message.includes('Insufficient balance')) {
    errorType = ERROR_TYPES.INSUFFICIENT_BALANCE;
  } else if (message.includes('locked') || message.includes('Locked')) {
    errorType = ERROR_TYPES.RESOURCE_LOCKED;
  } else if (message.includes('not allowed') || message.includes('forbidden')) {
    errorType = ERROR_TYPES.OPERATION_NOT_ALLOWED;
  }

  return createStandardizedError(
    functionName,
    ERROR_CATEGORIES.BUSINESS_LOGIC,
    errorType,
    message,
    { operation, context }
  );
}

/**
 * Create system error response
 * @param {string} functionName - GraphQL function name
 * @param {Error} error - System error object
 * @param {string} operation - Operation that failed
 * @returns {object} - Standardized error response
 */
function createSystemError(functionName, error, operation = 'system operation') {
  let errorType = ERROR_TYPES.INTERNAL_ERROR;
  let message = `System error during ${operation}`;

  // Map specific system errors
  if (error.message && error.message.includes('timeout')) {
    errorType = ERROR_TYPES.TIMEOUT;
    message = `Operation timeout during ${operation}. Please try again.`;
  } else if (error.message && error.message.includes('network')) {
    errorType = ERROR_TYPES.NETWORK_ERROR;
    message = `Network error during ${operation}. Please check your connection.`;
  } else if (error.message && error.message.includes('service unavailable')) {
    errorType = ERROR_TYPES.SERVICE_UNAVAILABLE;
    message = `Service temporarily unavailable during ${operation}. Please try again later.`;
  }

  return createStandardizedError(
    functionName,
    ERROR_CATEGORIES.SYSTEM,
    errorType,
    message,
    { operation, originalError: error.message }
  );
}

/**
 * Create blockchain error response
 * @param {string} functionName - GraphQL function name
 * @param {Error} error - Blockchain error object
 * @param {string} operation - Blockchain operation that failed
 * @returns {object} - Standardized error response
 */
function createBlockchainError(functionName, error, operation = 'blockchain operation') {
  let errorType = ERROR_TYPES.TRANSACTION_FAILED;
  let message = `Blockchain transaction failed during ${operation}`;

  // Map specific blockchain errors
  if (error.message && error.message.includes('insufficient funds')) {
    errorType = ERROR_TYPES.INSUFFICIENT_FUNDS;
    message = `Insufficient funds for ${operation}`;
  } else if (error.message && error.message.includes('contract')) {
    errorType = ERROR_TYPES.CONTRACT_ERROR;
    message = `Smart contract error during ${operation}`;
  } else if (error.message && error.message.includes('network')) {
    errorType = ERROR_TYPES.NETWORK_ERROR;
    message = `Blockchain network error during ${operation}`;
  }

  return createStandardizedError(
    functionName,
    ERROR_CATEGORIES.BLOCKCHAIN,
    errorType,
    message,
    { operation, originalError: error.message }
  );
}

/**
 * Auto-detect error type and create appropriate standardized response
 * @param {string} functionName - GraphQL function name
 * @param {Error} error - Error object
 * @param {string} operation - Operation that failed
 * @returns {object} - Standardized error response
 */
function createAutoDetectedError(functionName, error, operation = 'operation') {
  const errorMessage = error.message || error.toString();

  // DynamoDB errors
  if (error.code && (error.code.includes('DynamoDB') || error.code.includes('ResourceNotFound') || error.code.includes('ConditionalCheck'))) {
    return createDynamoDBError(functionName, error, operation);
  }

  // Business logic errors
  if (errorMessage.includes('insufficient balance') || 
      errorMessage.includes('locked') || 
      errorMessage.includes('not allowed') ||
      errorMessage.includes('invalid operation')) {
    return createBusinessLogicError(functionName, errorMessage, operation);
  }

  // Validation errors
  if (errorMessage.includes('validation') || 
      errorMessage.includes('invalid input') || 
      errorMessage.includes('required field') ||
      errorMessage.includes('missing')) {
    return createValidationError(functionName, errorMessage);
  }

  // Authorization errors
  if (errorMessage.includes('unauthorized') || 
      errorMessage.includes('forbidden') || 
      errorMessage.includes('access denied')) {
    return createAuthorizationError(functionName, errorMessage);
  }

  // Blockchain errors
  if (errorMessage.includes('blockchain') || 
      errorMessage.includes('transaction failed') || 
      errorMessage.includes('contract')) {
    return createBlockchainError(functionName, error, operation);
  }

  // Default to system error
  return createSystemError(functionName, error, operation);
}

module.exports = {
  ERROR_CATEGORIES,
  ERROR_TYPES,
  createStandardizedError,
  createValidationError,
  createAuthorizationError,
  createDynamoDBError,
  createBusinessLogicError,
  createSystemError,
  createBlockchainError,
  createAutoDetectedError
};
