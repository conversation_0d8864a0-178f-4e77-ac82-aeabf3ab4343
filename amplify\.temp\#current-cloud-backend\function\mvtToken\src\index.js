var contractService = require("./services/blockchain/contractService");
var stripeService = require("./services/stripe/stripeService");
var AWS = require("aws-sdk");

// Configure AWS SDK
AWS.config.update({
  region: process.env.REGION,
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY
});

// Initialize AWS services
var ddb = new AWS.DynamoDB();

// Add this to the environment variable check section at the top of the file
if (!process.env.MVT_USDC_CONTRACT_ADDRESS) {
  console.error("ERROR: MVT_USDC_CONTRACT_ADDRESS environment variable is not set");
}

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async function(event) {
    var fieldName = event.fieldName;
    var args = event.arguments;

  try {
    switch (fieldName) {
      case "mintMVT": {
        console.log('mintMVT: ', mintMVT);
        try {
          const { amount } = args;
          await contractService.mintMVT(amount);
          return {
            statusCode: 200,
            body: JSON.stringify({
              message: `Minted ${amount} MVT tokens`,
            }),
          };
        } catch (error) {
          console.error("Minting failed:", error);
          return {
            statusCode: 500,
            body: JSON.stringify({
              error: error?.message || "An error occurred",
            }),
          };
        }
      }
      case "getMVTBalance": {
        try {
          const { address, isAdmin } = args;
          const _address = isAdmin ? process.env.MVT_CONTRACT_ADDRESS : address;
          
          console.log("Environment variables check:");
          console.log("MVT_CONTRACT_ADDRESS:", process.env.MVT_CONTRACT_ADDRESS);
          console.log("MVT_WITHDRAW_CONTRACT_ADDRESS:", process.env.MVT_WITHDRAW_CONTRACT_ADDRESS);
          console.log("RPC_URL:", process.env.RPC_URL ? "Set" : "Not set");
          console.log("PRIVATE_KEY:", process.env.PRIVATE_KEY ? "Set" : "Not set");
          
          console.log("Get balance for address:", _address);
          const balance = await contractService.getMVTBalance(_address);
          console.log("New balance method result: ", balance);
          return {
            statusCode: 200,
            message: `The balance of MVT tokens for user ${address} is ${balance}`,
            data: { id: _address, balance },
          };
        } catch (error) {
          console.error("Getting balance with new method failed:", error);
          return {
            statusCode: 500,
            message: error?.message || "An error occurred",
            data: null,
          };
        }
      }
      case "transferMVT": {
        try {
          const { recipientAddress, amount } = args;
          await contractService.transferMVT(recipientAddress, amount);
          return {
            statusCode: 200,
            body: JSON.stringify({
              message: `Transferred ${amount} MVT tokens to ${recipientAddress}`,
            }),
          };
        } catch (error) {
          console.error("Transfer failed:", error);
          return {
            statusCode: 500,
            body: JSON.stringify({
              error: error?.message || "An error occurred",
            }),
          };
        }
      }
      case "getMVTTransactionList": {
        const { address, isAdmin, limit } = args;

        try {
          const _address = isAdmin ? process.env.MVT_CONTRACT_ADDRESS : address;

          console.log(
            `Fetching transaction list for address: ${_address}, limit: ${
              limit || "none"
            }`
          );
          const transactions = await contractService.getMVTTransactionList(_address, limit);

          console.log(`Retrieved ${transactions.length} transactions`);

          return {
            message: "Transactions fetched successfully",
            statusCode: 200,
            data: transactions,
          };
        } catch (error) {
          console.error("Error getting MVT transactions:", error);
          return {
            message: "Error fetching MVT transactions",
            statusCode: 500,
            data: [],
          };
        }
      }
      case "getUSDCTransactionList": {
        const { address, isAdmin, limit } = args;

        try {
          const _address = isAdmin
            ? process.env.USDC_CONTRACT_ADDRESS
            : address;

          console.log(
            `Fetching USDC transaction list for address: ${_address}, limit: ${
              limit || "none"
            }`
          );
          const transactions = await contractService.getUSDCTransactionList(_address, limit);

          console.log(`Retrieved ${transactions.length} USDC transactions`);

          return {
            message: "USDC transactions fetched successfully",
            statusCode: 200,
            data: transactions,
          };
        } catch (error) {
          console.error("Error getting USDC transactions:", error);
          return {
            message: "Error fetching USDC transactions",
            statusCode: 500,
            data: [],
          };
        }
      }
      case "getTokenTransactionList": {
        const { address, isAdmin, limit } = args;

        try {
          const _address = isAdmin 
            ? process.env.MVT_CONTRACT_ADDRESS 
            : address;

          console.log(
            `Fetching combined token transaction list for address: ${_address}, limit: ${
              limit || "none"
            }`
          );
          const transactions = await contractService.getTokenTransactionList(_address, limit);

          console.log(`Retrieved ${transactions.length} combined transactions`);

          return {
            message: "Combined transactions fetched successfully",
            statusCode: 200,
            data: transactions,
          };
        } catch (error) {
          console.error("Error getting combined transactions:", error);
          return {
            message: "Error fetching combined transactions",
            statusCode: 500,
            data: [],
          };
        }
      }
      case "getBlockchainConfig": {
        try {
          console.log("Fetching blockchain configuration");
          const config = await contractService.getBlockchainConfig();
          
          return {
            message: config.message,
            statusCode: config.success ? 200 : 500,
            data: config.data,
          };
        } catch (error) {
          console.error("Error fetching blockchain configuration:", error);
          return {
            message: "Error fetching blockchain configuration",
            statusCode: 500,
            data: null,
          };
        }
      }
      case "depositUSDC": {
        const amount = args.amount;

        try {
          const result = await contractService.depositUSDC(amount);
          return {
            message: result.message,
            statusCode: result.success ? 200 : 500,
            data: {
              success: result.success ? 1 : 0,
              fail: result.success ? [] : [result.error],
            },
          };
        } catch (error) {
          console.error("Error depositing USDC:", error);
          return {
            message: "Error depositing USDC",
            statusCode: 500,
            data: {
              success: 0,
              fail: [error.message],
            },
          };
        }
      }
      case "withdrawUSDC": {
        const amount = args.amount;

        try {
          const result = await contractService.withdrawUSDC(amount);
          return {
            message: result.message,
            statusCode: result.success ? 200 : 500,
            data: {
              success: result.success ? 1 : 0,
              fail: result.success ? [] : [result.error],
            },
          };
        } catch (error) {
          console.error("Error withdrawing USDC:", error);
          return {
            message: "Error withdrawing USDC",
            statusCode: 500,
            data: {
              success: 0,
              fail: [error.message],
            },
          };
        }
      }
      case "getUSDCBalance": {
        const { address, isAdmin } = args;
        const _address = isAdmin
          ? process.env.MVT_WITHDRAW_CONTRACT_ADDRESS
          : address;
        try {
          const balance = await contractService.getUSDCBalance(_address);
          return {
            message: "USDC balance fetched successfully",
            statusCode: 200,
            data: {
              id: _address,
              balance: parseFloat(balance),
            },
          };
        } catch (error) {
          console.error("Error getting USDC balance:", error);
          return {
            message: "Error fetching USDC balance",
            statusCode: 500,
            data: {
              id: _address,
              balance: 0,
            },
          };
        }
      }
      case "getExchangeRate": {
        try {
          const rate = await contractService.getExchangeRate();
          return {
            message: "Exchange rate fetched successfully",
            statusCode: 200,
            data: {
              rate: parseFloat(rate),
            },
          };
        } catch (error) {
          console.error("Error getting exchange rate:", error);
          return {
            message: "Error fetching exchange rate",
            statusCode: 500,
            data: {
              rate: 1, // Default to 1:1 exchange rate
            },
          };
        }
      }
      case "requestMVTSwap": {
        const { userWalletAddress, mvtAmount, signature } = args;

        try {
          console.log(`Starting MVT swap request for amount: ${mvtAmount}`);

          const requestId = await contractService.requestMVTSwap(
            userWalletAddress,
            mvtAmount,
            signature
          );

          console.log(`Swap request successful with ID: ${requestId}`);

          return {
            message: "MVT swap request submitted successfully",
            statusCode: 200,
            data: {
              id: requestId,
              mvtAmount: parseFloat(mvtAmount),
              status: "Pending",
              date: new Date().toISOString(),
            },
          };
        } catch (error) {
          console.error("Error requesting MVT swap:", error);

          // Create a user-friendly error message
          let userMessage = "Failed to process your MVT swap request.";

          if (
            error.message &&
            error.message.includes("Insufficient MVT balance")
          ) {
            userMessage = "You don't have enough MVT tokens for this swap.";
          } else if (
            error.message &&
            error.message.includes("Insufficient USDC")
          ) {
            userMessage =
              "The contract doesn't have enough USDC to fulfill this swap currently.";
          } else if (error.message && error.message.includes("MVT liquidity")) {
            userMessage =
              "The contract needs to have MVT tokens to calculate the exchange rate. Please contact the administrator.";
          } else if (
            error.message &&
            error.message.includes("transaction execution reverted")
          ) {
            userMessage =
              "The swap request cannot be processed at this time. Please try again later or contact support.";
          }

          return {
            message: userMessage,
            statusCode: 500,
            data: null,
            errorDetails: error.message || "Unknown error occurred",
          };
        }
      }
      case "getMVTSwapRequests": {
        try {
          const requests = await contractService.getSwapRequests();
          
          // Enhance requests with user names from DynamoDB
          for (var i = 0; i < requests.length; i++) {
            try {
              if (requests[i].user && requests[i].user.startsWith('0x')) {
                var params = {
                  TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
                  FilterExpression: "walletAddress = :addr",
                  ExpressionAttributeValues: { 
                    ":addr": { S: requests[i].user.toLowerCase() }
                  }
                };
                
                var result = await ddb.scan(params).promise();
                if (result.Items && result.Items.length > 0) {
                  var user = AWS.DynamoDB.Converter.unmarshall(result.Items[0]);
                  if (user.givenName && user.familyName) {
                    requests[i].userName = user.givenName + " " + user.familyName;
                  }
                }
              }
            } catch (error) {
              console.error("Error fetching user details for request:", error);
            }
          }
          
          return {
            message: "Swap requests fetched successfully",
            statusCode: 200,
            data: requests,
          };
        } catch (error) {
          console.error("Error fetching swap requests:", error);
          return {
            message: "Error fetching swap requests",
            statusCode: 500,
            data: [],
          };
        }
      }
      case "approveMVTSwap": {
        const requestId = args.requestId;

        try {
          const result = await contractService.approveSwap(requestId);
          return {
            message: "Swap request approved successfully",
            statusCode: 200,
            data: {
              success: 1,
              fail: [],
              txHash: result.txHash, // Include transaction hash in response
            },
          };
        } catch (error) {
          console.error("Error approving swap request:", error);
          return {
            message: error.message || "Error approving swap request",
            statusCode: 500,
            data: {
              success: 0,
              fail: [error.message || "Unknown error"],
            },
          };
        }
      }
      case "rejectMVTSwap": {
        const requestId = args.requestId;

        try {
          const result = await contractService.rejectSwap(requestId);
          return {
            message: "Swap request rejected successfully",
            statusCode: 200,
            data: {
              success: 1,
              fail: [],
              txHash: result.txHash, // Include transaction hash in response
            },
          };
        } catch (error) {
          console.error("Error rejecting swap request:", error);
          return {
            message: "Error rejecting swap request",
            statusCode: 500,
            data: {
              success: 0,
              fail: [error.message],
            },
          };
        }
      }
      case "setupContract": {
        try {
          const usdcAmount = args?.usdcAmount || 0;
          console.log(`Setting up contract with USDC amount: ${usdcAmount}`);

          const result = await contractService.setupContract(usdcAmount);

          return {
            message: result.message,
            statusCode: result.success ? 200 : 500,
            data: result,
          };
        } catch (error) {
          console.error("Error setting up contract:", error);
          return {
            message:
              "Error setting up contract: " +
              (error.message || "Unknown error"),
            statusCode: 500,
            data: { error: error.message },
          };
        }
      }
      case "batchUpdateOldUsersDetails": {
        try {
          console.log("Starting batch update of old users' blockchain details");
          
          // Step 1: Get all swap requests to find user wallet addresses
          var swapRequests = await contractService.getSwapRequests();
          var walletAddresses = [...new Set(
            swapRequests
              .filter(req => req.user && req.user.startsWith('0x'))
              .map(req => req.user.toLowerCase())
          )];
          
          if (walletAddresses.length === 0) {
            return {
              message: "No wallet addresses found in swap requests",
              statusCode: 200,
              data: { success: true, usersProcessed: 0 }
            };
          }
          
          // Step 2: Get user details by directly querying DynamoDB
          var userDetails = [];
          var userTableName = `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
          console.log(`Using User table: ${userTableName}`);
          
          // Process wallet addresses in batches to avoid timeouts
          var batchSize = 20;
          for (var i = 0; i < walletAddresses.length; i += batchSize) {
            var addressBatch = walletAddresses.slice(i, i + batchSize);
            console.log(`Processing batch ${Math.floor(i/batchSize) + 1} with ${addressBatch.length} addresses`);
            
            // For each wallet address, try to find a matching user in DynamoDB
            for (var j = 0; j < addressBatch.length; j++) {
              var address = addressBatch[j];
              
              // Query DynamoDB for users with this wallet address
              var params = {
                TableName: userTableName,
                FilterExpression: "walletAddress = :addr",
                ExpressionAttributeValues: { 
                  ":addr": { S: address }
                }
              };
              
              try {
                var result = await ddb.scan(params).promise();
                
                if (result.Items && result.Items.length > 0) {
                  // Convert DynamoDB item to regular JavaScript object
                  var user = AWS.DynamoDB.Converter.unmarshall(result.Items[0]);
                  console.log(`Found user with wallet address ${address}: ${user.givenName} ${user.familyName}`);
                  
                  // Only push users with all required fields
                  if (user.givenName && user.familyName && user.email) {
                    userDetails.push({
                      address: address,
                      firstName: user.givenName,
                      lastName: user.familyName,
                      email: user.email
                    });
                  } else {
                    console.log(`Skipping user with wallet address ${address} due to missing required details`);
                  }
                } else {
                  console.log(`No user found with wallet address ${address}`);
                }
              } catch (dbError) {
                console.error(`Error querying for wallet address ${address}:`, dbError);
                // Continue with other addresses even if one fails
              }
            }
          }
          
          if (userDetails.length === 0) {
            return {
              message: "No valid user details found for wallet addresses",
              statusCode: 200,
              data: { success: true, usersProcessed: 0 }
            };
          }
          
          console.log(`Found ${userDetails.length} users with valid details`);
          
          // Step 3: Prepare arrays for the contract function
          var addresses = userDetails.map(u => u.address);
          var firstNames = userDetails.map(u => u.firstName);
          var lastNames = userDetails.map(u => u.lastName);
          var emails = userDetails.map(u => u.email);
          
          // Step 4: Call the contract function via the contractService
          var result = await contractService.batchUpdateUserDetails(
            addresses, firstNames, lastNames, emails
          );
          
          return {
            message: `Successfully updated ${userDetails.length} users on blockchain`,
            statusCode: 200,
            data: {
              success: true,
              usersProcessed: userDetails.length,
              txHash: result.txHash
            }
          };
        } catch (error) {
          console.error("Error in batch update:", error);
          return {
            message: `Error: ${error.message || "Unknown error"}`,
            statusCode: 500,
            data: { success: false, error: error.message }
          };
        }
      }
      case "handleStripeWebhook": {
        try {
          const eventData = args.event;
          
          if (!eventData || !eventData.type) {
            return {
              statusCode: 400,
              message: "Invalid webhook event data provided",
              data: null
            };
          }
          
          console.log(`Processing Stripe webhook event: ${eventData.type}`);
          const result = await stripeService.handleWebhook(eventData);
          
          return {
            statusCode: 200,
            message: "Webhook processed successfully",
            data: result
          };
        } catch (error) {
          console.error("Error processing webhook:", error);
          return {
            statusCode: 500,
            message: error?.message || "Failed to process webhook",
            data: null
          };
        }
      }
      case "createOnrampSession": {
        try {
          const { usdcAmount, userWallet } = args;
          
          if (!usdcAmount || isNaN(parseFloat(usdcAmount)) || parseFloat(usdcAmount) <= 0) {
            return {
              statusCode: 400,
              message: "Invalid USDC amount provided",
              data: null
            };
          }
          
          if (!userWallet || !userWallet.startsWith('0x')) {
            return {
              statusCode: 400,
              message: "Invalid wallet address provided",
              data: null
            };
          }

          console.log(`Creating onramp session for ${usdcAmount} USDC to wallet ${userWallet}`);
          const session = await stripeService.createOnrampSession(usdcAmount, userWallet);
          
          return {
            statusCode: 200,
            message: "Onramp session created successfully",
            data: {
              sessionId: session.id,
              clientSecret: session.client_secret
            }
          };
        } catch (error) {
          console.error("Error creating onramp session:", error);
          return {
            statusCode: 500,
            message: error?.message || "Failed to create onramp session",
            data: null
          };
        }
      }
      default:
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: "Unsupported operation: " + fieldName,
          }),
        };
    }
  } catch (error) {
    console.error("Error in handler:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: error.message || "An error occurred",
      }),
    };
  }
};
