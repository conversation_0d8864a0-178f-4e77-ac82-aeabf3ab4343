const stripeService = require('./stripe.service');
const responseUtils = require('../../shared/utils/responseUtils');
const { createLogger, logError, logSuccess } = require('../../shared/utils/logger');

/**
 * Handle createOnrampSession request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function createOnrampSession(event, args) {
  const logger = createLogger({}, 'createOnrampSession', 'StripeOnramp', args);
  
  try {
    const { usdcAmount, userWallet, mvtAmount, exchangeRate } = args;

    // Validate inputs
    if (!usdcAmount || isNaN(parseFloat(usdcAmount)) || parseFloat(usdcAmount) <= 0) {
      return responseUtils.createBadRequestResponse("Invalid USDC amount provided");
    }

    // For MVT wallet system, userWallet parameter actually contains userId
    const userId = userWallet;
    if (!userId || userId.length < 3) {
      return responseUtils.createBadRequestResponse("Invalid user ID provided");
    }

    // Validate MVT amount and exchange rate if provided
    if (mvtAmount && (isNaN(parseFloat(mvtAmount)) || parseFloat(mvtAmount) <= 0)) {
      return responseUtils.createBadRequestResponse("Invalid MVT amount provided");
    }

    if (exchangeRate && (isNaN(parseFloat(exchangeRate)) || parseFloat(exchangeRate) <= 0)) {
      return responseUtils.createBadRequestResponse("Invalid exchange rate provided");
    }

    logger.info({ usdcAmount, userId, mvtAmount, exchangeRate }, `Creating onramp session for ${usdcAmount} USDC for user ${userId}`);

    const session = await stripeService.createOnrampSession(usdcAmount, userId, mvtAmount, exchangeRate);
    
    logSuccess(logger, 'createOnrampSession', session, { usdcAmount, userId, mvtAmount, exchangeRate });
    
    return responseUtils.createSuccessResponse(
      {
        sessionId: session.id,
        clientSecret: session.client_secret,
        livemode: session.livemode
      },
      "Onramp session created successfully"
    );
  } catch (error) {
    logError(logger, error, 'createOnrampSession', args);
    return responseUtils.createInternalErrorResponse(
      error.message || "Failed to create onramp session"
    );
  }
}

// Webhook resolver removed - using frontend-triggered transfers instead

module.exports = {
  createOnrampSession
};
