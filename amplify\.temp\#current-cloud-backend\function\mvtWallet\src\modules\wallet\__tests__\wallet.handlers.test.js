/**
 * Wallet Handlers Test Suite
 * Tests for wallet GraphQL handlers including authentication, authorization, and response formatting
 */

// Import setup first to ensure mocks are initialized
require('../../../__tests__/setup');

// Mock AWS config
jest.mock('../../../config/aws', () => ({
  AWS: {
    DynamoDB: {
      Converter: {
        marshall: jest.fn((item) => item),
        unmarshall: jest.fn((item) => item)
      }
    }
  },
  ddb: {
    getItem: jest.fn(),
    putItem: jest.fn(),
    updateItem: jest.fn()
  },
  dynamoClient: {
    get: jest.fn(),
    put: jest.fn(),
    update: jest.fn()
  }
}));

const walletHandlers = require('../wallet.handlers');
const walletService = require('../wallet.service');
const authService = require('../../../shared/services/authService');
const responseUtils = require('../../../shared/utils/responseUtils');

// Mock dependencies
jest.mock('../wallet.service');
jest.mock('../../../shared/services/authService');
jest.mock('../../../shared/utils/responseUtils');

describe('Wallet Handlers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('handleGetAdminMVTWalletBalance', () => {
    const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
    const mockArgs = global.testUtils.createMockArgs();

    test('should successfully retrieve admin wallet balance', async () => {
      // Arrange
      const mockWalletData = global.testUtils.createMockWalletBalance(5000, 0);
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      walletService.getCentralWalletBalance.mockResolvedValue(mockWalletData);
      responseUtils.createSuccessResponse.mockReturnValue({
        statusCode: 200,
        message: 'Central wallet balance retrieved successfully',
        data: mockWalletData
      });

      // Act
      const result = await walletHandlers.handleGetAdminMVTWalletBalance(mockEvent, mockArgs);

      // Assert
      expect(authService.checkAdminAuthorization).toHaveBeenCalledWith(mockEvent);
      expect(walletService.getCentralWalletBalance).toHaveBeenCalled();
      expect(responseUtils.createSuccessResponse).toHaveBeenCalledWith(
        { data: mockWalletData },
        'Central wallet balance retrieved successfully'
      );
      expect(result.statusCode).toBe(200);
    });

    test('should reject non-admin users', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(false);
      responseUtils.createForbiddenResponse.mockReturnValue({
        statusCode: 403,
        message: 'Unauthorized: Admin access required'
      });

      // Act
      const result = await walletHandlers.handleGetAdminMVTWalletBalance(mockEvent, mockArgs);

      // Assert
      expect(authService.checkAdminAuthorization).toHaveBeenCalledWith(mockEvent);
      expect(walletService.getCentralWalletBalance).not.toHaveBeenCalled();
      expect(result.statusCode).toBe(403);
    });

    test('should handle service errors gracefully', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      walletService.getCentralWalletBalance.mockRejectedValue(new Error('Database connection failed'));
      responseUtils.handleServiceError.mockReturnValue({
        statusCode: 500,
        message: 'Internal server error'
      });

      // Act
      const result = await walletHandlers.handleGetAdminMVTWalletBalance(mockEvent, mockArgs);

      // Assert
      expect(responseUtils.handleServiceError).toHaveBeenCalled();
      expect(result.statusCode).toBe(500);
    });

    test('should handle missing admin user ID', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue(null);
      responseUtils.createUnauthorizedResponse.mockReturnValue({
        statusCode: 401,
        message: 'Admin user not found in database'
      });

      // Act
      const result = await walletHandlers.handleGetAdminMVTWalletBalance(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(401);
    });
  });

  describe('handleGetUserMVTWalletBalance', () => {
    const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);

    test('should successfully retrieve user own balance', async () => {
      // Arrange
      const mockArgs = global.testUtils.createMockArgs(); // No userId provided
      const mockWalletData = global.testUtils.createMockWalletBalance(1000, 100);
      
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
      walletService.getUserBalance.mockResolvedValue(mockWalletData);

      // Act
      const result = await walletHandlers.handleGetUserMVTWalletBalance(mockEvent, mockArgs);

      // Assert
      expect(authService.getCurrentUserDatabaseId).toHaveBeenCalledWith(mockEvent);
      expect(walletService.getUserBalance).toHaveBeenCalledWith('test-user-123');
      expect(result.statusCode).toBe(200);
      expect(result.message).toBe('Your balance retrieved successfully');
      expect(result.data).toEqual(mockWalletData);
    });

    test('should allow admin to check other user balance', async () => {
      // Arrange
      const mockArgs = global.testUtils.createMockArgs({ userId: 'other-user-123' });
      const mockWalletData = global.testUtils.createMockWalletBalance(500, 50);
      
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      authService.checkAdminAuthorization.mockResolvedValue(true);
      walletService.getUserBalance.mockResolvedValue(mockWalletData);

      // Act
      const result = await walletHandlers.handleGetUserMVTWalletBalance(mockEvent, mockArgs);

      // Assert
      expect(walletService.getUserBalance).toHaveBeenCalledWith('other-user-123');
      expect(result.statusCode).toBe(200);
      expect(result.message).toBe('Balance retrieved successfully for user other-user-123');
    });

    test('should reject non-admin trying to check other user balance', async () => {
      // Arrange
      const mockArgs = global.testUtils.createMockArgs({ userId: 'other-user-123' });
      
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
      authService.checkAdminAuthorization.mockResolvedValue(false);

      // Act
      const result = await walletHandlers.handleGetUserMVTWalletBalance(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(403);
      expect(result.message).toContain('Only admins can check other users');
    });

    test('should handle unauthenticated requests', async () => {
      // Arrange
      const mockArgs = global.testUtils.createMockArgs();
      authService.getCurrentUserDatabaseId.mockResolvedValue(null);

      // Act
      const result = await walletHandlers.handleGetUserMVTWalletBalance(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(401);
      expect(result.message).toContain('Authentication required');
    });

    test('should handle service errors', async () => {
      // Arrange
      const mockArgs = global.testUtils.createMockArgs();
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
      walletService.getUserBalance.mockRejectedValue(new Error('User not found'));

      // Act
      const result = await walletHandlers.handleGetUserMVTWalletBalance(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });

    test('should handle database connection errors', async () => {
      // Arrange
      const mockArgs = global.testUtils.createMockArgs();
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
      walletService.getUserBalance.mockRejectedValue(new Error('ResourceNotFoundException'));

      // Act
      const result = await walletHandlers.handleGetUserMVTWalletBalance(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });
  });

  describe('Error Handling Edge Cases', () => {
    test('should handle malformed event objects', async () => {
      // Arrange
      const malformedEvent = null;
      const mockArgs = global.testUtils.createMockArgs();

      // Act & Assert
      await expect(
        walletHandlers.handleGetAdminMVTWalletBalance(malformedEvent, mockArgs)
      ).rejects.toThrow();
    });

    test('should handle missing arguments', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent();
      const malformedArgs = null;

      // Act & Assert
      await expect(
        walletHandlers.handleGetUserMVTWalletBalance(mockEvent, malformedArgs)
      ).rejects.toThrow();
    });

    test('should handle timeout errors', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent();
      const mockArgs = global.testUtils.createMockArgs();
      
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
      walletService.getUserBalance.mockRejectedValue(new Error('timeout'));

      // Act
      const result = await walletHandlers.handleGetUserMVTWalletBalance(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });
  });
});
