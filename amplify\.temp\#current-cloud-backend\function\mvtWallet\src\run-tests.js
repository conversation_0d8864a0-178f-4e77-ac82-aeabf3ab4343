#!/usr/bin/env node

/**
 * Test Runner Script for MVT Wallet Lambda Function
 * Comprehensive test execution with detailed reporting
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Test configuration
const testConfig = {
  testTimeout: 30000,
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  testPatterns: {
    unit: '**/__tests__/**/*.test.js',
    integration: '__tests__/integration/**/*.test.js',
    scenarios: '__tests__/scenarios/**/*.test.js',
    scenarioTypes: {
      userJourney: '__tests__/scenarios/user-journey.test.js',
      adminWorkflow: '__tests__/scenarios/admin-workflow.test.js',
      errorRecovery: '__tests__/scenarios/error-recovery.test.js',
      dataIntegrity: '__tests__/scenarios/data-integrity.test.js',
      performanceLoad: '__tests__/scenarios/performance-load.test.js',
      lockedBalance: '__tests__/scenarios/locked-balance.test.js'
    },
    integrationTypes: {
      walletTransaction: '__tests__/integration/wallet-transaction.test.js',
      exchangeRateUsdc: '__tests__/integration/exchange-rate-usdc.test.js',
      swapWallet: '__tests__/integration/swap-wallet.test.js',
      authValidation: '__tests__/integration/auth-validation.test.js'
    },
    modules: {
      wallet: 'modules/wallet/__tests__/**/*.test.js',
      transaction: 'modules/transaction/__tests__/**/*.test.js',
      usdc: 'modules/usdc/__tests__/**/*.test.js',
      exchangeRate: 'modules/exchangeRate/__tests__/**/*.test.js',
      swap: 'modules/swap/__tests__/**/*.test.js'
    }
  }
};

// Utility functions
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  const border = '='.repeat(60);
  log(border, 'cyan');
  log(`  ${message}`, 'cyan');
  log(border, 'cyan');
}

function logSection(message) {
  log(`\n${'-'.repeat(40)}`, 'blue');
  log(`  ${message}`, 'blue');
  log(`${'-'.repeat(40)}`, 'blue');
}

function checkTestFiles() {
  log('\nChecking test file structure...', 'yellow');
  
  const testDirs = [
    '__tests__',
    '__tests__/integration',
    '__tests__/scenarios',
    'modules/wallet/__tests__',
    'modules/transaction/__tests__',
    'modules/usdc/__tests__',
    'modules/exchangeRate/__tests__',
    'modules/swap/__tests__'
  ];

  const missingDirs = [];
  const existingDirs = [];

  testDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      existingDirs.push(dir);
    } else {
      missingDirs.push(dir);
    }
  });

  log(`✓ Found ${existingDirs.length} test directories`, 'green');
  existingDirs.forEach(dir => log(`  - ${dir}`, 'green'));

  if (missingDirs.length > 0) {
    log(`⚠ Missing ${missingDirs.length} test directories`, 'yellow');
    missingDirs.forEach(dir => log(`  - ${dir}`, 'yellow'));
  }

  // Count test files
  let totalTestFiles = 0;
  existingDirs.forEach(dir => {
    try {
      const files = fs.readdirSync(dir).filter(file => file.endsWith('.test.js'));
      totalTestFiles += files.length;
      if (files.length > 0) {
        log(`  ${dir}: ${files.length} test files`, 'cyan');
      }
    } catch (error) {
      // Directory might not exist or be accessible
    }
  });

  log(`\nTotal test files found: ${totalTestFiles}`, 'bright');
  return totalTestFiles > 0;
}

function runJestCommand(command, description) {
  logSection(description);
  
  try {
    log(`Running: ${command}`, 'cyan');
    const output = execSync(command, { 
      encoding: 'utf8',
      stdio: 'pipe',
      cwd: process.cwd()
    });
    
    log('✓ Tests completed successfully', 'green');
    
    // Parse and display key metrics
    const lines = output.split('\n');
    const summaryLine = lines.find(line => line.includes('Tests:'));
    const coverageLine = lines.find(line => line.includes('Coverage:'));
    
    if (summaryLine) {
      log(`Results: ${summaryLine.trim()}`, 'bright');
    }
    
    if (coverageLine) {
      log(`Coverage: ${coverageLine.trim()}`, 'bright');
    }
    
    return { success: true, output };
  } catch (error) {
    log('✗ Tests failed', 'red');
    log(error.stdout || error.message, 'red');
    return { success: false, error: error.stdout || error.message };
  }
}

function runModuleTests() {
  logSection('Running Module-Specific Tests');
  
  const modules = Object.keys(testConfig.testPatterns.modules);
  const results = {};
  
  modules.forEach(module => {
    log(`\nTesting ${module} module...`, 'yellow');
    const pattern = testConfig.testPatterns.modules[module];
    const command = `npm test -- --testPathPattern="${pattern}" --verbose`;
    
    const result = runJestCommand(command, `${module.toUpperCase()} Module Tests`);
    results[module] = result.success;
    
    if (result.success) {
      log(`✓ ${module} module tests passed`, 'green');
    } else {
      log(`✗ ${module} module tests failed`, 'red');
    }
  });
  
  return results;
}

function runIntegrationTests() {
  logSection('Running Integration Tests');
  
  const command = `npm test -- --testPathPattern="${testConfig.testPatterns.integration}" --verbose`;
  return runJestCommand(command, 'Integration Tests');
}

function runScenarioTests() {
  logSection('Running Scenario Tests');

  const command = `npm test -- --testPathPattern="${testConfig.testPatterns.scenarios}" --verbose`;
  return runJestCommand(command, 'Scenario Tests');
}

function runIndividualScenarioTests() {
  logSection('Running Individual Scenario Tests');

  const scenarios = Object.keys(testConfig.testPatterns.scenarioTypes);
  const results = {};

  scenarios.forEach(scenario => {
    log(`\nTesting ${scenario} scenario...`, 'yellow');
    const pattern = testConfig.testPatterns.scenarioTypes[scenario];
    const command = `npm test -- --testPathPattern="${pattern}" --verbose`;

    const result = runJestCommand(command, `${scenario.toUpperCase()} Scenario Tests`);
    results[scenario] = result.success;

    if (result.success) {
      log(`✓ ${scenario} scenario tests passed`, 'green');
    } else {
      log(`✗ ${scenario} scenario tests failed`, 'red');
    }
  });

  return results;
}

function runCoverageReport() {
  logSection('Generating Coverage Report');
  
  const command = 'npm run test:coverage';
  const result = runJestCommand(command, 'Coverage Analysis');
  
  if (result.success) {
    log('\n📊 Coverage report generated in ./coverage directory', 'cyan');
    log('Open ./coverage/lcov-report/index.html to view detailed coverage', 'cyan');
  }
  
  return result;
}

function runAllTests() {
  logSection('Running All Tests');
  
  const command = 'npm test -- --verbose --coverage';
  return runJestCommand(command, 'Complete Test Suite');
}

function generateTestReport(results) {
  logSection('Test Execution Summary');
  
  const totalModules = Object.keys(results.modules).length;
  const passedModules = Object.values(results.modules).filter(Boolean).length;
  
  log(`\nModule Tests: ${passedModules}/${totalModules} passed`, 
      passedModules === totalModules ? 'green' : 'red');
  
  Object.entries(results.modules).forEach(([module, passed]) => {
    log(`  ${module}: ${passed ? '✓ PASS' : '✗ FAIL'}`, passed ? 'green' : 'red');
  });
  
  log(`\nIntegration Tests: ${results.integration ? '✓ PASS' : '✗ FAIL'}`, 
      results.integration ? 'green' : 'red');
  
  log(`Scenario Tests: ${results.scenarios ? '✓ PASS' : '✗ FAIL'}`, 
      results.scenarios ? 'green' : 'red');
  
  log(`Coverage Report: ${results.coverage ? '✓ GENERATED' : '✗ FAILED'}`, 
      results.coverage ? 'green' : 'red');
  
  const overallSuccess = passedModules === totalModules && 
                        results.integration && 
                        results.scenarios;
  
  log(`\nOverall Result: ${overallSuccess ? '✓ ALL TESTS PASSED' : '✗ SOME TESTS FAILED'}`, 
      overallSuccess ? 'green' : 'red');
  
  return overallSuccess;
}

function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'all';
  
  logHeader('MVT Wallet Lambda Function Test Suite');
  
  // Check if test files exist
  if (!checkTestFiles()) {
    log('\n⚠ No test files found. Please ensure test files are created.', 'yellow');
    process.exit(1);
  }
  
  const results = {
    modules: {},
    integration: false,
    scenarios: false,
    coverage: false
  };
  
  try {
    switch (command) {
      case 'modules':
        results.modules = runModuleTests();
        break;
        
      case 'integration':
        results.integration = runIntegrationTests().success;
        break;
        
      case 'scenarios':
        results.scenarios = runScenarioTests().success;
        break;
        
      case 'coverage':
        results.coverage = runCoverageReport().success;
        break;
        
      case 'wallet':
      case 'transaction':
      case 'usdc':
      case 'exchangeRate':
      case 'swap':
        const pattern = testConfig.testPatterns.modules[command];
        const cmd = `npm test -- --testPathPattern="${pattern}" --verbose`;
        const result = runJestCommand(cmd, `${command.toUpperCase()} Module Tests`);
        results.modules[command] = result.success;
        break;

      case 'userJourney':
      case 'adminWorkflow':
      case 'errorRecovery':
      case 'dataIntegrity':
      case 'performanceLoad':
      case 'lockedBalance': {
        const scenarioPattern = testConfig.testPatterns.scenarioTypes[command];
        const scenarioCmd = `npm test -- --testPathPattern="${scenarioPattern}" --verbose`;
        const scenarioResult = runJestCommand(scenarioCmd, `${command.toUpperCase()} Scenario Tests`);
        results.scenarios = scenarioResult.success;
        break;
      }

      case 'walletTransaction':
      case 'exchangeRateUsdc':
      case 'swapWallet':
      case 'authValidation': {
        const integrationPattern = testConfig.testPatterns.integrationTypes[command];
        const integrationCmd = `npm test -- --testPathPattern="${integrationPattern}" --verbose`;
        const integrationResult = runJestCommand(integrationCmd, `${command.toUpperCase()} Integration Tests`);
        results.integration = integrationResult.success;
        break;
      }
        
      case 'all':
      default:
        // Run all test types
        results.modules = runModuleTests();
        results.integration = runIntegrationTests().success;
        results.scenarios = runScenarioTests().success;
        results.coverage = runCoverageReport().success;
        break;
    }
    
    // Generate final report
    const success = generateTestReport(results);
    process.exit(success ? 0 : 1);
    
  } catch (error) {
    log(`\n✗ Test execution failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Help text
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  logHeader('MVT Wallet Test Runner Help');
  log('\nUsage: node run-tests.js [command]', 'cyan');
  log('\nCommands:', 'yellow');
  log('  all          Run all tests (default)', 'cyan');
  log('  modules      Run all module tests', 'cyan');
  log('  integration  Run integration tests', 'cyan');
  log('  scenarios    Run scenario tests', 'cyan');
  log('  coverage     Generate coverage report', 'cyan');
  log('  wallet       Run wallet module tests only', 'cyan');
  log('  transaction  Run transaction module tests only', 'cyan');
  log('  usdc         Run USDC module tests only', 'cyan');
  log('  exchangeRate Run exchange rate module tests only', 'cyan');
  log('  swap         Run swap module tests only', 'cyan');
  log('\nScenario Tests:', 'yellow');
  log('  userJourney     Run user journey scenario tests', 'cyan');
  log('  adminWorkflow   Run admin workflow scenario tests', 'cyan');
  log('  errorRecovery   Run error recovery scenario tests', 'cyan');
  log('  dataIntegrity   Run data integrity scenario tests', 'cyan');
  log('  performanceLoad Run performance/load scenario tests', 'cyan');
  log('  lockedBalance   Run locked balance scenario tests', 'cyan');
  log('\nIntegration Tests:', 'yellow');
  log('  walletTransaction  Run wallet-transaction integration tests', 'cyan');
  log('  exchangeRateUsdc   Run exchange rate-USDC integration tests', 'cyan');
  log('  swapWallet         Run swap-wallet integration tests', 'cyan');
  log('  authValidation     Run auth-validation integration tests', 'cyan');
  log('\nExamples:', 'yellow');
  log('  node run-tests.js', 'cyan');
  log('  node run-tests.js wallet', 'cyan');
  log('  node run-tests.js userJourney', 'cyan');
  log('  node run-tests.js walletTransaction', 'cyan');
  log('  node run-tests.js coverage', 'cyan');
  process.exit(0);
}

// Run the main function
if (require.main === module) {
  main();
}

module.exports = {
  runModuleTests,
  runIntegrationTests,
  runScenarioTests,
  runCoverageReport,
  generateTestReport
};
