// HYBRID ARCHITECTURE: Swap Service
// This service handles MVT to USDC swap requests using:
// - Local MVT operations (deducting from user balance)
// - Local swap request storage in DynamoDB
// - Blockchain USDC transfers to user wallets
// - Local exchange rate calculations
const { AWS, ddb } = require('../../config/aws');
const { getTableName, tableExists } = require('../../shared/database/dynamoUtils');
const { TRANSACTION_TYPES, TRANSACTION_STATUS, SWAP_STATUS, TOKEN_TYPES } = require('../../shared/constants');
const walletService = require('../wallet/wallet.service');
const usdcService = require('../usdc/usdc.service'); // ✅ ADDED: Import USDC service for liquidity pool tracking
const exchangeRateService = require('../exchangeRate/exchangeRate.service');
const transactionService = require('../transaction/transaction.service');
const contractService = require('../../shared/blockchain/contractService');
const validationUtils = require('../../shared/utils/validationUtils');
const authService = require('../../shared/services/authService');
const { createDatabaseLogger, logError } = require('../../shared/utils/logger');

/**
 * Generate swap request ID
 * @param {string} prefix - ID prefix
 * @returns {string} - Generated ID
 */
function generateSwapRequestId(prefix = 'swap-req') {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 11);
  return `${prefix}-${timestamp}-${random}`;
}

/**
 * Create swap request
 * @param {string} userId - User ID
 * @param {number} mvtAmount - Amount of MVT to swap
 * @param {string} description - Request description
 * @returns {Promise<object>} - Swap request data
 */
async function createSwapRequest(userId, mvtAmount, description) {
  try {
    // First validate MVT amount is integer
    const mvtValidation = validationUtils.validateMVTAmount(mvtAmount);
    if (!mvtValidation.isValid) {
      throw new Error(mvtValidation.error);
    }

    // Validate swap feasibility (this checks available balance, not locked)
    const validation = await exchangeRateService.validateSwapFeasibility(mvtAmount, userId);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Lock the MVT tokens immediately upon request creation
    const logger = createDatabaseLogger({}, 'createSwapRequest', 'MVTSwapRequest', { userId, mvtAmount });

    try {
      logger.info({ mvtAmount, userId }, `Attempting to lock ${mvtAmount} MVT tokens for user during swap request creation`);
      await walletService.lockUserMVTTokens(userId, mvtAmount);
      logger.info({ mvtAmount, userId }, `Successfully locked ${mvtAmount} MVT tokens for user during swap request creation`);

      // Verify the lock was successful
      const postLockBalance = await walletService.getUserBalance(userId);
      if (postLockBalance.lockedBalance < mvtAmount) {
        throw new Error(`Lock verification failed: expected ${mvtAmount} locked, but only ${postLockBalance.lockedBalance} locked`);
      }
      logger.info({
        lockedBalance: postLockBalance.lockedBalance,
        userId
      }, `Lock verification successful: ${postLockBalance.lockedBalance} MVT tokens locked for user`);
    } catch (lockError) {
      logError(logger, lockError, 'lockMVTTokens', { userId, mvtAmount });
      throw new Error(`Failed to lock MVT tokens: ${lockError.message}`);
    }

    const swapRequestId = generateSwapRequestId();
    const now = new Date().toISOString();
    
    // Get user details for wallet address
    const userTableName = getTableName("User");
    const userParams = {
      TableName: userTableName,
      Key: {
        id: { S: userId }
      }
    };

    const userResult = await ddb.getItem(userParams).promise();
    if (!userResult.Item) {
      throw new Error("User not found");
    }

    const user = AWS.DynamoDB.Converter.unmarshall(userResult.Item);

    // Validate wallet address exists and is properly formatted
    const walletValidation = validationUtils.validateWalletAddress(user.walletAddress);
    if (!walletValidation.isValid) {
      throw new Error(walletValidation.error);
    }

    // Create swap request record
    const swapRequestData = {
      id: swapRequestId,
      userId: userId,
      userWalletAddress: user.walletAddress,
      mvtAmount: mvtAmount,
      usdcAmount: validation.conversionData.usdcAmount,
      exchangeRate: validation.conversionData.exchangeRate,
      status: SWAP_STATUS.PENDING,
      description: description || `Swap ${mvtAmount} MVT for ${validation.conversionData.usdcAmount} USDC`,
      requestedAt: now,
      createdAt: now,
      updatedAt: now,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours expiry
    };

    // Save swap request
    const swapTableName = getTableName("MVTSwapRequest");
    logger.debug({ tableName: swapTableName }, `Creating swap request in table`);
    logger.debug({ swapRequestData }, `Swap request data prepared`);

    const swapParams = {
      TableName: swapTableName,
      Item: AWS.DynamoDB.Converter.marshall(swapRequestData)
    };

    await ddb.putItem(swapParams).promise();
    logger.info({ swapRequestId }, `Successfully created swap request`);

    // Note: No transaction record created for PENDING swap requests
    // Transactions are only created when swap is APPROVED or REJECTED

    return {
      id: swapRequestId,
      userId: userId,
      mvtAmount: mvtAmount,
      usdcAmount: validation.conversionData.usdcAmount,
      exchangeRate: validation.conversionData.exchangeRate,
      status: SWAP_STATUS.PENDING,
      userWalletAddress: user.walletAddress,
      description: swapRequestData.description,
      requestedAt: now,
      expiresAt: swapRequestData.expiresAt
    };
  } catch (error) {
    const logger = createDatabaseLogger({}, 'createSwapRequest', 'MVTSwapRequest', { userId, mvtAmount });
    logError(logger, error, 'createSwapRequest', { userId, mvtAmount });
    throw new Error(error.message || "Failed to create swap request");
  }
}

/**
 * Get swap request by ID with user data populated
 * @param {string} swapRequestId - Swap request ID
 * @returns {Promise<object|null>} - Swap request data with user name
 */
async function getSwapRequestById(swapRequestId) {
  const logger = createDatabaseLogger({}, 'getSwapRequestById', 'MVTSwapRequest', { swapRequestId });

  try {
    const swapTableName = getTableName("MVTSwapRequest");

    const params = {
      TableName: swapTableName,
      Key: {
        id: { S: swapRequestId }
      }
    };

    const result = await ddb.getItem(params).promise();

    if (!result.Item) {
      return null;
    }

    const swapRequest = AWS.DynamoDB.Converter.unmarshall(result.Item);

    // Fetch user data for this specific request
    let userName = 'Unknown User';
    if (swapRequest.userId) {
      const userDataMap = await fetchUserDataBatch([swapRequest.userId]);
      const userData = userDataMap.get(swapRequest.userId);

      if (userData) {
        if (userData.givenName && userData.familyName) {
          userName = `${userData.givenName} ${userData.familyName}`;
        } else if (userData.name) {
          userName = userData.name;
        } else if (userData.givenName) {
          userName = userData.givenName;
        } else if (userData.familyName) {
          userName = userData.familyName;
        }
        logger.debug({ userId: swapRequest.userId, userName }, `User mapped to name`);
      } else {
        logger.warn({ userId: swapRequest.userId }, `No user data found for userId`);
      }
    }

    return {
      id: swapRequest.id,
      userId: swapRequest.userId,
      userName: userName,
      userWalletAddress: swapRequest.userWalletAddress,
      mvtAmount: swapRequest.mvtAmount,
      usdcAmount: swapRequest.usdcAmount,
      exchangeRate: swapRequest.exchangeRate,
      status: swapRequest.status,
      description: swapRequest.description,
      requestedAt: swapRequest.requestedAt,
      processedAt: swapRequest.processedAt,
      expiresAt: swapRequest.expiresAt,
      adminUserId: swapRequest.adminUserId,
      rejectionReason: swapRequest.rejectionReason,
      transactionHash: swapRequest.transactionHash,
      createdAt: swapRequest.createdAt,
      updatedAt: swapRequest.updatedAt
    };
  } catch (error) {
    logError(logger, error, 'getSwapRequestById', { swapRequestId });
    return null;
  }
}

/**
 * Fetch user data for multiple user IDs in batch
 * @param {Array} userIds - Array of user IDs
 * @returns {Promise<Map>} - Map of userId -> user data
 */
async function fetchUserDataBatch(userIds) {
  const logger = createDatabaseLogger({}, 'fetchUserDataBatch', 'User', { userCount: userIds?.length || 0 });
  const userDataMap = new Map();

  if (!userIds || userIds.length === 0) {
    return userDataMap;
  }

  try {
    const userTableName = getTableName("User");

    // Check if table exists
    const exists = await tableExists(ddb, userTableName);
    if (!exists) {
      logger.warn({ tableName: userTableName }, 'User table not found, returning empty user data');
      return userDataMap;
    }

    // Fetch user data in batches (DynamoDB batch get limit is 100)
    const batchSize = 100;
    for (let i = 0; i < userIds.length; i += batchSize) {
      const batch = userIds.slice(i, i + batchSize);

      const requestItems = {};
      requestItems[userTableName] = {
        Keys: batch.map(userId => ({ id: { S: userId } })),
        ProjectionExpression: 'id, givenName, familyName, #name, email',
        ExpressionAttributeNames: {
          '#name': 'name' // 'name' is a reserved keyword in DynamoDB
        }
      };

      const params = { RequestItems: requestItems };
      const result = await ddb.batchGetItem(params).promise();

      if (result.Responses && result.Responses[userTableName]) {
        result.Responses[userTableName].forEach(item => {
          const user = AWS.DynamoDB.Converter.unmarshall(item);
          userDataMap.set(user.id, {
            id: user.id,
            givenName: user.givenName || '',
            familyName: user.familyName || '',
            name: user.name || '',
            email: user.email || ''
          });
        });
      }
    }
  } catch (error) {
    logError(logger, error, 'fetchUserDataBatch', { userCount: userIds?.length || 0 });
  }

  return userDataMap;
}

/**
 * Get swap requests list with user data populated
 * @param {string} userId - User ID (optional, for user-specific requests)
 * @param {boolean} isAdmin - Whether request is from admin
 * @param {number} limit - Maximum number of requests to return
 * @param {boolean} excludeFailed - Whether to exclude failed transactions (default: true)
 * @returns {Promise<Array>} - List of swap requests with user names
 */
async function getSwapRequestsList(userId = null, isAdmin = false, limit = 50, excludeFailed = true) {
  const logger = createDatabaseLogger({}, 'getSwapRequestsList', 'MVTSwapRequest', { userId, isAdmin, limit, excludeFailed });

  try {
    let swapTableName = getTableName("MVTSwapRequest");
    logger.debug({
      tableName: swapTableName,
      apiId: process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT,
      env: process.env.ENV
    }, `Generated table name`);

    // Check if table exists first
    const tableExistsResult = await tableExists(ddb, swapTableName);
    logger.debug({ tableName: swapTableName, exists: tableExistsResult }, `Table existence check`);

    if (!tableExistsResult) {
      logger.error({ tableName: swapTableName }, `Table does not exist`);

      // Try alternative table name (in case data is in Amplify-generated table)
      const alternativeTableName = `MVTSwapRequest-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
      logger.debug({ alternativeTableName }, `Trying alternative table name`);

      const altTableExists = await tableExists(ddb, alternativeTableName);
      logger.debug({ alternativeTableName, exists: altTableExists }, `Alternative table existence check`);

      if (!altTableExists) {
        logger.error('Neither table exists. Returning empty array');
        return [];
      }

      // Use alternative table name
      swapTableName = alternativeTableName;
      logger.info({ tableName: swapTableName }, `Using alternative table`);
    }

    let params;

    if (userId && !isAdmin) {
      // User-specific requests
      logger.debug({ userId, excludeFailed }, `Building user-specific query - will only show swap requests created by this user`);

      let filterExpression = "userId = :userId";
      let expressionAttributeValues = {
        ":userId": { S: userId }
      };

      // ✅ ADDED: Filter out failed transactions if requested
      if (excludeFailed) {
        filterExpression += " AND #status <> :failedStatus";
        expressionAttributeValues[":failedStatus"] = { S: SWAP_STATUS.FAILED };
      }

      params = {
        TableName: swapTableName,
        FilterExpression: filterExpression,
        ExpressionAttributeValues: expressionAttributeValues,
        Limit: limit
      };

      // Add ExpressionAttributeNames if we're filtering by status
      if (excludeFailed) {
        params.ExpressionAttributeNames = {
          "#status": "status"
        };
      }
    } else {
      // Admin view - all requests
      logger.debug({ isAdmin, excludeFailed }, `Building admin query for all requests`);

      if (excludeFailed) {
        // ✅ ADDED: Filter out failed transactions for admin view
        params = {
          TableName: swapTableName,
          FilterExpression: "#status <> :failedStatus",
          ExpressionAttributeNames: {
            "#status": "status"
          },
          ExpressionAttributeValues: {
            ":failedStatus": { S: SWAP_STATUS.FAILED }
          },
          Limit: limit
        };
      } else {
        // Show all requests including failed ones
        params = {
          TableName: swapTableName,
          Limit: limit
        };
      }
    }

    logger.debug({ params }, `Final scan parameters`);

    let result;
    try {
      result = await ddb.scan(params).promise();
      logger.info({
        count: result.Count,
        scannedCount: result.ScannedCount,
        itemsFound: result.Items?.length || 0
      }, `Scan completed`);
    } catch (scanError) {
      logError(logger, scanError, 'scanTable', { tableName: swapTableName });

      // If ResourceNotFoundException, the table doesn't exist
      if (scanError.code === 'ResourceNotFoundException') {
        logger.error({ tableName: swapTableName }, `Table not found - possible naming issue`);
        return [];
      }

      throw scanError;
    }

    const swapRequests = result.Items.map(item => {
      const swapRequest = AWS.DynamoDB.Converter.unmarshall(item);
      logger.debug({ swapRequestId: swapRequest.id, status: swapRequest.status }, `Processing swap request item`);

      return {
        id: swapRequest.id,
        userId: swapRequest.userId,
        userWalletAddress: swapRequest.userWalletAddress,
        mvtAmount: swapRequest.mvtAmount,
        usdcAmount: swapRequest.usdcAmount,
        exchangeRate: swapRequest.exchangeRate,
        status: swapRequest.status,
        description: swapRequest.description,
        requestedAt: swapRequest.requestedAt,
        processedAt: swapRequest.processedAt,
        expiresAt: swapRequest.expiresAt,
        adminUserId: swapRequest.adminUserId,
        rejectionReason: swapRequest.rejectionReason,
        transactionHash: swapRequest.transactionHash
      };
    });

    // Sort by requestedAt descending
    swapRequests.sort((a, b) => new Date(b.requestedAt) - new Date(a.requestedAt));

    // Fetch user data for all unique user IDs
    const userIds = [...new Set(swapRequests.map(request => request.userId).filter(Boolean))];
    logger.debug({ userCount: userIds.length, userIds }, `Fetching user data for unique users`);

    const userDataMap = await fetchUserDataBatch(userIds);
    logger.debug({ retrievedUserCount: userDataMap.size }, `Retrieved user data`);

    // Enrich swap requests with user data
    const enrichedSwapRequests = swapRequests.map(request => {
      const userData = userDataMap.get(request.userId);
      let userName = 'Unknown User';

      if (userData) {
        if (userData.givenName && userData.familyName) {
          userName = `${userData.givenName} ${userData.familyName}`;
        } else if (userData.name) {
          userName = userData.name;
        } else if (userData.givenName) {
          userName = userData.givenName;
        } else if (userData.familyName) {
          userName = userData.familyName;
        }
        logger.debug({ userId: request.userId, userName }, `User mapped to name`);
      } else {
        logger.warn({ userId: request.userId }, `No user data found for userId`);
      }

      return {
        ...request,
        userName: userName
      };
    });

    // ✅ ADDED: Log filtering results
    const statusCounts = enrichedSwapRequests.reduce((counts, req) => {
      counts[req.status] = (counts[req.status] || 0) + 1;
      return counts;
    }, {});

    logger.info({
      requestCount: enrichedSwapRequests.length,
      hasUserNames: enrichedSwapRequests.some(req => req.userName !== 'Unknown User'),
      excludeFailed: excludeFailed,
      statusCounts: statusCounts,
      filteredOut: excludeFailed ? 'FAILED transactions excluded' : 'All statuses included'
    }, `Returning swap requests with user names populated`);

    return enrichedSwapRequests;
  } catch (error) {
    logError(logger, error, 'getSwapRequestsList', { userId, isAdmin, limit });
    return [];
  }
}

/**
 * Update swap request status
 * @param {string} swapRequestId - Swap request ID
 * @param {string} status - New status
 * @param {string} adminUserId - Admin user ID (optional)
 * @param {string} rejectionReason - Rejection reason (optional)
 * @param {string} transactionHash - Transaction hash (optional)
 * @returns {Promise<void>}
 */
async function updateSwapRequestStatus(swapRequestId, status, adminUserId = null, rejectionReason = null, transactionHash = null) {
  const logger = createDatabaseLogger({}, 'updateSwapRequestStatus', 'MVTSwapRequest', {
    swapRequestId,
    status,
    adminUserId
  });

  try {
    const swapTableName = getTableName("MVTSwapRequest");
    const now = new Date().toISOString();

    // First, verify the swap request exists
    const getParams = {
      TableName: swapTableName,
      Key: {
        id: { S: swapRequestId }
      }
    };

    const existingItem = await ddb.getItem(getParams).promise();
    if (!existingItem.Item) {
      throw new Error(`Swap request ${swapRequestId} not found in database`);
    }

    const currentStatus = existingItem.Item.status?.S;
    logger.info({ swapRequestId, currentStatus, newStatus: status }, `Found existing swap request`);

    let updateExpression = "SET #status = :status, #updatedAt = :updated";
    let expressionAttributeNames = {
      "#status": "status",
      "#updatedAt": "updatedAt"
    };
    let expressionAttributeValues = {
      ":status": { S: status },
      ":updated": { S: now }
    };

    if (adminUserId) {
      updateExpression += ", #adminUserId = :adminUserId";
      expressionAttributeNames["#adminUserId"] = "adminUserId";
      expressionAttributeValues[":adminUserId"] = { S: adminUserId };
    }

    if (rejectionReason) {
      updateExpression += ", #rejectionReason = :rejectionReason";
      expressionAttributeNames["#rejectionReason"] = "rejectionReason";
      expressionAttributeValues[":rejectionReason"] = { S: rejectionReason };
    }

    if (transactionHash) {
      updateExpression += ", #transactionHash = :transactionHash";
      expressionAttributeNames["#transactionHash"] = "transactionHash";
      expressionAttributeValues[":transactionHash"] = { S: transactionHash };
    }

    if (status === SWAP_STATUS.APPROVED || status === SWAP_STATUS.REJECTED || status === SWAP_STATUS.COMPLETED) {
      updateExpression += ", #processedAt = :processedAt";
      expressionAttributeNames["#processedAt"] = "processedAt";
      expressionAttributeValues[":processedAt"] = { S: now };
    }

    const updateParams = {
      TableName: swapTableName,
      Key: {
        id: { S: swapRequestId }
      },
      UpdateExpression: updateExpression,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
      // Add condition to ensure we're updating the right item
      ConditionExpression: "attribute_exists(id)"
    };

    logger.debug({
      swapRequestId,
      updateExpression,
      attributeNames: Object.keys(expressionAttributeNames),
      attributeValues: Object.keys(expressionAttributeValues),
      tableName: swapTableName
    }, `Executing DynamoDB update`);

    await ddb.updateItem(updateParams).promise();
    logger.info({ swapRequestId, status }, `Successfully updated swap request status`);

    // Verify the update was successful by reading the item back
    const verifyResult = await ddb.getItem(getParams).promise();
    if (verifyResult.Item && verifyResult.Item.status?.S === status) {
      logger.debug({ swapRequestId, status }, `Status update verified`);
    } else {
      const actualStatus = verifyResult.Item?.status?.S;
      logger.error({ swapRequestId, expectedStatus: status, actualStatus }, `Status update verification failed`);
      throw new Error(`Failed to verify status update for swap request ${swapRequestId}`);
    }

  } catch (error) {
    logError(logger, error, 'updateSwapRequestStatus', { swapRequestId, status });

    // Provide more specific error messages
    if (error.code === 'ConditionalCheckFailedException') {
      throw new Error(`Swap request ${swapRequestId} not found or has been modified`);
    } else if (error.code === 'ValidationException') {
      throw new Error(`Invalid data provided for swap request ${swapRequestId} status update`);
    } else {
      throw new Error(`Failed to update swap request ${swapRequestId}: ${error.message}`);
    }
  }
}

/**
 * Approve swap request
 * @param {string} swapRequestId - Swap request ID
 * @param {string} adminUserId - Admin user ID
 * @returns {Promise<object>} - Approval result
 */
async function approveSwapRequest(swapRequestId, adminUserId) {
  const logger = createDatabaseLogger({}, 'approveSwapRequest', 'MVTSwapRequest', {
    swapRequestId,
    adminUserId
  });

  try {
    logger.info({ swapRequestId, adminUserId }, `Starting approval process for swap request`);

    // Check blockchain connectivity before proceeding
    const isConnected = contractService.isBlockchainConnected();
    logger.info({ isConnected }, `Blockchain connectivity status`);

    if (!isConnected) {
      // Run comprehensive connection test for debugging
      try {
        const connectionTest = await contractService.testContractConnection();
        logger.error({ connectionTest }, `Detailed blockchain connection test results`);
      } catch (testError) {
        logger.error({ testError: testError.message }, `Failed to run connection test`);
      }

      logger.error({}, `Blockchain not connected - cannot proceed with USDC transfer`);
      throw new Error("Blockchain connection not available. Cannot process USDC transfer.");
    }

    // Get swap request details
    const swapRequest = await getSwapRequestById(swapRequestId);
    if (!swapRequest) {
      throw new Error(`Swap request ${swapRequestId} not found`);
    }

    if (swapRequest.status !== SWAP_STATUS.PENDING) {
      throw new Error(`Swap request ${swapRequestId} is not in pending status. Current status: ${swapRequest.status}`);
    }

    // Validate that user still has sufficient locked balance
    const userBalance = await walletService.getUserBalance(swapRequest.userId);
    if (userBalance.lockedBalance < swapRequest.mvtAmount) {
      throw new Error(`Insufficient locked MVT balance for user ${swapRequest.userId}. Required: ${swapRequest.mvtAmount}, Available: ${userBalance.lockedBalance}`);
    }

    // Validate user wallet address before transfer
    logger.info({
      userId: swapRequest.userId,
      userWalletAddress: swapRequest.userWalletAddress
    }, `Validating user wallet address before USDC transfer`);

    // Get user data to verify wallet address
    const userData = await authService.getUserById(swapRequest.userId);
    if (!userData) {
      throw new Error(`User ${swapRequest.userId} not found`);
    }

    // Validate that the user has a wallet address in their profile
    const walletValidation = validationUtils.validateWalletAddress(userData.walletAddress);
    if (!walletValidation.isValid) {
      logger.error({
        userId: swapRequest.userId,
        userWalletAddress: userData.walletAddress,
        validationError: walletValidation.error
      }, `User wallet address validation failed`);
      throw new Error(walletValidation.error);
    }

    // Ensure the wallet address in the swap request matches the user's profile
    if (swapRequest.userWalletAddress !== userData.walletAddress) {
      logger.error({
        userId: swapRequest.userId,
        swapRequestWalletAddress: swapRequest.userWalletAddress,
        userProfileWalletAddress: userData.walletAddress
      }, `Wallet address mismatch between swap request and user profile`);
      throw new Error("Wallet address in swap request does not match user profile. Please update your wallet address in your profile.");
    }

    logger.info({
      userId: swapRequest.userId,
      userWalletAddress: swapRequest.userWalletAddress
    }, `✅ User wallet address validation successful`);

    // Transfer USDC to user's wallet address
    let transferResult;
    try {
      logger.info({
        userWalletAddress: swapRequest.userWalletAddress,
        usdcAmount: swapRequest.usdcAmount
      }, `Starting USDC transfer to user`);

      transferResult = await contractService.transferUSDCToUser(
        swapRequest.userWalletAddress,
        swapRequest.usdcAmount
      );

      logger.info({
        transferResult: transferResult
      }, `Contract service returned transfer result`);

      // Validate transfer result
      if (!transferResult) {
        throw new Error("USDC transfer failed: No result returned from contract service");
      }

      if (!transferResult.transactionHash) {
        logger.error({
          transferResult: transferResult,
          resultKeys: Object.keys(transferResult || {}),
          resultType: typeof transferResult
        }, `Transfer result missing transaction hash`);
        throw new Error("USDC transfer failed: No transaction hash received");
      }

      logger.info({
        transactionHash: transferResult.transactionHash,
        userWalletAddress: swapRequest.userWalletAddress,
        usdcAmount: swapRequest.usdcAmount,
        blockNumber: transferResult.blockNumber,
        gasUsed: transferResult.gasUsed
      }, `USDC transfer completed successfully`);
    } catch (transferError) {
      logError(logger, transferError, 'transferUSDCToUser', {
        userWalletAddress: swapRequest.userWalletAddress,
        usdcAmount: swapRequest.usdcAmount,
        errorMessage: transferError.message,
        errorStack: transferError.stack
      });
      throw new Error(`Failed to transfer USDC to user: ${transferError.message}`);
    }

    // Transfer locked MVT tokens from user to central wallet (atomic operation)
    let mvtTransferResult;
    try {
      // ✅ ENHANCED: Log liquidity pool balances BEFORE MVT transfer
      const poolBalancesBefore = await Promise.all([
        usdcService.getUSDCLiquidityPool(),
        walletService.getCentralWalletBalance()
      ]);

      logger.info({
        liquidityPoolsBefore: {
          usdcPool: {
            totalReserves: poolBalancesBefore[0].totalReserves,
            availableBalance: poolBalancesBefore[0].availableBalance
          },
          mvtPool: {
            balance: poolBalancesBefore[1].balance,
            totalReceived: poolBalancesBefore[1].totalReceived
          }
        }
      }, `Liquidity pool balances BEFORE MVT transfer`);

      mvtTransferResult = await walletService.transferLockedMVTToCentral(swapRequest.userId, swapRequest.mvtAmount);

      // ✅ ENHANCED: Log liquidity pool balances AFTER MVT transfer
      const poolBalancesAfter = await Promise.all([
        usdcService.getUSDCLiquidityPool(),
        walletService.getCentralWalletBalance()
      ]);

      logger.info({
        liquidityPoolsAfter: {
          usdcPool: {
            totalReserves: poolBalancesAfter[0].totalReserves,
            availableBalance: poolBalancesAfter[0].availableBalance
          },
          mvtPool: {
            balance: poolBalancesAfter[1].balance,
            totalReceived: poolBalancesAfter[1].totalReceived
          }
        },
        changes: {
          usdcChange: poolBalancesAfter[0].availableBalance - poolBalancesBefore[0].availableBalance,
          mvtChange: poolBalancesAfter[1].balance - poolBalancesBefore[1].balance
        }
      }, `Liquidity pool balances AFTER MVT transfer`);

      logger.info({
        userId: swapRequest.userId,
        mvtAmount: swapRequest.mvtAmount,
        previousCentralBalance: mvtTransferResult.previousCentralBalance,
        newCentralBalance: mvtTransferResult.newCentralBalance
      }, `Successfully transferred locked MVT tokens to central wallet`);
    } catch (transferError) {
      logError(logger, transferError, 'transferLockedMVTToCentral', {
        userId: swapRequest.userId,
        mvtAmount: swapRequest.mvtAmount
      });

      // CRITICAL: USDC transfer succeeded but MVT transfer failed
      // We need to handle this atomicity failure properly
      logger.error({
        swapRequestId,
        userId: swapRequest.userId,
        mvtAmount: swapRequest.mvtAmount,
        usdcTransactionHash: transferResult.transactionHash,
        usdcAmount: swapRequest.usdcAmount,
        userWalletAddress: swapRequest.userWalletAddress
      }, 'CRITICAL: USDC transfer succeeded but MVT transfer failed - initiating failure handling');

      // Update swap request status to FAILED to prevent retry attempts
      try {
        await updateSwapRequestStatus(
          swapRequestId,
          SWAP_STATUS.FAILED,
          adminUserId,
          `MVT transfer failed: ${transferError.message}. USDC already transferred (Hash: ${transferResult.transactionHash})`,
          transferResult.transactionHash
        );

        logger.info({
          swapRequestId,
          newStatus: SWAP_STATUS.FAILED
        }, 'Updated swap request status to FAILED');
      } catch (statusUpdateError) {
        logger.error({
          swapRequestId,
          statusUpdateError: statusUpdateError.message
        }, 'Failed to update swap request status to FAILED');
      }

      // Create partial completion transaction records to track the state
      const now = new Date().toISOString();

      try {
        // Record the successful USDC transfer
        const usdcTransactionData = {
          id: `${swapRequestId}-usdc-partial`,
          transactionType: TRANSACTION_TYPES.SWAP_APPROVED,
          tokenType: TOKEN_TYPES.USDC,
          amount: swapRequest.usdcAmount,
          fromWalletId: "usdc-liquidity-pool",
          toWalletId: swapRequest.userWalletAddress,
          fromUserId: adminUserId,
          toUserId: swapRequest.userId,
          status: TRANSACTION_STATUS.COMPLETED,
          transactionHash: transferResult.transactionHash,
          internalTxId: `${swapRequestId}-usdc-partial`,
          description: `PARTIAL SWAP: USDC transferred but MVT transfer failed. Manual intervention required.`,
          adminUserId: adminUserId,
          gasUsed: parseFloat(transferResult.gasUsed || 0),
          blockNumber: transferResult.blockNumber,
          metadata: JSON.stringify({
            operation: "swap_partial_usdc_only",
            swapRequestId: swapRequestId,
            mvtAmount: swapRequest.mvtAmount,
            usdcAmount: swapRequest.usdcAmount,
            exchangeRate: swapRequest.exchangeRate,
            userWalletAddress: swapRequest.userWalletAddress,
            blockchainTx: transferResult.transactionHash,
            swapPart: "usdc_only",
            mvtTransferError: transferError.message,
            requiresManualIntervention: true,
            status: "PARTIAL_COMPLETION"
          }),
          createdAt: now,
          updatedAt: now
        };

        await transactionService.createMVTWalletTransaction(usdcTransactionData);
        logger.info({
          transactionId: usdcTransactionData.id,
          swapRequestId
        }, 'Created partial completion transaction record for USDC transfer');

      } catch (transactionRecordError) {
        logger.error({
          swapRequestId,
          transactionRecordError: transactionRecordError.message
        }, 'Failed to create partial completion transaction record');
      }

      // IMPORTANT: Keep MVT tokens locked to prevent double-spending
      // Do NOT unlock the tokens - they should remain locked until manual intervention
      logger.warn({
        swapRequestId,
        userId: swapRequest.userId,
        mvtAmount: swapRequest.mvtAmount,
        lockedTokensStatus: 'KEPT_LOCKED'
      }, 'MVT tokens kept locked to prevent double-spending. Manual intervention required.');

      // Throw error with detailed information for admin intervention
      const errorMessage = `CRITICAL SWAP FAILURE: USDC transferred (${transferResult.transactionHash}) but MVT transfer failed (${transferError.message}). ` +
        `Swap request ${swapRequestId} marked as FAILED. MVT tokens remain locked. Manual intervention required to either: ` +
        `1) Complete MVT transfer manually, or 2) Reverse USDC transfer and unlock MVT tokens.`;

      throw new Error(errorMessage);
    }

    // The atomic transaction was successful, so we trust the transaction result
    // However, we'll do a verification with eventual consistency tolerance
    logger.info({
      transactionResult: {
        previousCentralBalance: mvtTransferResult.previousCentralBalance,
        newCentralBalance: mvtTransferResult.newCentralBalance,
        previousUserBalance: mvtTransferResult.previousUserBalance,
        newUserBalance: mvtTransferResult.newUserBalance,
        transferAmount: mvtTransferResult.transferAmount
      }
    }, 'MVT transfer atomic transaction completed successfully');

    // Optional verification with eventual consistency tolerance
    // Note: DynamoDB eventual consistency may cause temporary discrepancies
    try {
      const updatedUserBalance = await walletService.getUserBalance(swapRequest.userId);
      const updatedCentralBalance = await walletService.getCentralWalletBalance();

      logger.info({
        transactionExpected: {
          centralBalance: mvtTransferResult.newCentralBalance,
          userBalance: mvtTransferResult.newUserBalance
        },
        actualQueried: {
          centralBalance: updatedCentralBalance.balance,
          userBalance: updatedUserBalance.balance
        },
        differences: {
          centralDiff: Math.abs(updatedCentralBalance.balance - mvtTransferResult.newCentralBalance),
          userDiff: Math.abs(updatedUserBalance.balance - mvtTransferResult.newUserBalance)
        }
      }, 'Balance verification check (eventual consistency may cause temporary differences)');

      // Use transaction results as the source of truth for the response
      // The atomic transaction guarantees consistency
      const finalUserBalance = mvtTransferResult.newUserBalance;
      const finalCentralBalance = mvtTransferResult.newCentralBalance;

      logger.info({
        centralBalanceIncrease: finalCentralBalance - mvtTransferResult.previousCentralBalance,
        userBalanceDecrease: mvtTransferResult.previousUserBalance - finalUserBalance,
        transferAmount: swapRequest.mvtAmount
      }, 'Balance verification successful - using atomic transaction results');

    } catch (verificationError) {
      // If verification fails, log it but don't fail the swap since the atomic transaction succeeded
      logger.warn({
        verificationError: verificationError.message,
        transactionResult: mvtTransferResult
      }, 'Balance verification query failed, but atomic transaction was successful');
    }

    // Update swap request status
    await updateSwapRequestStatus(
      swapRequestId,
      SWAP_STATUS.APPROVED,
      adminUserId,
      null,
      transferResult.transactionHash
    );

    // Create two separate transaction records for approved swap:
    // 1. MVT transaction (deducting MVT from user)
    // 2. USDC transaction (adding USDC to user)

    const now = new Date().toISOString();

    // 1. MVT Transaction - User loses MVT tokens
    const mvtTransactionData = {
      id: `${swapRequestId}-mvt-deducted`,
      transactionType: TRANSACTION_TYPES.SWAP_APPROVED,
      tokenType: TOKEN_TYPES.MVT,
      amount: swapRequest.mvtAmount,
      fromWalletId: `user-wallet-${swapRequest.userId}`,
      toWalletId: "central-mvt-wallet", // MVT goes to central wallet
      fromUserId: swapRequest.userId,
      toUserId: adminUserId, // ✅ FIXED: Admin receives MVT tokens in central wallet
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: transferResult.transactionHash,
      internalTxId: `${swapRequestId}-mvt`,
      description: `Swap: User sent ${swapRequest.mvtAmount} MVT to admin for USDC exchange`,
      adminUserId: adminUserId,
      gasUsed: parseFloat(transferResult.gasUsed || 0),
      blockNumber: transferResult.blockNumber,
      metadata: JSON.stringify({
        operation: "swap_mvt_deducted",
        swapRequestId: swapRequestId,
        mvtAmount: swapRequest.mvtAmount,
        usdcAmount: swapRequest.usdcAmount,
        exchangeRate: swapRequest.exchangeRate,
        userWalletAddress: swapRequest.userWalletAddress,
        blockchainTx: transferResult.transactionHash,
        swapPart: "mvt_deduction",
        fromUser: swapRequest.userId,
        toAdmin: adminUserId,
        direction: "user_to_admin"
      }),
      createdAt: now,
      updatedAt: now
    };

    // 2. USDC Transaction - User receives USDC tokens
    const usdcTransactionData = {
      id: `${swapRequestId}-usdc-received`,
      transactionType: TRANSACTION_TYPES.SWAP_APPROVED,
      tokenType: TOKEN_TYPES.USDC,
      amount: swapRequest.usdcAmount,
      fromWalletId: "usdc-liquidity-pool", // USDC comes from liquidity pool
      toWalletId: swapRequest.userWalletAddress,
      fromUserId: adminUserId, // ✅ FIXED: Admin sends USDC from liquidity pool
      toUserId: swapRequest.userId,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: transferResult.transactionHash,
      internalTxId: `${swapRequestId}-usdc`,
      description: `Swap: Admin sent ${swapRequest.usdcAmount} USDC to user for MVT exchange`,
      adminUserId: adminUserId,
      gasUsed: parseFloat(transferResult.gasUsed || 0),
      blockNumber: transferResult.blockNumber,
      metadata: JSON.stringify({
        operation: "swap_usdc_received",
        swapRequestId: swapRequestId,
        mvtAmount: swapRequest.mvtAmount,
        usdcAmount: swapRequest.usdcAmount,
        exchangeRate: swapRequest.exchangeRate,
        userWalletAddress: swapRequest.userWalletAddress,
        blockchainTx: transferResult.transactionHash,
        swapPart: "usdc_addition",
        fromAdmin: adminUserId,
        toUser: swapRequest.userId,
        direction: "admin_to_user"
      }),
      createdAt: now,
      updatedAt: now
    };

    // Create both transaction records
    try {
      await transactionService.createMVTWalletTransaction(mvtTransactionData);
      logger.info({ transactionId: mvtTransactionData.id }, `Created MVT transaction record for swap approval`);

      await transactionService.createMVTWalletTransaction(usdcTransactionData);
      logger.info({ transactionId: usdcTransactionData.id }, `Created USDC transaction record for swap approval`);
    } catch (transactionError) {
      logError(logger, transactionError, 'createTransactionRecords', {
        mvtTransactionId: mvtTransactionData.id,
        usdcTransactionId: usdcTransactionData.id
      });
      // Don't throw here - the main swap operation was successful
      logger.warn('Transaction records creation failed, but swap was completed successfully');
    }

    return {
      swapRequestId: swapRequestId,
      status: SWAP_STATUS.APPROVED,
      mvtAmount: swapRequest.mvtAmount,
      usdcAmount: swapRequest.usdcAmount,
      userWalletAddress: swapRequest.userWalletAddress,
      transactionHash: transferResult.transactionHash,
      blockNumber: transferResult.blockNumber,
      gasUsed: transferResult.gasUsed,
      newUserBalance: mvtTransferResult.newUserBalance, // Use transaction result for consistency
      approvedBy: adminUserId,
      approvedAt: new Date().toISOString()
    };
  } catch (error) {
    logError(logger, error, 'approveSwapRequest', { swapRequestId, adminUserId });
    throw new Error(error.message || "Failed to approve swap request");
  }
}

/**
 * Reject swap request
 * @param {string} swapRequestId - Swap request ID
 * @param {string} adminUserId - Admin user ID
 * @param {string} rejectionReason - Reason for rejection
 * @returns {Promise<object>} - Rejection result
 */
async function rejectSwapRequest(swapRequestId, adminUserId, rejectionReason) {
  const logger = createDatabaseLogger({}, 'rejectSwapRequest', 'MVTSwapRequest', {
    swapRequestId,
    adminUserId
  });

  try {
    logger.info({ swapRequestId, adminUserId, rejectionReason }, `Starting rejection process for swap request`);

    // Get swap request details
    const swapRequest = await getSwapRequestById(swapRequestId);
    if (!swapRequest) {
      throw new Error(`Swap request ${swapRequestId} not found`);
    }

    if (swapRequest.status !== SWAP_STATUS.PENDING) {
      throw new Error(`Swap request ${swapRequestId} is not in pending status. Current status: ${swapRequest.status}`);
    }

    // Unlock the MVT tokens (return them to available balance)
    await walletService.unlockUserMVTTokens(swapRequest.userId, swapRequest.mvtAmount);

    // Update swap request status
    await updateSwapRequestStatus(
      swapRequestId,
      SWAP_STATUS.REJECTED,
      adminUserId,
      rejectionReason
    );

    // Create rejection transaction record - single MVT transaction showing failed swap
    const transactionData = {
      id: `${swapRequestId}-rejected`,
      transactionType: TRANSACTION_TYPES.SWAP_REJECTED,
      tokenType: TOKEN_TYPES.MVT,
      amount: swapRequest.mvtAmount,
      fromWalletId: `user-wallet-${swapRequest.userId}`,
      toWalletId: `user-wallet-${swapRequest.userId}`, // No actual transfer, tokens unlocked
      fromUserId: swapRequest.userId,
      toUserId: swapRequest.userId,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: `swap-rejected-${swapRequestId}`,
      internalTxId: swapRequestId,
      description: `Swap rejected: ${swapRequest.mvtAmount} MVT tokens unlocked. Reason: ${rejectionReason}`,
      adminUserId: adminUserId,
      metadata: JSON.stringify({
        operation: "swap_rejected",
        swapRequestId: swapRequestId,
        mvtAmount: swapRequest.mvtAmount,
        usdcAmount: swapRequest.usdcAmount,
        rejectionReason: rejectionReason,
        tokensUnlocked: true
      }),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await transactionService.createMVTWalletTransaction(transactionData);

    return {
      swapRequestId: swapRequestId,
      status: SWAP_STATUS.REJECTED,
      mvtAmount: swapRequest.mvtAmount,
      usdcAmount: swapRequest.usdcAmount,
      userWalletAddress: swapRequest.userWalletAddress,
      rejectionReason: rejectionReason,
      rejectedBy: adminUserId,
      rejectedAt: new Date().toISOString(),
      tokensUnlocked: true
    };
  } catch (error) {
    logError(logger, error, 'rejectSwapRequest', { swapRequestId, adminUserId, rejectionReason });
    throw new Error(error.message || "Failed to reject swap request");
  }
}

/**
 * Recover from partial swap failure - Admin function to handle failed swaps
 * @param {string} swapRequestId - Swap request ID
 * @param {string} adminUserId - Admin user ID
 * @param {string} recoveryAction - 'COMPLETE_MVT_TRANSFER' or 'REVERSE_USDC_TRANSFER'
 * @returns {Promise<object>} - Recovery result
 */
async function recoverPartialSwap(swapRequestId, adminUserId, recoveryAction) {
  const logger = createDatabaseLogger({}, 'recoverPartialSwap', 'MVTSwapRequest', {
    swapRequestId,
    adminUserId,
    recoveryAction
  });

  try {
    logger.info({ swapRequestId, adminUserId, recoveryAction }, `Starting partial swap recovery`);

    // Get swap request details
    const swapRequest = await getSwapRequestById(swapRequestId);
    if (!swapRequest) {
      throw new Error(`Swap request ${swapRequestId} not found`);
    }

    if (swapRequest.status !== SWAP_STATUS.FAILED) {
      throw new Error(`Swap request ${swapRequestId} is not in FAILED status. Current status: ${swapRequest.status}. Only FAILED swaps can be recovered.`);
    }

    if (recoveryAction === 'COMPLETE_MVT_TRANSFER') {
      // Option 1: Complete the MVT transfer to finish the swap
      logger.info({ swapRequestId }, 'Attempting to complete MVT transfer');

      try {
        const mvtTransferResult = await walletService.transferLockedMVTToCentral(swapRequest.userId, swapRequest.mvtAmount);

        // Update swap request status to APPROVED
        await updateSwapRequestStatus(
          swapRequestId,
          SWAP_STATUS.APPROVED,
          adminUserId,
          `Recovered: MVT transfer completed manually by admin`,
          swapRequest.transactionHash
        );

        // Create the missing MVT transaction record
        const mvtTransactionData = {
          id: `${swapRequestId}-mvt-recovered`,
          transactionType: TRANSACTION_TYPES.SWAP_APPROVED,
          tokenType: TOKEN_TYPES.MVT,
          amount: swapRequest.mvtAmount,
          fromWalletId: `user-wallet-${swapRequest.userId}`,
          toWalletId: "central-mvt-wallet",
          fromUserId: swapRequest.userId,
          toUserId: adminUserId,
          status: TRANSACTION_STATUS.COMPLETED,
          transactionHash: swapRequest.transactionHash,
          internalTxId: `${swapRequestId}-mvt-recovered`,
          description: `Swap Recovery: MVT transfer completed manually by admin`,
          adminUserId: adminUserId,
          metadata: JSON.stringify({
            operation: "swap_recovery_mvt_completed",
            swapRequestId: swapRequestId,
            mvtAmount: swapRequest.mvtAmount,
            usdcAmount: swapRequest.usdcAmount,
            recoveryAction: "COMPLETE_MVT_TRANSFER",
            recoveredBy: adminUserId
          }),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        await transactionService.createMVTWalletTransaction(mvtTransactionData);

        logger.info({ swapRequestId, mvtTransferResult }, 'Successfully completed MVT transfer recovery');

        return {
          swapRequestId,
          status: SWAP_STATUS.APPROVED,
          recoveryAction: 'COMPLETE_MVT_TRANSFER',
          mvtTransferResult,
          message: 'Swap successfully recovered by completing MVT transfer',
          recoveredBy: adminUserId,
          recoveredAt: new Date().toISOString()
        };

      } catch (mvtTransferError) {
        logger.error({ swapRequestId, mvtTransferError: mvtTransferError.message }, 'Failed to complete MVT transfer during recovery');
        throw new Error(`Failed to complete MVT transfer: ${mvtTransferError.message}`);
      }

    } else if (recoveryAction === 'REVERSE_USDC_TRANSFER') {
      // Option 2: Reverse the USDC transfer and unlock MVT tokens
      logger.warn({ swapRequestId }, 'USDC reversal not implemented - this requires manual blockchain intervention');

      // For now, we'll unlock the MVT tokens and mark as rejected
      // Note: USDC reversal would need to be done manually on the blockchain

      await walletService.unlockUserMVTTokens(swapRequest.userId, swapRequest.mvtAmount);

      await updateSwapRequestStatus(
        swapRequestId,
        SWAP_STATUS.REJECTED,
        adminUserId,
        `Recovered: MVT tokens unlocked. USDC reversal requires manual blockchain intervention.`
      );

      logger.warn({
        swapRequestId,
        usdcTransactionHash: swapRequest.transactionHash,
        usdcAmount: swapRequest.usdcAmount,
        userWalletAddress: swapRequest.userWalletAddress
      }, 'MVT tokens unlocked. MANUAL ACTION REQUIRED: Reverse USDC transfer on blockchain');

      return {
        swapRequestId,
        status: SWAP_STATUS.REJECTED,
        recoveryAction: 'REVERSE_USDC_TRANSFER',
        message: 'MVT tokens unlocked. Manual USDC reversal required on blockchain.',
        manualActionRequired: {
          action: 'REVERSE_USDC_TRANSFER',
          transactionHash: swapRequest.transactionHash,
          amount: swapRequest.usdcAmount,
          userWalletAddress: swapRequest.userWalletAddress
        },
        recoveredBy: adminUserId,
        recoveredAt: new Date().toISOString()
      };

    } else {
      throw new Error(`Invalid recovery action: ${recoveryAction}. Must be 'COMPLETE_MVT_TRANSFER' or 'REVERSE_USDC_TRANSFER'`);
    }

  } catch (error) {
    logError(logger, error, 'recoverPartialSwap', { swapRequestId, adminUserId, recoveryAction });
    throw new Error(error.message || "Failed to recover partial swap");
  }
}

module.exports = {
  generateSwapRequestId,
  createSwapRequest,
  getSwapRequestById,
  fetchUserDataBatch,
  getSwapRequestsList,
  updateSwapRequestStatus,
  approveSwapRequest,
  rejectSwapRequest,
  recoverPartialSwap
};
