// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

contract WithdrawMVTToken is
    Initializable,
    OwnableUpgradeable,
    ReentrancyGuardUpgradeable,
    UUPSUpgradeable
{
    ERC20Upgradeable public mvtToken;
    ERC20Upgradeable public usdcToken;
    address public mvtTokenContractAddress;

    struct SwapRequest {
        address user;
        uint256 mvtAmount;
        uint256 usdcAmount;
        bool approved;
        bool rejected;
        uint256 timestamp;
        uint256 expiry;
    }

    mapping(uint256 => SwapRequest) public swapRequests;
    mapping(address => uint256) public userNonce;
    uint256 public swapRequestId;
    uint256 public expirationTime;

    event SwapRequested(
        uint256 requestId,
        address indexed user,
        uint256 mvtAmount,
        uint256 usdcAmount
    );
    event SwapApproved(uint256 requestId, address indexed user);
    event SwapRejected(uint256 requestId, address indexed user);
    event SwapCancelled(uint256 requestId, address indexed user);
    event WithdrawApproved(address indexed user, uint256 usdcAmount);
    event Deposit(address indexed user, uint256 usdcAmount);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(
        address _mvtToken,
        address _usdcToken
    ) public virtual initializer {
        require(
            _mvtToken != address(0) && _usdcToken != address(0),
            "Invalid token addresses"
        );

        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        mvtToken = ERC20Upgradeable(_mvtToken);
        usdcToken = ERC20Upgradeable(_usdcToken);
        mvtTokenContractAddress = _mvtToken;
        expirationTime = 1 days;
    }

    // ========== PUBLIC FUNCTIONS ==========

    function depositUSDC(uint256 amount) external virtual {
        require(amount > 0, "Amount must be greater than zero");
        require(
            usdcToken.transferFrom(msg.sender, address(this), amount),
            "USDC deposit failed"
        );
        emit Deposit(msg.sender, amount);
    }

    function withdrawUSDC(uint256 amount) external virtual {
        require(amount > 0, "Invalid amount");
        require(
            usdcToken.balanceOf(address(this)) >= amount,
            "Insufficient USDC balance"
        );
        require(
            usdcToken.transfer(msg.sender, amount),
            "USDC withdrawal failed"
        );
        emit WithdrawApproved(msg.sender, amount);
    }

    function requestSwap(uint256 mvtAmount) external virtual {
        require(mvtAmount > 0, "Amount must be greater than zero");
        require(
            mvtToken.transferFrom(msg.sender, address(this), mvtAmount),
            "MVT transfer failed"
        );

        uint256 exchangeRate = getExchangeRate();
        uint256 usdcAmount = (mvtAmount * exchangeRate) / 1e18;
        require(usdcAmount > 0, "Exchange value too low");
        require(
            usdcToken.balanceOf(address(this)) >= usdcAmount,
            "Insufficient USDC in contract"
        );

        swapRequests[swapRequestId] = SwapRequest({
            user: msg.sender,
            mvtAmount: mvtAmount,
            usdcAmount: usdcAmount,
            approved: false,
            rejected: false,
            timestamp: block.timestamp,
            expiry: block.timestamp + expirationTime
        });

        emit SwapRequested(swapRequestId, msg.sender, mvtAmount, usdcAmount);
        swapRequestId++;
        userNonce[msg.sender]++;
    }

    function cancelSwap(uint256 requestId) external virtual nonReentrant {
        SwapRequest storage request = swapRequests[requestId];
        require(request.user == msg.sender, "Not your request");
        require(
            !request.approved && !request.rejected,
            "Request already processed"
        );
        require(
            block.timestamp > request.expiry,
            "Swap request not yet expired"
        );

        request.rejected = true;
        require(
            mvtToken.transfer(msg.sender, request.mvtAmount),
            "MVT refund failed"
        );

        emit SwapCancelled(requestId, msg.sender);
    }

    function getExchangeRate() public view virtual returns (uint256) {
        uint256 usdcReserve = usdcToken.balanceOf(address(this));
        uint256 mvtReserve = mvtToken.balanceOf(mvtTokenContractAddress);
        require(mvtReserve > 0, "No MVT liquidity available");
        return (usdcReserve * 1e18) / mvtReserve;
    }

    function getContractUSDCBalance() external view virtual returns (uint256) {
        return usdcToken.balanceOf(address(this));
    }

    function getContractMVTBalance() external view virtual returns (uint256) {
        return mvtToken.balanceOf(address(this));
    }

    function getMVTTokenBalance() external view virtual returns (uint256) {
        return mvtToken.balanceOf(mvtTokenContractAddress);
    }

    // ========== ADMIN-ONLY FUNCTIONS ==========

    function approveSwap(
        uint256 requestId
    ) external virtual onlyOwner nonReentrant {
        SwapRequest storage request = swapRequests[requestId];
        require(request.user != address(0), "Invalid swap request ID");
        require(
            !request.approved && !request.rejected,
            "Request already processed"
        );
        require(block.timestamp <= request.expiry, "Swap request expired");

        require(
            usdcToken.transfer(request.user, request.usdcAmount),
            "USDC transfer failed"
        );
        require(
            mvtToken.transfer(mvtTokenContractAddress, request.mvtAmount),
            "MVT return failed"
        );

        request.approved = true;
        emit SwapApproved(requestId, request.user);
    }

    function rejectSwap(
        uint256 requestId
    ) external virtual onlyOwner nonReentrant {
        SwapRequest storage request = swapRequests[requestId];
        require(request.user != address(0), "Invalid swap request ID");
        require(
            !request.approved && !request.rejected,
            "Request already processed"
        );

        require(
            mvtToken.transfer(request.user, request.mvtAmount),
            "MVT refund failed"
        );

        request.rejected = true;
        emit SwapRejected(requestId, request.user);
    }

    function approveWithdraw(
        address user,
        uint256 amount
    ) external virtual onlyOwner nonReentrant {
        require(user != address(0), "Invalid user");
        require(amount > 0, "Amount must be greater than zero");
        require(
            usdcToken.balanceOf(address(this)) >= amount,
            "Insufficient USDC"
        );
        require(usdcToken.transfer(user, amount), "Withdrawal failed");

        emit WithdrawApproved(user, amount);
    }

    function setExpirationTime(uint256 _expiration) external virtual onlyOwner {
        expirationTime = _expiration;
    }

    // ========== UUPS OVERRIDE ==========

    function _authorizeUpgrade(
        address newImplementation
    ) internal virtual override onlyOwner {}
}
