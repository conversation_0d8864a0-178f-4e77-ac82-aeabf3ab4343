const walletService = require('./wallet.service');
const { AuthorizationTypes, executeHandler, handleUserBalanceAuthorization } = require('../../shared/utils/handlerUtils');
const standardizedErrorUtils = require('../../shared/utils/standardizedErrorUtils');

/**
 * Handle getAdminMVTWalletBalance request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleGetAdminMVTWalletBalance(event, args) {
  return executeHandler(event, args, {
    authorization: { type: AuthorizationTypes.ADMIN_ONLY },
    operationName: 'getAdminMVTWalletBalance',
    successMessage: 'Central wallet balance retrieved successfully'
  }, async () => {
    const walletData = await walletService.getCentralWalletBalance();
    return { data: walletData };
  });
}

/**
 * Handle getUserMVTWalletBalance request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleGetUserMVTWalletBalance(event, args) {
  try {
    // Use specialized authorization for balance requests
    const authResult = await handleUserBalanceAuthorization(event, args);
    if (authResult.error) {
      return authResult.error;
    }

    const { targetUserId, isAdmin } = authResult.context;
    const balanceData = await walletService.getUserBalance(targetUserId);
    
    const message = isAdmin 
      ? `Balance retrieved successfully for user ${targetUserId}`
      : "Your balance retrieved successfully";

    return {
      statusCode: 200,
      message,
      data: balanceData
    };
  } catch (error) {
    console.error("Error in getUserMVTWalletBalance:", error);
    return standardizedErrorUtils.createAutoDetectedError(
      'getUserMVTWalletBalance',
      error,
      'balance retrieval'
    );
  }
}

module.exports = {
  handleGetAdminMVTWalletBalance,
  handleGetUserMVTWalletBalance
};
