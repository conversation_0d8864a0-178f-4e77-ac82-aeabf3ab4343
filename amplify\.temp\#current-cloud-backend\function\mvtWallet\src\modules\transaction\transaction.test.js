/**
 * Transaction Module Tests
 * Tests for transaction handlers, service, and validation functions
 */

const transactionHandlers = require('./transaction.handlers');
const transactionService = require('./transaction.service');
const transactionValidation = require('./transaction.validation');

describe('Transaction Module', () => {
  describe('Transaction Validation', () => {
    describe('validateMintInput', () => {
      test('should validate valid mint input', () => {
        const result = transactionValidation.validateMintInput({ 
          amount: 100, 
          description: 'Test mint' 
        });
        expect(result.isValid).toBe(true);
      });

      test('should reject non-integer amount', () => {
        const result = transactionValidation.validateMintInput({ 
          amount: 10.5, 
          description: 'Test mint' 
        });
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('integer');
      });

      test('should reject negative amount', () => {
        const result = transactionValidation.validateMintInput({ 
          amount: -10, 
          description: 'Test mint' 
        });
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('greater than 0');
      });

      test('should allow empty description', () => {
        const result = transactionValidation.validateMintInput({ 
          amount: 100 
        });
        expect(result.isValid).toBe(true);
      });
    });

    describe('validateTransferInput', () => {
      test('should validate valid transfer input', () => {
        const result = transactionValidation.validateTransferInput({ 
          userId: 'test-user-123',
          amount: 50, 
          description: 'Test transfer' 
        });
        expect(result.isValid).toBe(true);
      });

      test('should reject missing userId', () => {
        const result = transactionValidation.validateTransferInput({ 
          amount: 50, 
          description: 'Test transfer' 
        });
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('user ID is required');
      });

      test('should reject empty userId', () => {
        const result = transactionValidation.validateTransferInput({ 
          userId: '',
          amount: 50, 
          description: 'Test transfer' 
        });
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('user ID is required');
      });
    });

    describe('validateUserTransferInput', () => {
      test('should validate valid user transfer input', () => {
        const result = transactionValidation.validateUserTransferInput({ 
          recipientUserId: 'recipient-123',
          amount: 25, 
          description: 'User transfer' 
        });
        expect(result.isValid).toBe(true);
      });

      test('should reject missing recipient user ID', () => {
        const result = transactionValidation.validateUserTransferInput({ 
          amount: 25, 
          description: 'User transfer' 
        });
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('recipient user ID is required');
      });
    });

    describe('validateTransactionId', () => {
      test('should validate valid transaction ID', () => {
        const result = transactionValidation.validateTransactionId('txn-123-abc');
        expect(result.isValid).toBe(true);
      });

      test('should reject empty transaction ID', () => {
        const result = transactionValidation.validateTransactionId('');
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('transaction ID is required');
      });

      test('should reject null transaction ID', () => {
        const result = transactionValidation.validateTransactionId(null);
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('transaction ID is required');
      });
    });

    describe('validateTransactionListRequest', () => {
      test('should validate valid transaction list request', () => {
        const result = transactionValidation.validateTransactionListRequest({ 
          address: 'user-123',
          isAdmin: false,
          limit: 50 
        });
        expect(result.isValid).toBe(true);
      });

      test('should reject invalid limit', () => {
        const result = transactionValidation.validateTransactionListRequest({ 
          limit: -10 
        });
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('positive integer');
      });

      test('should reject excessive limit', () => {
        const result = transactionValidation.validateTransactionListRequest({ 
          limit: 2000 
        });
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('cannot exceed 1000');
      });

      test('should reject non-boolean isAdmin', () => {
        const result = transactionValidation.validateTransactionListRequest({ 
          isAdmin: 'true' 
        });
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('boolean value');
      });
    });
  });

  describe('Transaction Service', () => {
    test('should export required functions', () => {
      expect(typeof transactionService.generateTransactionId).toBe('function');
      expect(typeof transactionService.createMVTWalletTransaction).toBe('function');
      expect(typeof transactionService.mintMVTTokens).toBe('function');
      expect(typeof transactionService.transferMVTToUser).toBe('function');
      expect(typeof transactionService.transferMVTBetweenUsers).toBe('function');
      expect(typeof transactionService.getMVTWalletTransactionList).toBe('function');
      expect(typeof transactionService.getMVTWalletTransactionById).toBe('function');
    });

    test('should generate unique transaction IDs', () => {
      const id1 = transactionService.generateTransactionId('test');
      const id2 = transactionService.generateTransactionId('test');
      
      expect(id1).toContain('test-');
      expect(id2).toContain('test-');
      expect(id1).not.toBe(id2);
    });
  });

  describe('Transaction Handlers', () => {
    test('should export required handler functions', () => {
      expect(typeof transactionHandlers.handleAdminMintMVT).toBe('function');
      expect(typeof transactionHandlers.handleAdminTransferMVT).toBe('function');
      expect(typeof transactionHandlers.handleUserTransferMVT).toBe('function');
      expect(typeof transactionHandlers.handleGetMVTWalletTransactionList).toBe('function');
      expect(typeof transactionHandlers.handleGetMVTWalletTransactionById).toBe('function');
    });
  });
});

// Mock test runner for basic verification
if (require.main === module) {
  console.log('Running transaction module tests...');
  
  // Test validation functions
  const validationTests = [
    transactionValidation.validateMintInput({ amount: 100 }),
    transactionValidation.validateTransferInput({ userId: 'test', amount: 50 }),
    transactionValidation.validateTransactionId('test-123')
  ];
  
  const allValid = validationTests.every(test => test.isValid);
  console.log('Validation tests:', allValid ? 'PASSED' : 'FAILED');
  
  // Test service exports
  const serviceExports = [
    'generateTransactionId',
    'createMVTWalletTransaction',
    'mintMVTTokens',
    'transferMVTToUser',
    'transferMVTBetweenUsers',
    'getMVTWalletTransactionList',
    'getMVTWalletTransactionById'
  ];
  
  const allExported = serviceExports.every(fn => typeof transactionService[fn] === 'function');
  console.log('Service exports:', allExported ? 'PASSED' : 'FAILED');
  
  // Test handler exports
  const handlerExports = [
    'handleAdminMintMVT',
    'handleAdminTransferMVT',
    'handleUserTransferMVT',
    'handleGetMVTWalletTransactionList',
    'handleGetMVTWalletTransactionById'
  ];
  
  const handlersExported = handlerExports.every(fn => typeof transactionHandlers[fn] === 'function');
  console.log('Handler exports:', handlersExported ? 'PASSED' : 'FAILED');
  
  console.log('Transaction module tests completed');
}

module.exports = {
  // Export test functions for integration with test runners
  testValidation: () => transactionValidation,
  testService: () => transactionService,
  testHandlers: () => transactionHandlers
};
