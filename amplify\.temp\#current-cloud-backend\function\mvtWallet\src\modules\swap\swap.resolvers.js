const swapHandlers = require('./swap.handlers');

/**
 * GraphQL field resolver mapping for Swap operations
 * Maps GraphQL field names to their corresponding handler functions
 */
const swapResolvers = {
  // Swap request operations
  requestMVTWalletSwap: swapHandlers.handleRequestMVTWalletSwap,
  getMVTWalletSwapRequests: swapHandlers.handleGetMVTWalletSwapRequests,
  
  // Admin swap operations
  approveMVTWalletSwap: swapHandlers.handleApproveMVTWalletSwap,
  rejectMVTWalletSwap: swapHandlers.handleRejectMVTWalletSwap
};

module.exports = swapResolvers;
