/**
 * User Journey Scenario Test Suite
 * Tests for complete user journeys: Receive → Transfer → Swap
 */

// Create mock DynamoDB instance
const mockDynamoDB = {
  getItem: jest.fn(),
  putItem: jest.fn(),
  updateItem: jest.fn(),
  scan: jest.fn(),
  query: jest.fn(),
  describeTable: jest.fn(),
  batchGetItem: jest.fn(),
  batchWriteItem: jest.fn()
};

// Mock all dependencies
jest.mock('../../config/aws', () => ({
  AWS: {
    DynamoDB: {
      Converter: {
        marshall: jest.fn((item) => item),
        unmarshall: jest.fn((item) => item)
      }
    }
  },
  ddb: mockDynamoDB
}));

jest.mock('../../shared/database/dynamoUtils', () => ({
  getTableName: jest.fn((tableName) => `test-${tableName}`),
  tableExists: jest.fn().mockResolvedValue(true)
}));

jest.mock('../../shared/services/authService', () => ({
  checkAdminAuthorization: jest.fn(),
  getCurrentUserDatabaseId: jest.fn(),
  getUserIdFromEvent: jest.fn(),
  checkUserAuthorization: jest.fn()
}));

jest.mock('../../shared/utils/validationUtils', () => ({
  validateMintInput: jest.fn().mockReturnValue({ isValid: true }),
  validateTransferInput: jest.fn().mockReturnValue({ isValid: true }),
  validateUserTransferInput: jest.fn().mockReturnValue({ isValid: true }),
  validateUSDCDepositInput: jest.fn().mockReturnValue({ isValid: true }),
  validateSwapRequestInput: jest.fn().mockReturnValue({ isValid: true }),
  validateSwapApprovalInput: jest.fn().mockReturnValue({ isValid: true }),
  validateSwapRejectionInput: jest.fn().mockReturnValue({ isValid: true }),
  validateMVTAmount: jest.fn().mockReturnValue({ isValid: true }),
  validateUSDCAmount: jest.fn().mockReturnValue({ isValid: true }),
  validateUserId: jest.fn().mockReturnValue({ isValid: true }),
  validateWalletAddress: jest.fn().mockReturnValue({ isValid: true })
}));

// Create stateful mocks for wallet service
let mockUserLockedBalance = 0;
let mockUserBalance = 1000; // Default balance, can be overridden for specific tests

const mockWalletService = {
  getCentralWalletBalance: jest.fn().mockResolvedValue({
    id: 'central-mvt-wallet',
    balance: 50000,
    totalMinted: 100000,
    totalTransferred: 50000,
    lastMintedAt: '2024-01-01T00:00:00.000Z',
    createdAt: '2024-01-01T00:00:00.000Z'
  }),
  getUserBalance: jest.fn().mockImplementation((userId) => {
    console.log(`[DEBUG] getUserBalance called for userId: ${userId}, returning balance: ${mockUserBalance}`);
    return Promise.resolve({
      userId: userId,
      balance: mockUserBalance,
      pendingBalance: 0,
      lockedBalance: mockUserLockedBalance,
      availableBalance: mockUserBalance - mockUserLockedBalance,
      totalReceived: mockUserBalance,
      totalSent: 0,
      lastUpdated: '2024-01-01T00:00:00.000Z',
      recentTransactions: []
    });
  }),
  updateCentralWalletBalance: jest.fn().mockResolvedValue(true),
  updateUserBalance: jest.fn().mockResolvedValue(true),
  lockUserMVTTokens: jest.fn().mockImplementation((userId, amount) => {
    console.log(`[DEBUG] lockUserMVTTokens called for userId: ${userId}, amount: ${amount}`);
    mockUserLockedBalance = amount;
    return Promise.resolve(true);
  }),
  unlockUserMVTTokens: jest.fn().mockImplementation((userId, amount) => {
    console.log(`[DEBUG] unlockUserMVTTokens called for userId: ${userId}, amount: ${amount}`);
    mockUserLockedBalance = Math.max(0, mockUserLockedBalance - amount);
    return Promise.resolve(true);
  }),
  transferLockedMVTToCentral: jest.fn().mockResolvedValue(true)
};

jest.mock('../../modules/wallet/wallet.service', () => mockWalletService);

jest.mock('../../shared/blockchain/contractService', () => ({
  getContractUSDCBalance: jest.fn().mockResolvedValue('10000000000'), // 10,000 USDC
  getWalletAddress: jest.fn().mockReturnValue('******************************************'),
  isBlockchainConnected: jest.fn().mockReturnValue(true),
  depositUSDCToContract: jest.fn().mockResolvedValue({
    transactionHash: '0xabcdef123456',
    blockNumber: 12345,
    gasUsed: '21000',
    status: 1,
    approvalNeeded: false,
    approvalTxHash: null
  }),
  withdrawUSDCFromContract: jest.fn().mockResolvedValue({
    transactionHash: '0xfedcba654321',
    blockNumber: 12346,
    gasUsed: '25000',
    status: 1
  }),
  transferUSDCToUser: jest.fn().mockResolvedValue({
    transactionHash: '0xfedcba654321',
    blockNumber: 12346,
    gasUsed: '25000',
    status: 1
  })
}));

// Mock constants globally to ensure they're available everywhere
const mockConstants = {
  CENTRAL_WALLET_ID: 'central-mvt-wallet',
  TOKEN_TYPES: {
    MVT: 'MVT',
    USDC: 'USDC'
  },
  TRANSACTION_TYPES: {
    ADMIN_MINT: 'ADMIN_MINT',
    CENTRAL_TO_USER_TRANSFER: 'CENTRAL_TO_USER_TRANSFER',
    USER_TO_USER_TRANSFER: 'USER_TO_USER_TRANSFER',
    USDC_DEPOSIT: 'USDC_DEPOSIT',
    USDC_WITHDRAWAL: 'USDC_WITHDRAWAL'
  },
  TRANSACTION_STATUS: {
    COMPLETED: 'COMPLETED',
    PENDING: 'PENDING',
    FAILED: 'FAILED'
  },
  SWAP_STATUS: {
    PENDING: 'PENDING',
    APPROVED: 'APPROVED',
    REJECTED: 'REJECTED'
  },
  STATUS_CODES: {
    SUCCESS: 200,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_ERROR: 500
  }
};

// Make constants available globally
global.TOKEN_TYPES = mockConstants.TOKEN_TYPES;
global.TRANSACTION_TYPES = mockConstants.TRANSACTION_TYPES;
global.TRANSACTION_STATUS = mockConstants.TRANSACTION_STATUS;
global.SWAP_STATUS = mockConstants.SWAP_STATUS;
global.STATUS_CODES = mockConstants.STATUS_CODES;
global.CENTRAL_WALLET_ID = mockConstants.CENTRAL_WALLET_ID;

jest.mock('../../shared/constants/index', () => mockConstants);

// Import modules after mocks are set up
const walletHandlers = require('../../modules/wallet/wallet.handlers');
const transactionHandlers = require('../../modules/transaction/transaction.handlers');
const usdcHandlers = require('../../modules/usdc/usdc.handlers');
const exchangeRateHandlers = require('../../modules/exchangeRate/exchangeRate.handlers');
const swapHandlers = require('../../modules/swap/swap.handlers');
const authService = require('../../shared/services/authService');

// Set up global test utilities
global.testUtils = {
  // Mock GraphQL event
  createMockEvent: (cognitoIdentityId = 'test-cognito-id', isAdmin = false) => ({
    requestContext: {
      identity: {
        cognitoIdentityId
      }
    },
    arguments: {},
    info: {
      fieldName: 'testField'
    },
    source: {},
    stateValues: {},
    prev: null
  }),

  // Mock GraphQL arguments
  createMockArgs: (input = {}) => ({
    input
  }),

  // Mock user data
  createMockUser: (userId = 'test-user-123', isAdmin = false) => ({
    id: userId,
    cognitoId: 'test-cognito-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    walletAddress: isAdmin ? null : '******************************************',
    role: isAdmin ? 'SUPER_ADMIN' : 'MEMBER',
    isDeleted: 'false',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }),

  // Mock swap request
  createMockSwapRequest: (id = 'test-swap-123', status = 'PENDING') => ({
    id,
    userId: 'test-user-123',
    mvtAmount: 100,
    usdcAmount: 50.0,
    exchangeRate: 0.5,
    status,
    userWalletAddress: '******************************************',
    description: 'Test swap request',
    requestedAt: '2024-01-01T00:00:00.000Z',
    processedAt: status !== 'PENDING' ? '2024-01-01T01:00:00.000Z' : null,
    adminUserId: status !== 'PENDING' ? 'admin-user-123' : null,
    transactionHash: status === 'APPROVED' ? '0xabcdef' : null,
    isDeleted: 'false',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }),

  // Mock DynamoDB responses
  mockDynamoDBSuccess: (data = {}) => ({
    promise: jest.fn().mockResolvedValue(data)
  }),

  mockDynamoDBError: (error = new Error('DynamoDB error')) => ({
    promise: jest.fn().mockRejectedValue(error)
  })
};

describe('User Journey Scenario Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Reset stateful variables
    mockUserLockedBalance = 0;
    mockUserBalance = 1000; // Reset to default balance

    // Mock environment variables for blockchain connectivity
    process.env.ETHEREUM_RPC_URL = 'http://localhost:8545';
    process.env.PRIVATE_KEY = '******************************************123456789012345678901234';
    process.env.MVT_WITHDRAW_CONTRACT_ADDRESS = '******************************************';
    process.env.USDC_TOKEN_ADDRESS = '******************************************';

    // Setup comprehensive mock data for different scenarios
    mockDynamoDB.getItem.mockImplementation((params) => {
      // Mock central wallet data (MVTTokenWallet table) - Return DynamoDB marshalled format
      if (params.Key && params.Key.id && params.Key.id.S === 'central-mvt-wallet') {
        return global.testUtils.mockDynamoDBSuccess({
          Item: {
            id: { S: 'central-mvt-wallet' },
            balance: { N: '50000' },  // DynamoDB format
            totalMinted: { N: '100000' },
            totalTransferred: { N: '50000' },
            lastMintedAt: { S: '2024-01-01T00:00:00.000Z' },
            createdAt: { S: '2024-01-01T00:00:00.000Z' },
            updatedAt: { S: '2024-01-01T00:00:00.000Z' }
          }
        });
      }
      // Mock swap request data (MVTSwapRequest table) - Return DynamoDB marshalled format
      if (params.TableName && params.TableName.includes('MVTSwapRequest') &&
          params.Key && params.Key.id && params.Key.id.S === 'swap-123') {
        return global.testUtils.mockDynamoDBSuccess({
          Item: {
            id: { S: 'swap-123' },
            userId: { S: 'test-user-123' },
            mvtAmount: { N: '100' },
            usdcAmount: { N: '50.0' },
            exchangeRate: { N: '0.5' },
            status: { S: 'PENDING' },
            userWalletAddress: { S: '******************************************' },
            description: { S: 'Test swap request' },
            createdAt: { S: '2024-01-01T00:00:00.000Z' },
            updatedAt: { S: '2024-01-01T00:00:00.000Z' }
          }
        });
      }
      // Mock user wallet balance data (UserMVTBalance table) - Return DynamoDB marshalled format
      if (params.TableName && params.TableName.includes('UserMVTBalance')) {
        return global.testUtils.mockDynamoDBSuccess({
          Item: {
            id: { S: 'test-user-123' },
            userId: { S: 'test-user-123' },
            balance: { N: '1000' },  // DynamoDB format
            pendingBalance: { N: '0' },
            lockedBalance: { N: '0' },
            totalReceived: { N: '1000' },
            totalSent: { N: '0' },
            lastUpdated: { S: '2024-01-01T00:00:00.000Z' },
            createdAt: { S: '2024-01-01T00:00:00.000Z' },
            updatedAt: { S: '2024-01-01T00:00:00.000Z' }
          }
        });
      }
      // Mock user data (User table) - Return DynamoDB marshalled format
      return global.testUtils.mockDynamoDBSuccess({
        Item: {
          id: { S: 'test-user-123' },
          cognitoId: { S: 'cognito-user-123' },
          email: { S: '<EMAIL>' },
          firstName: { S: 'Test' },
          lastName: { S: 'User' },
          walletAddress: { S: '******************************************' },
          role: { S: 'MEMBER' },
          isDeleted: { S: 'false' }
        }
      });
    });

    // Mock scan for user lookup by cognitoId (return DynamoDB marshalled format)
    mockDynamoDB.scan.mockImplementation((params) => {
      if (params.FilterExpression && params.FilterExpression.includes('cognitoId')) {
        return global.testUtils.mockDynamoDBSuccess({
          Items: [{
            id: { S: 'test-user-123' },
            cognitoId: { S: 'cognito-user-123' },
            email: { S: '<EMAIL>' },
            firstName: { S: 'Test' },
            lastName: { S: 'User' },
            walletAddress: { S: '******************************************' },
            role: { S: 'MEMBER' },
            isDeleted: { S: 'false' }
          }]
        });
      }
      return global.testUtils.mockDynamoDBSuccess({ Items: [] });
    });

    mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());
    mockDynamoDB.updateItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

    // Setup auth service defaults
    authService.checkAdminAuthorization.mockResolvedValue(true);
    authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
    authService.getUserIdFromEvent.mockResolvedValue('cognito-user-123');
    authService.checkUserAuthorization.mockResolvedValue(true);
  });

  describe('Complete User Journey: Receive → Transfer → Swap', () => {
    test('should complete full user journey successfully', async () => {
      // Setup user and admin
      const userId = 'test-user-123';
      const adminUserId = 'admin-user-123';
      const recipientUserId = 'recipient-user-456';
      const mockUser = global.testUtils.createMockUser(userId, false);

      // Mock authentication
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId
        .mockResolvedValueOnce(adminUserId) // Admin transfer
        .mockResolvedValueOnce(userId) // User transfer
        .mockResolvedValueOnce(userId); // Swap request
      authService.getUserIdFromEvent.mockResolvedValue('cognito-user-123');

      // Mock database responses for different operations
      mockDynamoDB.getItem
        // Central wallet balance for admin transfer
        .mockReturnValueOnce(global.testUtils.mockDynamoDBSuccess({
          Item: { id: 'central-mvt-wallet', mvtBalance: 10000, lockedMVT: 0 }
        }))
        // User wallet balance for admin transfer
        .mockReturnValueOnce(global.testUtils.mockDynamoDBSuccess({
          Item: { id: `user-wallet-${userId}`, mvtBalance: 0, lockedMVT: 0 }
        }))
        // User wallet balance for user transfer (after receiving tokens)
        .mockReturnValueOnce(global.testUtils.mockDynamoDBSuccess({
          Item: { id: `user-wallet-${userId}`, mvtBalance: 1000, lockedMVT: 0 }
        }))
        // Recipient wallet balance for user transfer
        .mockReturnValueOnce(global.testUtils.mockDynamoDBSuccess({
          Item: { id: `user-wallet-${recipientUserId}`, mvtBalance: 0, lockedMVT: 0 }
        }))
        // User lookup for swap request
        .mockReturnValueOnce(global.testUtils.mockDynamoDBSuccess({ Item: mockUser }))
        // Central wallet for exchange rate calculation
        .mockReturnValueOnce(global.testUtils.mockDynamoDBSuccess({
          Item: { id: 'central-mvt-wallet', mvtBalance: 20000, lockedMVT: 0 }
        }))
        // User wallet for token locking
        .mockReturnValueOnce(global.testUtils.mockDynamoDBSuccess({
          Item: { id: `user-wallet-${userId}`, mvtBalance: 800, lockedMVT: 0 }
        }));

      // Step 1: Admin transfers MVT tokens to user
      const adminTransferEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const adminTransferArgs = global.testUtils.createMockArgs({
        userId: userId,
        amount: 1000,
        description: 'Initial token allocation'
      });

      const transferResult = await transactionHandlers.handleAdminTransferMVT(adminTransferEvent, adminTransferArgs);

      expect(transferResult.statusCode).toBe(200);
      expect(transferResult.data.transactionType).toBe('CENTRAL_TO_USER_TRANSFER');
      expect(transferResult.data.amount).toBe(1000);

      // Step 2: User transfers some tokens to another user
      const userTransferEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const userTransferArgs = global.testUtils.createMockArgs({
        recipientUserId: recipientUserId,
        amount: 200,
        description: 'Payment to friend'
      });

      const userTransferResult = await transactionHandlers.handleUserTransferMVT(userTransferEvent, userTransferArgs);

      expect(userTransferResult.statusCode).toBe(200);
      expect(userTransferResult.data.transactionType).toBe('USER_TO_USER_TRANSFER');
      expect(userTransferResult.data.amount).toBe(200);

      // Step 3: User creates swap request
      const swapRequestEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const swapRequestArgs = global.testUtils.createMockArgs({
        mvtAmount: 100,
        description: 'Convert MVT to USDC'
      });

      const swapResult = await swapHandlers.handleRequestMVTWalletSwap(swapRequestEvent, swapRequestArgs);

      expect(swapResult.statusCode).toBe(200);
      expect(swapResult.data.status).toBe('PENDING');
      expect(swapResult.data.mvtAmount).toBe(100);

      // Verify all operations were logged
      expect(mockDynamoDB.putItem).toHaveBeenCalledTimes(3); // 2 transactions + 1 swap request
    });

    test('should handle user journey with insufficient balance', async () => {
      console.log('[DEBUG] Starting insufficient balance test');

      // Setup user with low balance
      const userId = 'test-user-123';
      const recipientUserId = 'recipient-user-456';
      const transferAmount = 200;
      const userLowBalance = 50; // Less than transfer amount

      console.log(`[DEBUG] Test setup - userId: ${userId}, transferAmount: ${transferAmount}, userBalance: ${userLowBalance}`);

      // Set the mock user balance to insufficient amount
      mockUserBalance = userLowBalance;
      console.log(`[DEBUG] Set mockUserBalance to: ${mockUserBalance}`);

      authService.getCurrentUserDatabaseId.mockResolvedValue(userId);

      // Mock recipient user data for validation
      mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
        Items: [{
          id: { S: recipientUserId },
          cognitoId: { S: 'recipient-cognito-123' },
          email: { S: '<EMAIL>' },
          firstName: { S: 'Recipient' },
          lastName: { S: 'User' },
          role: { S: 'MEMBER' },
          isDeleted: { S: 'false' }
        }]
      }));

      // Attempt user transfer with insufficient balance
      const userTransferEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const userTransferArgs = global.testUtils.createMockArgs({
        recipientUserId: recipientUserId,
        amount: transferAmount,
        description: 'Payment exceeding balance'
      });

      console.log('[DEBUG] About to call handleUserTransferMVT');
      console.log('[DEBUG] Transfer args:', JSON.stringify(userTransferArgs, null, 2));

      const result = await transactionHandlers.handleUserTransferMVT(userTransferEvent, userTransferArgs);

      console.log('[DEBUG] Transfer result:', JSON.stringify(result, null, 2));
      console.log(`[DEBUG] Expected status: 500, Actual status: ${result.statusCode}`);

      expect(result.statusCode).toBe(500);
      expect(result.message).toContain('Insufficient balance');
    });
  });
});
