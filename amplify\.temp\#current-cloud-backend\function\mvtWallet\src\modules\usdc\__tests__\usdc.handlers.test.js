/**
 * USDC Handlers Test Suite
 * Tests for USDC GraphQL handlers including blockchain operations, authentication, and response formatting
 */

const usdcHandlers = require('../usdc.handlers');
const usdcService = require('../usdc.service');
const authService = require('../../../shared/services/authService');
const validationUtils = require('../../../shared/utils/validationUtils');
const responseUtils = require('../../../shared/utils/responseUtils');

// Mock dependencies
jest.mock('../usdc.service');
jest.mock('../../../shared/services/authService');
jest.mock('../../../shared/utils/validationUtils');
jest.mock('../../../shared/utils/responseUtils');

describe('USDC Handlers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('handleGetUSDCLiquidityPool', () => {
    const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
    const mockArgs = global.testUtils.createMockArgs();

    test('should successfully retrieve USDC liquidity pool data', async () => {
      // Arrange
      const mockPoolData = {
        totalReserves: 10000.50,
        availableBalance: 9500.25,
        adminWalletAddress: '******************************************',
        lastUpdated: '2024-01-01T00:00:00.000Z',
        status: 'AVAILABLE',
        note: 'Connected to blockchain contract'
      };
      
      usdcService.getUSDCLiquidityPool.mockResolvedValue(mockPoolData);
      responseUtils.createSuccessResponse.mockReturnValue({
        statusCode: 200,
        message: 'USDC liquidity pool data retrieved successfully',
        data: mockPoolData
      });

      // Act
      const result = await usdcHandlers.handleGetUSDCLiquidityPool(mockEvent, mockArgs);

      // Assert
      expect(usdcService.getUSDCLiquidityPool).toHaveBeenCalled();
      expect(responseUtils.createSuccessResponse).toHaveBeenCalledWith(
        mockPoolData,
        'USDC liquidity pool data retrieved successfully'
      );
      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(mockPoolData);
    });

    test('should handle blockchain connection errors', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockRejectedValue(new Error('Blockchain connection failed'));
      responseUtils.handleServiceError.mockReturnValue({
        statusCode: 500,
        message: 'Failed to retrieve USDC liquidity pool data'
      });

      // Act
      const result = await usdcHandlers.handleGetUSDCLiquidityPool(mockEvent, mockArgs);

      // Assert
      expect(responseUtils.handleServiceError).toHaveBeenCalledWith(
        expect.any(Error),
        'Failed to retrieve USDC liquidity pool data'
      );
      expect(result.statusCode).toBe(500);
    });

    test('should handle contract unavailable scenarios', async () => {
      // Arrange
      const mockPoolData = {
        totalReserves: 0,
        availableBalance: 0,
        adminWalletAddress: '******************************************',
        lastUpdated: '2024-01-01T00:00:00.000Z',
        status: 'LOW_LIQUIDITY',
        note: 'Contract balance is 0 or unavailable'
      };
      
      usdcService.getUSDCLiquidityPool.mockResolvedValue(mockPoolData);

      // Act
      const result = await usdcHandlers.handleGetUSDCLiquidityPool(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(200);
      expect(result.data.status).toBe('LOW_LIQUIDITY');
    });

    test('should handle timeout errors', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockRejectedValue(new Error('timeout'));
      responseUtils.handleServiceError.mockReturnValue({
        statusCode: 500,
        message: 'Service timeout. Please try again in a few moments.'
      });

      // Act
      const result = await usdcHandlers.handleGetUSDCLiquidityPool(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
      expect(result.message).toContain('timeout');
    });
  });

  describe('handleAdminDepositUSDC', () => {
    const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
    const mockArgs = global.testUtils.createMockArgs({
      amount: 1000.50,
      description: 'Admin USDC deposit to liquidity pool'
    });

    test('should successfully deposit USDC to liquidity pool', async () => {
      // Arrange
      const mockTransactionData = {
        id: 'usdc-deposit-123',
        transactionType: 'USDC_DEPOSIT',
        amount: 1000.50,
        status: 'COMPLETED',
        transactionHash: '0xabcdef123456',
        blockNumber: 12345,
        gasUsed: '21000',
        adminUserId: 'admin-user-123'
      };
      
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateUSDCDepositInput.mockReturnValue({ isValid: true });
      usdcService.depositUSDCToPool.mockResolvedValue(mockTransactionData);
      responseUtils.createSuccessResponse.mockReturnValue({
        statusCode: 200,
        message: 'Successfully deposited 1000.5 USDC to liquidity pool',
        data: mockTransactionData
      });

      // Act
      const result = await usdcHandlers.handleAdminDepositUSDC(mockEvent, mockArgs);

      // Assert
      expect(authService.checkAdminAuthorization).toHaveBeenCalledWith(mockEvent);
      expect(validationUtils.validateUSDCDepositInput).toHaveBeenCalledWith(mockArgs.input);
      expect(usdcService.depositUSDCToPool).toHaveBeenCalledWith(
        1000.50,
        'admin-user-123',
        'Admin USDC deposit to liquidity pool'
      );
      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(mockTransactionData);
    });

    test('should reject non-admin users', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(false);
      responseUtils.createForbiddenResponse.mockReturnValue({
        statusCode: 403,
        message: 'Unauthorized: Admin access required'
      });

      // Act
      const result = await usdcHandlers.handleAdminDepositUSDC(mockEvent, mockArgs);

      // Assert
      expect(authService.checkAdminAuthorization).toHaveBeenCalledWith(mockEvent);
      expect(usdcService.depositUSDCToPool).not.toHaveBeenCalled();
      expect(result.statusCode).toBe(403);
    });

    test('should validate USDC deposit input', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateUSDCDepositInput.mockReturnValue({
        isValid: false,
        error: 'Amount must be a positive number'
      });
      responseUtils.createBadRequestResponse.mockReturnValue({
        statusCode: 400,
        message: 'Amount must be a positive number'
      });

      // Act
      const result = await usdcHandlers.handleAdminDepositUSDC(mockEvent, mockArgs);

      // Assert
      expect(validationUtils.validateUSDCDepositInput).toHaveBeenCalledWith(mockArgs.input);
      expect(usdcService.depositUSDCToPool).not.toHaveBeenCalled();
      expect(result.statusCode).toBe(400);
    });

    test('should handle blockchain transaction failures', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateUSDCDepositInput.mockReturnValue({ isValid: true });
      usdcService.depositUSDCToPool.mockRejectedValue(new Error('Transaction failed: insufficient gas'));
      responseUtils.handleServiceError.mockReturnValue({
        statusCode: 500,
        message: 'Blockchain transaction failed'
      });

      // Act
      const result = await usdcHandlers.handleAdminDepositUSDC(mockEvent, mockArgs);

      // Assert
      expect(responseUtils.handleServiceError).toHaveBeenCalled();
      expect(result.statusCode).toBe(500);
    });

    test('should handle missing admin user ID', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue(null);
      responseUtils.createUnauthorizedResponse.mockReturnValue({
        statusCode: 401,
        message: 'Admin user not found in database'
      });

      // Act
      const result = await usdcHandlers.handleAdminDepositUSDC(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(401);
      expect(usdcService.depositUSDCToPool).not.toHaveBeenCalled();
    });

    test('should handle blockchain connection unavailable', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateUSDCDepositInput.mockReturnValue({ isValid: true });
      usdcService.depositUSDCToPool.mockRejectedValue(new Error('Blockchain connection not available'));

      // Act
      const result = await usdcHandlers.handleAdminDepositUSDC(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });
  });

  describe('handleAdminWithdrawUSDC', () => {
    const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
    const mockArgs = global.testUtils.createMockArgs({
      amount: 500.25,
      description: 'Admin USDC withdrawal from liquidity pool'
    });

    test('should successfully withdraw USDC from liquidity pool', async () => {
      // Arrange
      const mockTransactionData = {
        id: 'usdc-withdrawal-123',
        transactionType: 'USDC_WITHDRAWAL',
        amount: 500.25,
        status: 'COMPLETED',
        transactionHash: '0xfedcba654321',
        blockNumber: 12346,
        gasUsed: '25000',
        adminUserId: 'admin-user-123'
      };
      
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateUSDCWithdrawalInput.mockReturnValue({ isValid: true });
      usdcService.withdrawUSDCFromPool.mockResolvedValue(mockTransactionData);

      // Act
      const result = await usdcHandlers.handleAdminWithdrawUSDC(mockEvent, mockArgs);

      // Assert
      expect(usdcService.withdrawUSDCFromPool).toHaveBeenCalledWith(
        500.25,
        'admin-user-123',
        'Admin USDC withdrawal from liquidity pool'
      );
      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(mockTransactionData);
    });

    test('should validate withdrawal input', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateUSDCWithdrawalInput.mockReturnValue({
        isValid: false,
        error: 'Amount exceeds available balance'
      });

      // Act
      const result = await usdcHandlers.handleAdminWithdrawUSDC(mockEvent, mockArgs);

      // Assert
      expect(validationUtils.validateUSDCWithdrawalInput).toHaveBeenCalledWith(mockArgs.input);
      expect(result.statusCode).toBe(400);
    });

    test('should handle insufficient liquidity', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateUSDCWithdrawalInput.mockReturnValue({ isValid: true });
      usdcService.withdrawUSDCFromPool.mockRejectedValue(new Error('Insufficient liquidity pool balance'));

      // Act
      const result = await usdcHandlers.handleAdminWithdrawUSDC(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });

    test('should handle gas estimation failures', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateUSDCWithdrawalInput.mockReturnValue({ isValid: true });
      usdcService.withdrawUSDCFromPool.mockRejectedValue(new Error('Gas estimation failed'));

      // Act
      const result = await usdcHandlers.handleAdminWithdrawUSDC(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });
  });

  describe('Security and Authorization', () => {
    test('should prevent unauthorized access to admin operations', async () => {
      // Arrange
      const userEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = global.testUtils.createMockArgs({ amount: 1000 });
      
      authService.checkAdminAuthorization.mockResolvedValue(false);

      // Act
      const depositResult = await usdcHandlers.handleAdminDepositUSDC(userEvent, mockArgs);
      const withdrawResult = await usdcHandlers.handleAdminWithdrawUSDC(userEvent, mockArgs);

      // Assert
      expect(depositResult.statusCode).toBe(403);
      expect(withdrawResult.statusCode).toBe(403);
      expect(usdcService.depositUSDCToPool).not.toHaveBeenCalled();
      expect(usdcService.withdrawUSDCFromPool).not.toHaveBeenCalled();
    });

    test('should handle concurrent admin operations safely', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const depositArgs = global.testUtils.createMockArgs({ amount: 1000, description: 'Deposit' });
      const withdrawArgs = global.testUtils.createMockArgs({ amount: 500, description: 'Withdraw' });
      
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateUSDCDepositInput.mockReturnValue({ isValid: true });
      validationUtils.validateUSDCWithdrawalInput.mockReturnValue({ isValid: true });
      
      usdcService.depositUSDCToPool.mockResolvedValue({ id: 'deposit-123', status: 'COMPLETED' });
      usdcService.withdrawUSDCFromPool.mockResolvedValue({ id: 'withdraw-123', status: 'COMPLETED' });

      // Act
      const [depositResult, withdrawResult] = await Promise.all([
        usdcHandlers.handleAdminDepositUSDC(mockEvent, depositArgs),
        usdcHandlers.handleAdminWithdrawUSDC(mockEvent, withdrawArgs)
      ]);

      // Assert
      expect(depositResult.statusCode).toBe(200);
      expect(withdrawResult.statusCode).toBe(200);
      expect(usdcService.depositUSDCToPool).toHaveBeenCalledWith(1000, 'admin-user-123', 'Deposit');
      expect(usdcService.withdrawUSDCFromPool).toHaveBeenCalledWith(500, 'admin-user-123', 'Withdraw');
    });
  });

  describe('Error Handling Edge Cases', () => {
    test('should handle malformed event objects', async () => {
      // Arrange
      const malformedEvent = null;
      const mockArgs = global.testUtils.createMockArgs({ amount: 1000 });

      // Act & Assert
      await expect(
        usdcHandlers.handleGetUSDCLiquidityPool(malformedEvent, mockArgs)
      ).rejects.toThrow();
    });

    test('should handle network connectivity issues', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent();
      const mockArgs = global.testUtils.createMockArgs();
      
      usdcService.getUSDCLiquidityPool.mockRejectedValue(new Error('network error'));

      // Act
      const result = await usdcHandlers.handleGetUSDCLiquidityPool(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });

    test('should handle blockchain node unavailable', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs({ amount: 1000 });
      
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      validationUtils.validateUSDCDepositInput.mockReturnValue({ isValid: true });
      usdcService.depositUSDCToPool.mockRejectedValue(new Error('could not detect network'));

      // Act
      const result = await usdcHandlers.handleAdminDepositUSDC(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });
  });
});
