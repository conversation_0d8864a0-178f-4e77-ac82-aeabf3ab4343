const AWS = require("aws-sdk");
const {
  TranscribeClient,
  StartTranscriptionJobCommand,
  GetTranscriptionJobCommand,
} = require("@aws-sdk/client-transcribe");

const transcribeClient = new TranscribeClient({ region: "us-east-1" });
const s3 = new AWS.S3();

async function transcribeFile(s3URI) {
  console.log('Starting transcription for:', s3URI);
  const jobName = `transcribe-job-${Date.now()}`;
  let format;
  
  // Determine file format from extension
  if (s3URI.endsWith('.mp4')) {
    format = 'mp4';
  } else if (s3URI.endsWith('.mp3')) {
    format = 'mp3';
  } else if (s3URI.endsWith('.wav')) {
    format = 'wav';
  } else {
    throw new Error("Unsupported file format. Only mp4, mp3, and wav are supported.");
  }

  console.time("TranscriptionJob");
  let transcriptFileKey = '';

  try {
    // Start the transcription job
    const startCommand = new StartTranscriptionJobCommand({
      TranscriptionJobName: jobName,
      LanguageCode: "en-US",
      MediaFormat: format,
      Media: { MediaFileUri: s3URI },
      OutputBucketName: process.env.STORAGE_S3MYVILLAGEPROJECTADMINPORTALORGPROFILELOGO_BUCKETNAME,
      Settings: {
        ShowSpeakerLabels: true,
        MaxSpeakerLabels: 2,
        ChannelIdentification: false,
      },
    });

    console.log('Starting transcription job:', jobName);
    await transcribeClient.send(startCommand);

    // Poll for job completion
    let response;
    let attempts = 0;
    const maxAttempts = 60; // 10 minutes max (10s * 60 = 600s = 10min)
    
    while (attempts < maxAttempts) {
      attempts++;
      await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds between checks
      
      const getCommand = new GetTranscriptionJobCommand({ TranscriptionJobName: jobName });
      response = await transcribeClient.send(getCommand);
      
      const status = response.TranscriptionJob.TranscriptionJobStatus;
      console.log(`Transcription job status (attempt ${attempts}):`, status);
      
      if (status === "COMPLETED") {
        console.log('Transcription completed successfully');
        transcriptFileKey = response.TranscriptionJob.Transcript.TranscriptFileUri
          .split('/')
          .slice(4)
          .join('/');
        break;
      } else if (status === "FAILED") {
        throw new Error(`Transcription job failed: ${response.TranscriptionJob.FailureReason}`);
      } else if (status === "CANCELED") {
        throw new Error("Transcription job was canceled");
      }
    }

    if (!transcriptFileKey) {
      throw new Error("Transcription job did not complete within the expected time");
    }

    // Download the transcription result
    console.log('Downloading transcription result:', transcriptFileKey);
    const s3Params = {
      Bucket: process.env.STORAGE_S3MYVILLAGEPROJECTADMINPORTALORGPROFILELOGO_BUCKETNAME,
      Key: transcriptFileKey,
    };
    
    const s3Data = await s3.getObject(s3Params).promise();
    const bodyBuffer = Buffer.isBuffer(s3Data.Body) ? s3Data.Body : Buffer.from(s3Data.Body);
    let transcript = bodyBuffer.toString();
    // Ensure transcript is parsed as an object
    try {
      transcript = JSON.parse(transcript);
    } catch (parseError) {
      console.error('Error parsing transcript JSON:', parseError);
      throw new Error('Failed to parse transcript JSON');
    }

    // Clean up the transcription file from S3
    try {
      console.log('Cleaning up transcription file:', transcriptFileKey);
      await s3.deleteObject(s3Params).promise();
    } catch (cleanupError) {
      console.error('Error cleaning up transcription file:', cleanupError);
      // Don't fail the whole process if cleanup fails
    }

    // Return the complete response
    return {
      status: 'SUCCESS',
      data: transcript,
      message: 'Transcription completed successfully',
      jobName,
      s3URI
    };
    
  } catch (error) {
    console.error('Error in transcribeFile:', error);
    
    // Clean up any partial files
    if (transcriptFileKey) {
      try {
        await s3.deleteObject({
          Bucket: process.env.STORAGE_S3MYVILLAGEPROJECTADMINPORTALORGPROFILELOGO_BUCKETNAME,
          Key: transcriptFileKey
        }).promise();
      } catch (cleanupError) {
        console.error('Error during cleanup after error:', cleanupError);
      }
    }
    
    throw error; // Re-throw the error to be handled by the caller
    
  } finally {
    console.timeEnd("TranscriptionJob");
  }
}

module.exports = { transcribeFile };




