<!-- MVT Token Transactions List -->
<div class="card">
    <div class="card-header">
        <div class="card-title d-flex justify-content-between align-items-center w-100">
            <h3 class="m-0 text-gray-800">{{getTitle()}}</h3>
            <div class="d-flex align-items-center">
                <!-- Currency Filter -->
                <div class="me-3">
                    <select class="form-select form-select-sm" [(ngModel)]="currentTransactionType"
                        (change)="filterTransactionsByType()">
                        <option [value]="TransactionType.ALL">All Currencies</option>
                        <option [value]="TransactionType.MVT">MVT Only</option>
                        <option [value]="TransactionType.USDC">USDC Only</option>
                    </select>
                </div>
                <!-- Only show back button when not embedded -->
                <button *ngIf="!stakeholderWalletAddress" class="btn btn-sm btn-light" (click)="goBack()">
                    <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- Transaction List Section -->
        <div class="table-responsive">
            <table class="table align-middle table-row-bordered table-row-solid gy-4 gs-9">
                <!-- Table header with updated styling -->
                <thead class="border-gray-200 fs-5 fw-semibold bg-lighten">
                    <tr>
                        <th class="min-w-175px ps-9">Date</th>
                        <th class="min-w-120px">Type</th>
                        <th class="min-w-125px">Amount</th>
                        <th class="min-w-100px">Currency</th>
                        <th class="min-w-120px">Status</th>
                        <th class="min-w-200px">Details</th>
                        <th class="min-w-50px text-end">Actions</th>
                    </tr>
                </thead>
                <tbody class="fs-6 fw-semibold text-gray-600">
                    <!-- Loading state for transactions -->
                    <tr *ngIf="isLoading">
                        <td colspan="7" class="text-center">
                            <div class="d-flex flex-column align-items-center py-5">
                                <div class="spinner-border text-primary mb-3">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="text-muted">{{spinnerMessage || 'Loading transactions...'}}</span>
                            </div>
                        </td>
                    </tr>

                    <!-- Regular transaction rows when not loading -->
                    <tr *ngFor="let transaction of displayTransactions" [hidden]="isLoading">
                        <td class="ps-9">
                            <!-- ✅ ENHANCED: Multiple fallback options for date display -->
                            <span *ngIf="transaction.formattedDate; else dateFormatFallback">
                                {{transaction.formattedDate}}
                            </span>
                            <ng-template #dateFormatFallback>
                                <span *ngIf="transaction.date; else noDateFallback">
                                    {{transaction.date | date:'MMM d, y, h:mm a'}}
                                </span>
                                <ng-template #noDateFallback>
                                    <span class="text-muted">—</span>
                                </ng-template>
                            </ng-template>
                        </td>
                        <td>
                            <span class="badge" [ngClass]="{
                                'badge-success': transaction.type === TransactionOperationType.RECEIVED,
                                'badge-info': transaction.type === TransactionOperationType.ADDED,
                                'badge-danger': transaction.type === TransactionOperationType.SENT
                            }">{{transaction.type}}</span>
                        </td>
                        <td [ngClass]="{
                            'text-success': transaction.type === TransactionOperationType.RECEIVED || transaction.type === TransactionOperationType.ADDED,
                            'text-danger': transaction.type === TransactionOperationType.SENT
                        }">
                            {{transaction.type === TransactionOperationType.SENT ? '-' : '+'}}{{transaction.amount}}
                        </td>
                        <td>
                            <span class="badge" [ngClass]="{
                                'badge-light-primary': transaction.currency === TransactionType.MVT,
                                'badge-light-warning': transaction.currency === TransactionType.USDC
                            }">{{transaction.currency === TransactionType.MVT ? 'MVT' : 'USDC'}}</span>
                        </td>
                        <td>
                            <span class="badge" [ngClass]="{
                                'badge-light-success': transaction.status === TransactionStatus.COMPLETED,
                                'badge-light-warning': transaction.status === TransactionStatus.PENDING,
                                'badge-light-danger': transaction.status === TransactionStatus.FAILED
                            }">{{transaction.status}}</span>
                        </td>
                        <td>
                            <!-- Use backend-provided display label -->
                            {{getTransactionDisplayLabel(transaction)}}
                        </td>
                        <td class="text-end">
                            <!-- Use backend-provided flag for Etherscan link -->
                            <a *ngIf="transaction.showEtherscanLink"
                               href="javascript:void(0)"
                               class="btn btn-icon btn-sm btn-light-primary"
                               (click)="openInEtherscan(transaction.transactionHash)"
                               title="View on Etherscan">
                                <i class="bi bi-box-arrow-up-right"></i>
                            </a>
                            <!-- Keep column space for transactions without Etherscan link -->
                            <span *ngIf="!transaction.showEtherscanLink" class="text-muted">—</span>
                        </td>
                    </tr>

                    <!-- Empty state when not loading but no data -->
                    <tr *ngIf="displayTransactions.length === 0 && !isLoading">
                        <td colspan="7" class="text-center">
                            <div class="d-flex flex-column align-items-center py-5">
                                <i class="bi bi-exclamation-circle fs-3x text-muted mb-3"></i>
                                <span class="text-muted">No transactions found</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination (only show if enabled) -->
        <div class="d-flex justify-content-between align-items-center mt-5" *ngIf="totalItems > 0 && showPagination">
            <div class="fs-6 fw-bold text-gray-700">
                Showing {{ getStartIndex() }} to {{ getEndIndex() }} of {{ totalItems }} entries
            </div>
            <ul class="pagination">
                <li class="page-item previous" [ngClass]="{'disabled': currentPage === 1}">
                    <a class="page-link cursor-pointer" (click)="goToFirstPage()">First</a>
                </li>
                <li class="page-item previous" [ngClass]="{'disabled': currentPage === 1}">
                    <a class="page-link cursor-pointer" (click)="goToPreviousPage()">
                        <i class="previous"></i>
                    </a>
                </li>

                <ng-container *ngFor="let page of getPageNumbers()">
                    <li class="page-item" [ngClass]="{'active': currentPage === page}">
                        <a class="page-link cursor-pointer" (click)="goToPage(page)">
                            {{ page }}
                        </a>
                    </li>
                </ng-container>

                <li class="page-item next" [ngClass]="{'disabled': currentPage >= getTotalPages()}">
                    <a class="page-link cursor-pointer" (click)="goToNextPage()">
                        <i class="next"></i>
                    </a>
                </li>
                <li class="page-item next" [ngClass]="{'disabled': currentPage >= getTotalPages()}">
                    <a class="page-link cursor-pointer" (click)="goToLastPage()">Last</a>
                </li>
            </ul>
        </div>

        <!-- View All Transactions Button (only show if enabled) -->
        <div class="d-flex justify-content-end mt-4" *ngIf="showViewAllButton && totalItems > 0">
            <button class="btn btn-primary" (click)="onViewAllTransactions()">
                View All Transactions
            </button>
        </div>
    </div>
</div>