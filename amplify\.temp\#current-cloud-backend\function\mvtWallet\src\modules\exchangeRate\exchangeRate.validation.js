const { 
  validateMVTAmount, 
  isNonEmptyString 
} = require('../../shared/utils/validationUtils');

/**
 * Validate exchange rate request parameters
 * @param {object} params - Request parameters
 * @returns {object} - Validation result
 */
function validateExchangeRateRequest(params) {
  // No specific validation needed for getting exchange rate data
  // This is a read-only operation that doesn't require input parameters
  return { isValid: true };
}

/**
 * Validate MVT amount for exchange rate calculation
 * @param {number} mvtAmount - MVT amount to validate
 * @returns {object} - Validation result
 */
function validateMVTAmountForExchange(mvtAmount) {
  if (mvtAmount === undefined || mvtAmount === null) {
    return { isValid: false, error: 'MVT amount is required for exchange rate calculation' };
  }
  
  return validateMVTAmount(mvtAmount);
}

/**
 * Validate USDC amount for exchange rate calculation
 * @param {number} usdcAmount - USDC amount to validate
 * @returns {object} - Validation result
 */
function validateUSDCAmountForExchange(usdcAmount) {
  if (usdcAmount === undefined || usdcAmount === null) {
    return { isValid: false, error: 'USDC amount is required for exchange rate calculation' };
  }
  
  if (typeof usdcAmount !== 'number') {
    return { isValid: false, error: 'USDC amount must be a number' };
  }
  
  if (usdcAmount <= 0) {
    return { isValid: false, error: 'USDC amount must be greater than 0' };
  }
  
  if (!isFinite(usdcAmount)) {
    return { isValid: false, error: 'USDC amount must be a valid number' };
  }
  
  // Check for reasonable precision (USDC has 6 decimals)
  const decimalPlaces = (usdcAmount.toString().split('.')[1] || '').length;
  if (decimalPlaces > 6) {
    return { isValid: false, error: 'USDC amount cannot have more than 6 decimal places' };
  }
  
  return { isValid: true };
}

/**
 * Validate swap feasibility parameters
 * @param {object} params - Swap parameters
 * @returns {object} - Validation result
 */
function validateSwapFeasibilityParams(params) {
  if (!params) {
    return { isValid: false, error: 'Swap parameters are required' };
  }
  
  const { mvtAmount, userId } = params;
  
  // Validate MVT amount
  const mvtValidation = validateMVTAmountForExchange(mvtAmount);
  if (!mvtValidation.isValid) {
    return mvtValidation;
  }
  
  // Validate user ID
  if (!userId || !isNonEmptyString(userId)) {
    return { isValid: false, error: 'Valid user ID is required' };
  }
  
  return { isValid: true };
}

/**
 * Validate liquidity pool parameters
 * @param {object} params - Liquidity pool parameters
 * @returns {object} - Validation result
 */
function validateLiquidityPoolParams(params) {
  // Basic validation for liquidity pool operations
  // Most validation is done at the service level
  return { isValid: true };
}

/**
 * Validate exchange rate calculation input
 * @param {object} input - Exchange rate calculation input
 * @returns {object} - Validation result
 */
function validateExchangeRateCalculationInput(input) {
  if (!input) {
    return { isValid: false, error: 'Exchange rate calculation input is required' };
  }
  
  // Validate based on calculation type
  if (input.mvtAmount !== undefined) {
    return validateMVTAmountForExchange(input.mvtAmount);
  }
  
  if (input.usdcAmount !== undefined) {
    return validateUSDCAmountForExchange(input.usdcAmount);
  }
  
  // If no specific amount provided, it's a general rate request
  return { isValid: true };
}

module.exports = {
  validateExchangeRateRequest,
  validateMVTAmountForExchange,
  validateUSDCAmountForExchange,
  validateSwapFeasibilityParams,
  validateLiquidityPoolParams,
  validateExchangeRateCalculationInput
};
