import { Component, Input, Output, EventEmitter, OnInit, OnD<PERSON>roy } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { SharedService } from 'src/app/shared/services/shared.service';
import { MvtWalletService } from 'src/app/shared/services/mvt-wallet.service';
import { Transaction, TransactionType, TransactionStatus, TransactionOperationType } from 'src/app/shared/types/transaction.types';
import { ErrorHandlerService } from '../../../../shared/services/error-handler.service';
import { ErrorSeverity } from '../../../../shared/interfaces/standardized-error.interface';

@Component({
  selector: 'app-mvt-transactions',
  templateUrl: './mvt-transactions.component.html',
  styleUrls: ['./mvt-transactions.component.scss']
})
export class MvtTransactionsComponent implements On<PERSON><PERSON><PERSON>, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  public readonly ITEMS_PER_PAGE = 10;

  public readonly TransactionType = TransactionType;
  public readonly TransactionStatus = TransactionStatus;
  public readonly TransactionOperationType = TransactionOperationType;

  math = Math;
  allTransactions: Transaction[] = [];
  displayTransactions: Transaction[] = [];
  isLoading = false;
  currentPage = 1;
  totalItems = 0;
  currentTransactionType: TransactionType = TransactionType.ALL;

  @Input() stakeholderWalletAddress = '';
  @Input() stakeholderName = '';
  @Input() showAllTransactions = false; // New input to control admin "view all" mode
  @Input() transactionLimit = 100; // New input to control number of transactions to fetch
  @Input() showPagination = true; // New input to control pagination display
  @Input() showViewAllButton = false; // New input to control "View All" button display

  @Output() viewAllClicked = new EventEmitter<void>(); // Event emitter for "View All" button

  walletAddress = '0x12345678987';
  contractAddress = '';
  spinnerMessage = '';

  constructor(
    private readonly sharedService: SharedService,
    private readonly mvtWalletService: MvtWalletService,
    private readonly toastr: ToastrService,
    private readonly router: Router,
    private readonly errorHandler: ErrorHandlerService
  ) { }

  ngOnInit(): void {
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeComponent(): void {
    // Detect admin context: when accessed via route without stakeholder inputs
    const isAccessedViaRoute = !this.stakeholderWalletAddress && !this.stakeholderName;
    const currentUrl = this.router.url;
    const isAdminFundingDashboard = currentUrl.includes('/funding-dashboard/mvt-transactions');

    if (isAccessedViaRoute && isAdminFundingDashboard) {
      this.showAllTransactions = true;
      console.log('Detected admin funding dashboard context - showing all transactions');
    }

    if (this.stakeholderWalletAddress) {
      this.walletAddress = this.stakeholderWalletAddress;
    }

    this.getMVTBalance();
    this.loadAllTransactions();

    this.sharedService.currentPage
      .pipe(takeUntil(this.destroy$))
      .subscribe(page => {
        this.currentPage = page;
        this.filterTransactionsByType();
      });
  }

  private loadAllTransactions(): void {
    this.isLoading = true;
    this.spinnerMessage = 'Loading transactions...';

    // Determine the address parameter based on context
    let addressToUse: string;

    if (this.showAllTransactions) {
      // Admin "view all" mode - use "all" to fetch all transactions
      addressToUse = 'all';
    } else if (this.stakeholderWalletAddress) {
      // Stakeholder-specific mode - use the provided stakeholder address
      addressToUse = this.stakeholderWalletAddress;
    } else {
      // Default mode - use the default wallet address
      addressToUse = this.walletAddress;
    }

    console.log('Loading transactions with context:', {
      showAllTransactions: this.showAllTransactions,
      stakeholderWalletAddress: this.stakeholderWalletAddress,
      addressToUse: addressToUse,
      transactionLimit: this.transactionLimit
    });

    console.log(`🔍 DEBUG: Calling getMVTTransactionList with limit=${this.transactionLimit} for ${addressToUse}`);

    this.mvtWalletService.getMVTTransactionList(addressToUse, true, this.transactionLimit)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('Component received response:', response);

          if (response?.data?.getMVTTransactionList?.data) {
            const transactionList = response.data.getMVTTransactionList.data;
            console.log('Raw transaction list from service:', transactionList);

            // Use backend-provided display-ready data directly
            this.allTransactions = this.mapBackendTransactions(transactionList);
            console.log('Mapped transactions:', this.allTransactions);

            // ✅ DEBUG: Log date formatting for first few transactions
            console.log('🗓️ Date formatting verification:');
            this.allTransactions.slice(0, 3).forEach((tx, index) => {
              console.log(`  Transaction ${index + 1}:`, {
                id: tx.id,
                rawDate: tx.date,
                formattedDate: tx.formattedDate,
                dateType: typeof tx.date,
                isValidDate: tx.date instanceof Date && !isNaN(tx.date.getTime())
              });
            });

            // Backend handles context-aware filtering, so no frontend filtering needed
            // Sort by date (backend should already provide sorted data, but ensure consistency)
            this.allTransactions.sort((a, b) => b.date.getTime() - a.date.getTime());

            // Debug: Log final sorted transactions to verify order
            console.log(`🔍 DEBUG: Final sorted transactions (limit=${this.transactionLimit}):`);
            this.allTransactions.slice(0, Math.min(5, this.allTransactions.length)).forEach((tx, index) => {
              console.log(`  ${index + 1}. ID: ${tx.id}, Date: ${tx.date.toISOString()}, Type: ${tx.type}`);
            });

            this.totalItems = this.allTransactions.length;
            this.sharedService.currentPage.next(1);
            this.filterTransactionsByType();

            console.log(`Final loaded transactions count: ${this.allTransactions.length}`);

          } else {
            console.log('No transaction data found in response');
            this.allTransactions = [];
            this.totalItems = 0;
            this.displayTransactions = [];
          }
          this.isLoading = false;
          this.spinnerMessage = '';
        },
        error: (error) => {
          console.error('Component error:', error);
          this.handleTransactionError(error);
          this.isLoading = false;
          this.spinnerMessage = '';
        }
      });
  }

  /**
   * Map backend transactions to frontend interface
   * Backend provides display-ready data, minimal transformation needed
   */
  private mapBackendTransactions(transactions: any[]): Transaction[] {
    if (!transactions?.length) {
      return [];
    }

    return transactions.map((item: any) => ({
      id: item.id,
      date: this.parseDate(item.createdAt ?? item.date ?? item.timestamp),
      type: this.mapDisplayTypeToEnum(item.displayType ?? 'ADDED'),
      originalType: item.transactionType,
      amount: item.amount,
      currency: item.tokenType ? (item.tokenType.toLowerCase() as TransactionType) : TransactionType.MVT,
      status: this.mapTransactionStatus(item.status),
      from: item.fromUserId ?? item.fromWalletId,
      to: item.toUserId ?? item.toWalletId,
      fromUser: item.fromUser,
      toUser: item.toUser,
      adminUser: item.adminUser,
      // ✅ FIXED: Include blockchain transaction hash for Etherscan links
      transactionHash: item.transactionHash,
      blockNumber: item.blockNumber,
      gasUsed: item.gasUsed,
      // Use backend-provided display-ready fields directly
      displayType: item.displayType ?? 'ADDED',
      primaryLabel: item.primaryLabel ?? 'Transaction',
      secondaryInfo: item.secondaryInfo,
      showEtherscanLink: item.showEtherscanLink ?? false,
      // ✅ ENHANCED: Ensure formattedDate is always available
      formattedDate: item.formattedDate ?? this.formatDate(this.parseDate(item.createdAt ?? item.date ?? item.timestamp))
    }));
  }

  /**
   * Map backend display type to frontend enum
   * @param displayType Backend display type string (SENT, RECEIVED, ADDED)
   * @returns Frontend TransactionOperationType enum (Sent, Received, Added)
   */
  private mapDisplayTypeToEnum(displayType: string): TransactionOperationType {
    if (!displayType) {
      return TransactionOperationType.ADDED;
    }

    switch (displayType.toUpperCase()) {
      case 'SENT':
        return TransactionOperationType.SENT;
      case 'RECEIVED':
        return TransactionOperationType.RECEIVED;
      case 'ADDED':
      case 'MINTED':
      case 'MINT':
        return TransactionOperationType.ADDED;
      default:
        console.warn(`Unknown display type: ${displayType}, defaulting to ADDED`);
        return TransactionOperationType.ADDED;
    }
  }

  /**
   * Parse various date formats into a Date object
   * @param dateValue Date value in various formats
   * @returns Parsed Date object
   */
  private parseDate(dateValue: any): Date {
    if (!dateValue) {
      console.warn('parseDate: No date value provided, using current date');
      return new Date();
    }

    // If it's already a Date object, validate it
    if (dateValue instanceof Date) {
      if (isNaN(dateValue.getTime())) {
        console.warn('parseDate: Invalid Date object provided, using current date');
        return new Date();
      }
      return dateValue;
    }

    // If it's a string
    if (typeof dateValue === 'string') {
      // Handle ISO date strings (most common format from backend)
      if (dateValue.includes('T') || dateValue.includes('-')) {
        const parsed = new Date(dateValue);
        if (!isNaN(parsed.getTime())) {
          return parsed;
        }
      }

      // Try parsing as timestamp string
      const timestamp = parseInt(dateValue);
      if (!isNaN(timestamp)) {
        // If timestamp is in seconds, convert to milliseconds
        const date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
    }

    // If it's a number (timestamp)
    if (typeof dateValue === 'number') {
      // If timestamp is in seconds, convert to milliseconds
      const date = new Date(dateValue < 10000000000 ? dateValue * 1000 : dateValue);
      if (!isNaN(date.getTime())) {
        return date;
      }
    }

    // Fallback to current date with warning
    console.warn('parseDate: Could not parse date value, using current date:', dateValue);
    return new Date();
  }



  filterTransactionsByType(): void {
    let filteredTransactions: Transaction[] = [];

    if (this.currentTransactionType === TransactionType.MVT) {
      filteredTransactions = this.allTransactions.filter(tx => tx.currency === TransactionType.MVT);
    } else if (this.currentTransactionType === TransactionType.USDC) {
      filteredTransactions = this.allTransactions.filter(tx => tx.currency === TransactionType.USDC);
    } else {
      filteredTransactions = this.allTransactions;
    }

    this.totalItems = filteredTransactions.length;
    this.updateDisplayTransactionsFromFiltered(filteredTransactions);
  }

  private updateDisplayTransactionsFromFiltered(filteredTransactions: Transaction[]): void {
    const startIndex = (this.currentPage - 1) * this.ITEMS_PER_PAGE;
    const endIndex = Math.min(startIndex + this.ITEMS_PER_PAGE, filteredTransactions.length);
    this.displayTransactions = filteredTransactions.slice(startIndex, endIndex);
  }

  private getMVTBalance(): void {
    // Skip balance loading when showing all transactions (admin context)
    if (this.showAllTransactions) {
      return;
    }

    this.isLoading = true;
    this.spinnerMessage = 'Loading contract information...';

    const addressToUse = this.stakeholderWalletAddress || this.walletAddress;

    this.mvtWalletService.getMVTBalance(addressToUse, true)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          if (response?.data?.getMVTBalance?.data?.id) {
            this.contractAddress = response.data.getMVTBalance.data.id;
          }
          this.isLoading = false;
          this.spinnerMessage = '';
        },
        error: (error) => {
          console.error('Error loading MVT balance:', error);
          this.isLoading = false;
          this.spinnerMessage = '';
        }
      });
  }

  private handleTransactionError(error: any): void {
    console.error('Transaction error:', error);
    this.allTransactions = [];
    this.totalItems = 0;
    this.displayTransactions = [];

    this.errorHandler.handleError(error, {
      showToast: true,
      severity: ErrorSeverity.MEDIUM
    });
  }



  /**
   * Check if a transaction is an admin minting operation
   * @param transaction The transaction to check
   * @returns true if it's an admin minting transaction
   */
  isAdminMinting(transaction: any): boolean {
    // Check if the original backend transaction type is ADMIN_MINT
    return transaction.originalType === 'ADMIN_MINT';
  }

  /**
   * Get display label for transaction - now uses backend-provided primaryLabel
   * @param transaction The transaction object with display-ready data
   * @returns Display label string
   */
  getTransactionDisplayLabel(transaction: Transaction): string {
    // Use backend-provided primary label if available
    if (transaction.primaryLabel) {
      return transaction.primaryLabel;
    }

    // Fallback for backward compatibility (should not be needed with optimized backend)
    return 'Transaction';
  }

  openInEtherscan(transactionHash: string | undefined): void {
    if (!transactionHash) {
      console.warn('No transaction hash available for Etherscan link');
      return;
    }
    this.sharedService.openInEtherscan(transactionHash);
  }

  /**
   * Maps API response status string to the TransactionStatus enum value
   */
  private mapTransactionStatus(status: string): TransactionStatus {
    if (!status) return TransactionStatus.COMPLETED;

    switch (status.toUpperCase()) {
      case 'COMPLETED':
      case 'COMPLETE':
      case 'SUCCESS':
      case 'SUCCESSFUL':
        return TransactionStatus.COMPLETED;
      case 'PENDING':
      case 'PROCESSING':
      case 'IN_PROGRESS':
        return TransactionStatus.PENDING;
      case 'FAILED':
      case 'FAILURE':
      case 'ERROR':
        return TransactionStatus.FAILED;
      default:
        return TransactionStatus.COMPLETED;
    }
  }

  /**
   * Format date for display in user-friendly format
   * @param date Date object to format
   * @returns Formatted date string in local timezone
   */
  formatDate(date: Date | string | null | undefined): string {
    if (!date) return '';

    // Convert to Date object if it's a string
    let dateObj: Date;
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return '';
    }

    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      console.warn('Invalid date provided to formatDate:', date);
      return '';
    }

    // Format in local timezone with user-friendly format
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true // Use 12-hour format with AM/PM
      // Removed timeZone: 'UTC' to use local timezone
    });
  }

  /**
   * Get formatted date for display with multiple fallback options
   * @param transaction Transaction object
   * @returns Formatted date string
   */
  getFormattedTransactionDate(transaction: Transaction): string {
    // Priority 1: Use backend-provided formatted date
    if (transaction.formattedDate) {
      return transaction.formattedDate;
    }

    // Priority 2: Format the date object
    if (transaction.date) {
      return this.formatDate(transaction.date);
    }

    // Priority 3: Return placeholder
    return '—';
  }

  getTitle(): string {
    if (this.showAllTransactions) {
      return 'All MVT Transactions';
    } else if (this.stakeholderName) {
      return `${this.stakeholderName}'s Transactions`;
    } else {
      return 'My Transactions';
    }
  }

  goBack(): void {
    this.router.navigate(['/funding-dashboard']);
  }

  onViewAllTransactions(): void {
    this.viewAllClicked.emit();
  }

  /**
   * Public method to refresh transactions
   * Can be called by parent components after operations like minting
   */
  public refreshTransactions(): void {
    console.log('Refreshing transactions...');
    this.loadAllTransactions();
  }

  getStartIndex(): number {
    return (this.currentPage - 1) * this.ITEMS_PER_PAGE + 1;
  }

  getEndIndex(): number {
    return Math.min(this.currentPage * this.ITEMS_PER_PAGE, this.totalItems);
  }

  getTotalPages(): number {
    return Math.ceil(this.totalItems / this.ITEMS_PER_PAGE);
  }

  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.getTotalPages()) {
      this.currentPage = page;
      this.filterTransactionsByType();
    }
  }

  goToFirstPage(): void {
    this.goToPage(1);
  }

  goToLastPage(): void {
    this.goToPage(this.getTotalPages());
  }

  goToPreviousPage(): void {
    this.goToPage(this.currentPage - 1);
  }

  goToNextPage(): void {
    this.goToPage(this.currentPage + 1);
  }
}
