/**
 * Authentication and Validation Integration Test Suite
 * Tests for interactions between authentication and validation modules
 */

// Create mock DynamoDB instance
const mockDynamoDB = {
  getItem: jest.fn(),
  putItem: jest.fn(),
  updateItem: jest.fn(),
  scan: jest.fn(),
  query: jest.fn(),
  describeTable: jest.fn(),
  batchGetItem: jest.fn(),
  batchWriteItem: jest.fn()
};

const mockData = require('../shared/mockData');

// Mock AWS and shared utilities
jest.mock('../../config/aws', () => ({
  AWS: {
    DynamoDB: {
      Converter: {
        marshall: jest.fn((item) => item),
        unmarshall: jest.fn((item) => item)
      }
    }
  },
  ddb: mockDynamoDB
}));

const authService = require('../../shared/services/authService');
const validationUtils = require('../../shared/utils/validationUtils');

jest.mock('../../shared/database/dynamoUtils', () => ({
  getTableName: jest.fn((tableName) => `test-${tableName}`),
  tableExists: jest.fn().mockResolvedValue(true)
}));

// Mock authentication service
jest.mock('../../shared/services/authService', () => ({
  checkAdminAuthorization: jest.fn(),
  getCurrentUserDatabaseId: jest.fn(),
  getUserIdFromEvent: jest.fn(),
  checkUserAuthorization: jest.fn(),
  validateUserExists: jest.fn(),
  validateUserWalletAddress: jest.fn()
}));

// Mock validation utilities
jest.mock('../../shared/utils/validationUtils', () => ({
  validateMintInput: jest.fn(),
  validateTransferInput: jest.fn(),
  validateUserTransferInput: jest.fn(),
  validateUSDCDepositInput: jest.fn(),
  validateSwapRequestInput: jest.fn(),
  validateSwapApprovalInput: jest.fn(),
  validateSwapRejectionInput: jest.fn(),
  validateMVTAmount: jest.fn(),
  validateUSDCAmount: jest.fn(),
  validateUserId: jest.fn(),
  validateWalletAddress: jest.fn()
}));

// Make constants available globally
global.TOKEN_TYPES = mockData.MOCK_CONSTANTS.TOKEN_TYPES;
global.TRANSACTION_TYPES = mockData.MOCK_CONSTANTS.TRANSACTION_TYPES;
global.TRANSACTION_STATUS = mockData.MOCK_CONSTANTS.TRANSACTION_STATUS;
global.SWAP_STATUS = mockData.MOCK_CONSTANTS.SWAP_STATUS;
global.STATUS_CODES = mockData.MOCK_CONSTANTS.STATUS_CODES;
global.CENTRAL_WALLET_ID = mockData.MOCK_CONSTANTS.CENTRAL_WALLET_ID;

jest.mock('../../shared/constants/index', () => mockData.MOCK_CONSTANTS);

// Set up global test utilities
global.testUtils = {
  createMockEvent: mockData.createMockEvent,
  createMockArgs: mockData.createMockArgs,
  createMockUser: mockData.createMockUser,
  createMockSwapRequest: mockData.createMockSwapRequest,
  createMockWalletBalance: mockData.createMockWalletBalance,
  mockDynamoDBSuccess: mockData.mockDynamoDBSuccess,
  mockDynamoDBError: mockData.mockDynamoDBError
};

describe('Authentication and Validation Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default successful DynamoDB responses
    mockDynamoDB.getItem.mockReturnValue(mockData.mockDynamoDBSuccess({
      Item: mockData.TEST_USERS.REGULAR_USER
    }));
    mockDynamoDB.scan.mockReturnValue(mockData.mockDynamoDBSuccess({
      Items: [mockData.TEST_USERS.REGULAR_USER]
    }));
    
    // Setup default auth responses
    authService.checkAdminAuthorization.mockResolvedValue(true);
    authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
    authService.getUserIdFromEvent.mockResolvedValue('cognito-user-123');
    authService.checkUserAuthorization.mockResolvedValue(true);
    authService.validateUserExists.mockResolvedValue(true);
    authService.validateUserWalletAddress.mockResolvedValue(true);
    
    // Setup default validation responses
    validationUtils.validateMintInput.mockReturnValue({ isValid: true });
    validationUtils.validateTransferInput.mockReturnValue({ isValid: true });
    validationUtils.validateUserTransferInput.mockReturnValue({ isValid: true });
    validationUtils.validateUSDCDepositInput.mockReturnValue({ isValid: true });
    validationUtils.validateSwapRequestInput.mockReturnValue({ isValid: true });
    validationUtils.validateSwapApprovalInput.mockReturnValue({ isValid: true });
    validationUtils.validateSwapRejectionInput.mockReturnValue({ isValid: true });
    validationUtils.validateMVTAmount.mockReturnValue({ isValid: true });
    validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
    validationUtils.validateUserId.mockReturnValue({ isValid: true });
    validationUtils.validateWalletAddress.mockReturnValue({ isValid: true });
  });

  describe('Admin Authorization Integration', () => {
    test('should validate admin permissions for mint operations', async () => {
      // Arrange
      const event = mockData.createMockEvent('admin-cognito-id', true);
      const mintInput = { amount: 1000, description: 'Test mint' };

      // Act
      const isAdmin = await authService.checkAdminAuthorization(event);
      const validation = validationUtils.validateMintInput(mintInput);

      // Assert
      expect(isAdmin).toBe(true);
      expect(validation.isValid).toBe(true);
    });

    test('should reject non-admin users for admin operations', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(false);
      const event = mockData.createMockEvent('user-cognito-id', false);
      const mintInput = { amount: 1000, description: 'Unauthorized mint' };

      // Act
      const isAdmin = await authService.checkAdminAuthorization(event);
      const validation = validationUtils.validateMintInput(mintInput);

      // Assert
      expect(isAdmin).toBe(false);
      expect(validation.isValid).toBe(true); // Input is valid, but user lacks permission
    });

    test('should validate admin permissions for USDC operations', async () => {
      // Arrange
      const event = mockData.createMockEvent('admin-cognito-id', true);
      const depositInput = { amount: 5000.0, description: 'Liquidity deposit' };

      // Act
      const isAdmin = await authService.checkAdminAuthorization(event);
      const validation = validationUtils.validateUSDCDepositInput(depositInput);

      // Assert
      expect(isAdmin).toBe(true);
      expect(validation.isValid).toBe(true);
    });
  });

  describe('User Authorization Integration', () => {
    test('should validate user permissions for transfer operations', async () => {
      // Arrange
      const event = mockData.createMockEvent('user-cognito-id', false);
      const transferInput = {
        recipientUserId: 'recipient-123',
        amount: 500,
        description: 'Payment to friend'
      };

      // Act
      const isAuthorized = await authService.checkUserAuthorization(event);
      const validation = validationUtils.validateUserTransferInput(transferInput);

      // Assert
      expect(isAuthorized).toBe(true);
      expect(validation.isValid).toBe(true);
    });

    test('should validate user wallet address for swap operations', async () => {
      // Arrange
      const userId = 'test-user-123';
      const swapInput = { mvtAmount: 100, description: 'Convert to USDC' };

      // Act
      const hasWalletAddress = await authService.validateUserWalletAddress(userId);
      const validation = validationUtils.validateSwapRequestInput(swapInput);

      // Assert
      expect(hasWalletAddress).toBe(true);
      expect(validation.isValid).toBe(true);
    });

    test('should reject swap requests for users without wallet addresses', async () => {
      // Arrange
      authService.validateUserWalletAddress.mockResolvedValue(false);
      const userId = 'user-without-wallet';
      const swapInput = { mvtAmount: 100, description: 'Convert to USDC' };

      // Act
      const hasWalletAddress = await authService.validateUserWalletAddress(userId);
      const validation = validationUtils.validateSwapRequestInput(swapInput);

      // Assert
      expect(hasWalletAddress).toBe(false);
      expect(validation.isValid).toBe(true); // Input is valid, but user lacks wallet
    });
  });

  describe('Input Validation Integration', () => {
    test('should validate MVT amounts across different operations', async () => {
      // Arrange
      const testCases = [
        { amount: 100, operation: 'mint', expected: true },
        { amount: 0, operation: 'mint', expected: false },
        { amount: -50, operation: 'transfer', expected: false },
        { amount: 1000000, operation: 'swap', expected: true }
      ];

      for (const testCase of testCases) {
        // Act
        validationUtils.validateMVTAmount.mockReturnValue({ 
          isValid: testCase.expected,
          error: testCase.expected ? null : 'Invalid amount'
        });

        const validation = validationUtils.validateMVTAmount(testCase.amount);

        // Assert
        expect(validation.isValid).toBe(testCase.expected);
        if (!testCase.expected) {
          expect(validation.error).toBeDefined();
        }
      }
    });

    test('should validate USDC amounts for deposit operations', async () => {
      // Arrange
      const testCases = [
        { amount: 1000.50, expected: true },
        { amount: 0, expected: false },
        { amount: -100.25, expected: false },
        { amount: 0.01, expected: true }
      ];

      for (const testCase of testCases) {
        // Act
        validationUtils.validateUSDCAmount.mockReturnValue({ 
          isValid: testCase.expected,
          error: testCase.expected ? null : 'Invalid USDC amount'
        });

        const validation = validationUtils.validateUSDCAmount(testCase.amount);

        // Assert
        expect(validation.isValid).toBe(testCase.expected);
      }
    });

    test('should validate wallet addresses for blockchain operations', async () => {
      // Arrange
      const testCases = [
        { address: '******************************************', expected: true },
        { address: '0xinvalid', expected: false },
        { address: '', expected: false },
        { address: null, expected: false }
      ];

      for (const testCase of testCases) {
        // Act
        validationUtils.validateWalletAddress.mockReturnValue({ 
          isValid: testCase.expected,
          error: testCase.expected ? null : 'Invalid wallet address'
        });

        const validation = validationUtils.validateWalletAddress(testCase.address);

        // Assert
        expect(validation.isValid).toBe(testCase.expected);
      }
    });
  });

  describe('Cross-Module Validation Integration', () => {
    test('should validate complete swap request workflow', async () => {
      // Arrange
      const event = mockData.createMockEvent('user-cognito-id', false);
      const userId = 'test-user-123';
      const swapInput = { mvtAmount: 100, description: 'Test swap' };

      // Act
      const userAuth = await authService.checkUserAuthorization(event);
      const userExists = await authService.validateUserExists(userId);
      const hasWallet = await authService.validateUserWalletAddress(userId);
      const inputValid = validationUtils.validateSwapRequestInput(swapInput);
      const amountValid = validationUtils.validateMVTAmount(swapInput.mvtAmount);

      // Assert
      expect(userAuth).toBe(true);
      expect(userExists).toBe(true);
      expect(hasWallet).toBe(true);
      expect(inputValid.isValid).toBe(true);
      expect(amountValid.isValid).toBe(true);
    });

    test('should validate complete admin transfer workflow', async () => {
      // Arrange
      const event = mockData.createMockEvent('admin-cognito-id', true);
      const transferInput = { userId: 'recipient-123', amount: 1000, description: 'Admin transfer' };

      // Act
      const adminAuth = await authService.checkAdminAuthorization(event);
      const inputValid = validationUtils.validateTransferInput(transferInput);
      const userExists = await authService.validateUserExists(transferInput.userId);
      const amountValid = validationUtils.validateMVTAmount(transferInput.amount);

      // Assert
      expect(adminAuth).toBe(true);
      expect(inputValid.isValid).toBe(true);
      expect(userExists).toBe(true);
      expect(amountValid.isValid).toBe(true);
    });
  });

  describe('Error Handling Integration', () => {
    test('should handle authentication failures gracefully', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockRejectedValue(new Error('Auth service unavailable'));
      const event = mockData.createMockEvent('admin-cognito-id', true);

      // Act & Assert
      await expect(
        authService.checkAdminAuthorization(event)
      ).rejects.toThrow('Auth service unavailable');
    });

    test('should handle validation failures with detailed error messages', async () => {
      // Arrange
      const invalidInput = { amount: -100, description: '' };
      validationUtils.validateMintInput.mockReturnValue({
        isValid: false,
        errors: ['Amount must be positive', 'Description is required']
      });

      // Act
      const validation = validationUtils.validateMintInput(invalidInput);

      // Assert
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toHaveLength(2);
      expect(validation.errors).toContain('Amount must be positive');
      expect(validation.errors).toContain('Description is required');
    });
  });
});
