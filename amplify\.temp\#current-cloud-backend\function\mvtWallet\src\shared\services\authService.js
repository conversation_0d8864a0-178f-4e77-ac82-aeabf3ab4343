const { AWS, ddb } = require('../../config/aws');
const { getTableName } = require('../database/dynamoUtils');
const { ADMIN_ROLES } = require('../constants');

/**
 * Get user ID from GraphQL event
 * @param {object} event - GraphQL event
 * @returns {Promise<string|null>} - User ID or null
 */
async function getUserIdFromEvent(event) {
  try {
    // Extract from Cognito identity context (primary method)
    if (event.identity && event.identity.claims && event.identity.claims.sub) {
      return event.identity.claims.sub;
    }

    // Fallback: direct identity sub
    if (event.identity && event.identity.sub) {
      return event.identity.sub;
    }

    // Extract from request context if available
    if (
      event.requestContext &&
      event.requestContext.identity &&
      event.requestContext.identity.cognitoIdentityId
    ) {
      return event.requestContext.identity.cognitoIdentityId;
    }

    return null;
  } catch (error) {
    console.error("Error getting user ID from event:", error);
    return null;
  }
}

/**
 * Check if user has admin authorization
 * @param {object} event - GraphQL event
 * @returns {Promise<boolean>} - True if user is admin
 */
async function checkAdminAuthorization(event) {
  try {
    const userId = await getUserIdFromEvent(event);
    if (!userId) {
      return false;
    }

    // Query user from DynamoDB by cognitoId field (not primary key)
    const params = {
      TableName: getTableName("User"),
      FilterExpression: "cognitoId = :cognitoId",
      ExpressionAttributeValues: {
        ":cognitoId": { S: userId }
      }
    };

    const result = await ddb.scan(params).promise();

    if (!result.Items || result.Items.length === 0) {
      console.log('No user found with cognitoId:', userId);
      return false;
    }

    // Get the first (and should be only) user with this cognitoId
    const user = AWS.DynamoDB.Converter.unmarshall(result.Items[0]);
    const userRole = user.role;
    const assignedRole = user.assignedRole;

    // Check if user has admin privileges
    const isAdmin =
      userRole === ADMIN_ROLES.SUPER_ADMIN ||
      userRole === ADMIN_ROLES.STAFF_MEMBER ||
      assignedRole === ADMIN_ROLES.SUPER_ADMIN ||
      assignedRole === ADMIN_ROLES.STAFF_MEMBER;
    
    return isAdmin;
  } catch (error) {
    console.error("Error checking admin authorization:", error);
    return false;
  }
}

/**
 * Get current user's database ID (not Cognito ID)
 * @param {object} event - GraphQL event
 * @returns {Promise<string|null>} - Database user ID or null
 */
async function getCurrentUserDatabaseId(event) {
  try {
    const cognitoId = await getUserIdFromEvent(event);
    if (!cognitoId) {
      return null;
    }

    // Query user from DynamoDB by cognitoId field
    const params = {
      TableName: getTableName("User"),
      FilterExpression: "cognitoId = :cognitoId",
      ExpressionAttributeValues: {
        ":cognitoId": { S: cognitoId }
      }
    };

    const result = await ddb.scan(params).promise();

    if (!result.Items || result.Items.length === 0) {
      console.log('No user found with cognitoId:', cognitoId);
      return null;
    }

    // Get the first (and should be only) user with this cognitoId
    const user = AWS.DynamoDB.Converter.unmarshall(result.Items[0]);
    return user.id; // Return the database ID
  } catch (error) {
    console.error("Error getting current user database ID:", error);
    return null;
  }
}

/**
 * Check user authorization for specific resource
 * @param {object} event - GraphQL event
 * @param {string} requestedUserId - Requested user ID
 * @returns {Promise<boolean>} - True if authorized
 */
async function checkUserAuthorization(event, requestedUserId) {
  try {
    const currentCognitoId = await getUserIdFromEvent(event);
    if (!currentCognitoId) {
      return false;
    }

    // Get current user's database ID
    const currentUserDatabaseId = await getCurrentUserDatabaseId(event);

    if (!currentUserDatabaseId) {
      console.log('Could not find current user database ID');
      return false;
    }

    // Users can access their own data (compare database IDs)
    if (currentUserDatabaseId === requestedUserId) {
      return true;
    }

    // Check if current user is admin for accessing other user's data
    return await checkAdminAuthorization(event);
  } catch (error) {
    console.error("Error checking user authorization:", error);
    return false;
  }
}

/**
 * Get user by ID from database
 * @param {string} userId - User ID
 * @returns {Promise<object|null>} - User object or null
 */
async function getUserById(userId) {
  try {
    const params = {
      TableName: getTableName("User"),
      Key: {
        id: { S: userId }
      }
    };

    const result = await ddb.getItem(params).promise();

    if (result.Item) {
      return AWS.DynamoDB.Converter.unmarshall(result.Item);
    }

    return null;
  } catch (error) {
    console.error("Error getting user by ID:", error);
    return null;
  }
}

module.exports = {
  getUserIdFromEvent,
  checkAdminAuthorization,
  getCurrentUserDatabaseId,
  checkUserAuthorization,
  getUserById
};
