/**
 * Shared Mock Data for MVT Wallet Tests
 * Provides reusable mock data objects for both scenario and integration tests
 */

// Mock constants
const MOCK_CONSTANTS = {
  CENTRAL_WALLET_ID: 'central-mvt-wallet',
  TOKEN_TYPES: {
    MVT: 'MVT',
    USDC: 'USDC'
  },
  TRANSACTION_TYPES: {
    ADMIN_MINT: 'ADMIN_MINT',
    CENTRAL_TO_USER_TRANSFER: 'CENTRAL_TO_USER_TRANSFER',
    USER_TO_USER_TRANSFER: 'USER_TO_USER_TRANSFER',
    USDC_DEPOSIT: 'USDC_DEPOSIT',
    USDC_WITHDRAWAL: 'USDC_WITHDRAWAL',
    SWAP_REQUEST: 'SWAP_REQUEST'
  },
  TRANSACTION_STATUS: {
    COMPLETED: 'COMPLETED',
    PENDING: 'PENDING',
    FAILED: 'FAILED'
  },
  SWAP_STATUS: {
    PENDING: 'PENDING',
    APPROVED: 'APPROVED',
    REJECTED: 'REJECTED'
  },
  STATUS_CODES: {
    SUCCESS: 200,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_ERROR: 500
  }
};

// Mock user data
const createMockUser = (userId = 'test-user-123', isAdmin = false) => ({
  id: userId,
  cognitoId: isAdmin ? 'admin-cognito-id' : 'test-cognito-id',
  email: isAdmin ? '<EMAIL>' : '<EMAIL>',
  firstName: isAdmin ? 'Admin' : 'Test',
  lastName: 'User',
  walletAddress: isAdmin ? null : '******************************************',
  role: isAdmin ? 'SUPER_ADMIN' : 'MEMBER',
  isDeleted: 'false',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
});

// Mock wallet balance data
const createMockWalletBalance = (balance = 1000, lockedBalance = 0) => ({
  id: 'user-wallet-test-user-123',
  userId: 'test-user-123',
  mvtBalance: balance,
  lockedMVT: lockedBalance,
  availableBalance: balance - lockedBalance,
  pendingBalance: 0,
  totalReceived: balance,
  totalSent: 0,
  lastUpdated: '2024-01-01T00:00:00.000Z',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
});

// Mock central wallet data
const createMockCentralWallet = (balance = 50000, lockedBalance = 0) => ({
  id: 'central-mvt-wallet',
  mvtBalance: balance,
  lockedMVT: lockedBalance,
  totalMinted: 100000,
  totalTransferred: 50000,
  lastMintedAt: '2024-01-01T00:00:00.000Z',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
});

// Mock transaction data
const createMockTransaction = (transactionType = 'USER_TO_USER_TRANSFER', amount = 100) => ({
  id: 'transaction-123',
  transactionType,
  amount,
  status: 'COMPLETED',
  fromUserId: 'sender-123',
  toUserId: 'recipient-123',
  fromWalletId: 'user-wallet-sender-123',
  toWalletId: 'user-wallet-recipient-123',
  description: 'Test transaction',
  tokenType: 'MVT',
  transactionHash: null,
  blockNumber: null,
  gasUsed: null,
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
});

// Mock swap request data
const createMockSwapRequest = (id = 'test-swap-123', status = 'PENDING') => ({
  id,
  userId: 'test-user-123',
  mvtAmount: 100,
  usdcAmount: 50.0,
  exchangeRate: 0.5,
  status,
  userWalletAddress: '******************************************',
  description: 'Test swap request',
  requestedAt: '2024-01-01T00:00:00.000Z',
  processedAt: status !== 'PENDING' ? '2024-01-01T01:00:00.000Z' : null,
  adminUserId: status !== 'PENDING' ? 'admin-user-123' : null,
  transactionHash: status === 'APPROVED' ? '0xabcdef123456' : null,
  rejectionReason: status === 'REJECTED' ? 'Test rejection reason' : null,
  isDeleted: 'false',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
});

// Mock exchange rate data
const createMockExchangeRate = () => ({
  usdcReserves: 10000.0,
  mvtReserves: 20000.0,
  baseRate: 0.5,
  rate: 0.49,
  safetyBuffer: 0.98,
  lastUpdated: '2024-01-01T00:00:00.000Z'
});

// Mock GraphQL event
const createMockEvent = (cognitoIdentityId = 'test-cognito-id', isAdmin = false) => ({
  requestContext: {
    identity: {
      cognitoIdentityId
    }
  },
  arguments: {},
  info: {
    fieldName: 'testField'
  },
  source: {},
  stateValues: {},
  prev: null
});

// Mock GraphQL arguments
const createMockArgs = (input = {}) => ({
  input
});

// Mock DynamoDB responses
const mockDynamoDBSuccess = (data = {}) => ({
  promise: jest.fn().mockResolvedValue(data)
});

const mockDynamoDBError = (error = new Error('DynamoDB error')) => ({
  promise: jest.fn().mockRejectedValue(error)
});

// Mock blockchain transaction responses
const createMockBlockchainTransaction = (type = 'transfer') => {
  const baseTransaction = {
    blockNumber: 12345,
    gasUsed: '21000',
    status: 1
  };

  switch (type) {
    case 'deposit':
      return {
        ...baseTransaction,
        transactionHash: '0xabcdef123456',
        approvalNeeded: false,
        approvalTxHash: null
      };
    case 'withdrawal':
    case 'transfer':
    default:
      return {
        ...baseTransaction,
        transactionHash: '0xfedcba654321'
      };
  }
};

// Mock environment variables
const createMockEnvironment = () => ({
  ETHEREUM_RPC_URL: 'http://localhost:8545',
  PRIVATE_KEY: '******************************************123456789012345678901234',
  MVT_WITHDRAW_CONTRACT_ADDRESS: '******************************************',
  USDC_TOKEN_ADDRESS: '******************************************',
  NODE_ENV: 'test',
  AWS_REGION: 'us-east-1',
  ENVIRONMENT: 'test'
});

// Mock DynamoDB marshalled format data
const createMockDynamoDBItem = (data) => {
  const marshalled = {};
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      marshalled[key] = { S: value };
    } else if (typeof value === 'number') {
      marshalled[key] = { N: value.toString() };
    } else if (typeof value === 'boolean') {
      marshalled[key] = { BOOL: value };
    } else {
      marshalled[key] = { S: JSON.stringify(value) };
    }
  }
  return marshalled;
};

// Test data collections
const TEST_USERS = {
  REGULAR_USER: createMockUser('test-user-123', false),
  ADMIN_USER: createMockUser('admin-user-123', true),
  RECIPIENT_USER: createMockUser('recipient-user-456', false),
  SENDER_USER: createMockUser('sender-user-789', false)
};

const TEST_WALLETS = {
  CENTRAL: createMockCentralWallet(50000, 0),
  USER_WITH_BALANCE: createMockWalletBalance(1000, 0),
  USER_WITH_LOCKED: createMockWalletBalance(1000, 200),
  EMPTY_USER: createMockWalletBalance(0, 0)
};

const TEST_TRANSACTIONS = {
  MINT: createMockTransaction('ADMIN_MINT', 1000),
  TRANSFER: createMockTransaction('USER_TO_USER_TRANSFER', 500),
  ADMIN_TRANSFER: createMockTransaction('CENTRAL_TO_USER_TRANSFER', 1000)
};

const TEST_SWAP_REQUESTS = {
  PENDING: createMockSwapRequest('swap-pending-123', 'PENDING'),
  APPROVED: createMockSwapRequest('swap-approved-123', 'APPROVED'),
  REJECTED: createMockSwapRequest('swap-rejected-123', 'REJECTED')
};

module.exports = {
  // Constants
  MOCK_CONSTANTS,
  
  // Factory functions
  createMockUser,
  createMockWalletBalance,
  createMockCentralWallet,
  createMockTransaction,
  createMockSwapRequest,
  createMockExchangeRate,
  createMockEvent,
  createMockArgs,
  createMockBlockchainTransaction,
  createMockEnvironment,
  createMockDynamoDBItem,
  
  // DynamoDB helpers
  mockDynamoDBSuccess,
  mockDynamoDBError,
  
  // Test data collections
  TEST_USERS,
  TEST_WALLETS,
  TEST_TRANSACTIONS,
  TEST_SWAP_REQUESTS
};
