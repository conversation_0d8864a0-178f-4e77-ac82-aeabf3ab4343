/**
 * Test suite to verify GraphQL schema compliance for swap operations
 * Ensures all responses match the expected SwapProcessingResponse structure
 */

const swapHandlers = require('../swap.handlers');
const authService = require('../../../shared/services/authService');
const swapValidation = require('../swap.validation');

// Mock dependencies
jest.mock('../../../shared/services/authService');
jest.mock('../swap.validation');
jest.mock('../swap.service');
jest.mock('../../../shared/utils/logger');

const mockAuthService = authService;
const mockSwapValidation = swapValidation;

describe('Swap GraphQL Schema Compliance', () => {
  const mockEvent = {
    requestContext: {
      identity: {
        cognitoIdentityId: 'test-identity-id'
      }
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('handleApproveMVTWalletSwap', () => {
    const mockArgs = {
      input: {
        swapRequestId: 'swap-123'
      }
    };

    test('should return proper SwapProcessingResponse structure for authorization failure', async () => {
      // Arrange
      mockAuthService.checkAdminAuthorization.mockResolvedValue(false);

      // Act
      const result = await swapHandlers.handleApproveMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result).toHaveProperty('statusCode');
      expect(result).toHaveProperty('message');
      expect(result).toHaveProperty('data');
      
      expect(typeof result.statusCode).toBe('number');
      expect(typeof result.message).toBe('string');
      expect(result.data).toBeNull();
      
      expect(result.statusCode).toBe(403);
      expect(result.message).toContain('Unauthorized');
    });

    test('should return proper SwapProcessingResponse structure for validation failure', async () => {
      // Arrange
      mockAuthService.checkAdminAuthorization.mockResolvedValue(true);
      mockSwapValidation.validateSwapApprovalInput.mockReturnValue({
        isValid: false,
        error: 'Invalid swap request ID'
      });

      // Act
      const result = await swapHandlers.handleApproveMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result).toHaveProperty('statusCode');
      expect(result).toHaveProperty('message');
      expect(result).toHaveProperty('data');
      
      expect(typeof result.statusCode).toBe('number');
      expect(typeof result.message).toBe('string');
      expect(result.data).toBeNull();
      
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('Invalid input');
    });

    test('should return proper SwapProcessingResponse structure for missing admin user', async () => {
      // Arrange
      mockAuthService.checkAdminAuthorization.mockResolvedValue(true);
      mockSwapValidation.validateSwapApprovalInput.mockReturnValue({
        isValid: true
      });
      mockAuthService.getCurrentUserDatabaseId.mockResolvedValue(null);

      // Act
      const result = await swapHandlers.handleApproveMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result).toHaveProperty('statusCode');
      expect(result).toHaveProperty('message');
      expect(result).toHaveProperty('data');
      
      expect(typeof result.statusCode).toBe('number');
      expect(typeof result.message).toBe('string');
      expect(result.data).toBeNull();
      
      expect(result.statusCode).toBe(403);
      expect(result.message).toContain('Admin user not found');
    });
  });

  describe('handleRejectMVTWalletSwap', () => {
    const mockArgs = {
      input: {
        swapRequestId: 'swap-123',
        rejectionReason: 'Test rejection'
      }
    };

    test('should return proper SwapProcessingResponse structure for authorization failure', async () => {
      // Arrange
      mockAuthService.checkAdminAuthorization.mockResolvedValue(false);

      // Act
      const result = await swapHandlers.handleRejectMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result).toHaveProperty('statusCode');
      expect(result).toHaveProperty('message');
      expect(result).toHaveProperty('data');
      
      expect(typeof result.statusCode).toBe('number');
      expect(typeof result.message).toBe('string');
      expect(result.data).toBeNull();
      
      expect(result.statusCode).toBe(403);
      expect(result.message).toContain('Unauthorized');
    });

    test('should return proper SwapProcessingResponse structure for validation failure', async () => {
      // Arrange
      mockAuthService.checkAdminAuthorization.mockResolvedValue(true);
      mockSwapValidation.validateSwapRejectionInput.mockReturnValue({
        isValid: false,
        error: 'Missing rejection reason'
      });

      // Act
      const result = await swapHandlers.handleRejectMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result).toHaveProperty('statusCode');
      expect(result).toHaveProperty('message');
      expect(result).toHaveProperty('data');
      
      expect(typeof result.statusCode).toBe('number');
      expect(typeof result.message).toBe('string');
      expect(result.data).toBeNull();
      
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('Invalid input');
    });
  });

  describe('Error Response Structure Validation', () => {
    test('should ensure all error responses match SwapProcessingResponse schema', () => {
      // Define the expected schema structure
      const expectedSchema = {
        statusCode: 'number',
        message: 'string',
        data: 'object' // Can be null or SwapProcessingResult
      };

      // Test various error scenarios
      const errorScenarios = [
        { statusCode: 400, message: 'Bad Request', data: null },
        { statusCode: 403, message: 'Forbidden', data: null },
        { statusCode: 404, message: 'Not Found', data: null },
        { statusCode: 500, message: 'Internal Server Error', data: null }
      ];

      errorScenarios.forEach(scenario => {
        // Verify each response matches the schema
        expect(typeof scenario.statusCode).toBe(expectedSchema.statusCode);
        expect(typeof scenario.message).toBe(expectedSchema.message);
        expect(scenario.data === null || typeof scenario.data === 'object').toBe(true);
        
        // Verify required fields are present
        expect(scenario).toHaveProperty('statusCode');
        expect(scenario).toHaveProperty('message');
        expect(scenario).toHaveProperty('data');
        
        // Verify statusCode is a valid HTTP status code
        expect(scenario.statusCode).toBeGreaterThanOrEqual(200);
        expect(scenario.statusCode).toBeLessThan(600);
        
        // Verify message is not empty
        expect(scenario.message.length).toBeGreaterThan(0);
      });
    });

    test('should ensure success responses match SwapProcessingResponse schema', () => {
      const successResponse = {
        statusCode: 200,
        message: 'Swap approved successfully',
        data: {
          success: true,
          swapRequestId: 'swap-123',
          mvtAmount: 100,
          usdcAmount: 50,
          userWalletAddress: '0x123...',
          transactionHash: '0xabc...',
          newUserBalance: 900,
          processedAt: '2024-01-01T00:00:00.000Z',
          status: 'APPROVED'
        }
      };

      // Verify response structure
      expect(typeof successResponse.statusCode).toBe('number');
      expect(typeof successResponse.message).toBe('string');
      expect(typeof successResponse.data).toBe('object');
      expect(successResponse.data).not.toBeNull();
      
      // Verify data structure matches SwapProcessingResult
      expect(typeof successResponse.data.success).toBe('boolean');
      expect(typeof successResponse.data.swapRequestId).toBe('string');
      expect(typeof successResponse.data.mvtAmount).toBe('number');
      expect(typeof successResponse.data.usdcAmount).toBe('number');
      expect(typeof successResponse.data.status).toBe('string');
    });
  });

  describe('GraphQL Non-Nullable Field Compliance', () => {
    test('should never return null for statusCode field', async () => {
      // Test various error conditions to ensure statusCode is never null
      mockAuthService.checkAdminAuthorization.mockResolvedValue(false);
      
      const result = await swapHandlers.handleApproveMVTWalletSwap(mockEvent, {
        input: { swapRequestId: 'test' }
      });
      
      expect(result.statusCode).not.toBeNull();
      expect(result.statusCode).not.toBeUndefined();
      expect(typeof result.statusCode).toBe('number');
    });

    test('should never return null for message field', async () => {
      // Test various error conditions to ensure message is never null
      mockAuthService.checkAdminAuthorization.mockResolvedValue(false);
      
      const result = await swapHandlers.handleApproveMVTWalletSwap(mockEvent, {
        input: { swapRequestId: 'test' }
      });
      
      expect(result.message).not.toBeNull();
      expect(result.message).not.toBeUndefined();
      expect(typeof result.message).toBe('string');
      expect(result.message.length).toBeGreaterThan(0);
    });

    test('should allow null for data field in error responses', async () => {
      // Test that data can be null for error responses (this is allowed by schema)
      mockAuthService.checkAdminAuthorization.mockResolvedValue(false);
      
      const result = await swapHandlers.handleApproveMVTWalletSwap(mockEvent, {
        input: { swapRequestId: 'test' }
      });
      
      expect(result.data).toBeNull();
    });
  });
});
