{"version": "1", "cognitoConfig": {"identityPoolName": "myvillageprojectadmifeb4ea87_identitypool_feb4ea87", "allowUnauthenticatedIdentities": true, "resourceNameTruncated": "myvillfeb4ea87", "userPoolName": "myvillageprojectadmifeb4ea87_userpool_feb4ea87", "autoVerifiedAttributes": ["email"], "mfaConfiguration": "OFF", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Your verification code", "emailVerificationMessage": "Your verification code is {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": [], "requiredAttributes": ["email", "family_name", "given_name", "name", "phone_number"], "aliasAttributes": [], "userpoolClientGenerateSecret": false, "userpoolClientRefreshTokenValidity": 30, "userpoolClientWriteAttributes": ["email"], "userpoolClientReadAttributes": ["email"], "userpoolClientLambdaRole": "myvillfeb4ea87_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "sharedId": "feb4ea87", "resourceName": "myvillageprojectadmifeb4ea87", "authSelections": "identityPoolAndUserPool", "useDefault": "default", "usernameAttributes": ["email"], "triggers": {}, "userPoolGroupList": [], "serviceName": "Cognito", "usernameCaseSensitive": false, "useEnabledMfas": true, "authRoleArn": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}, "unauthRoleArn": {"Fn::GetAtt": ["UnauthRole", "<PERSON><PERSON>"]}, "breakCircularDependency": true, "dependsOn": []}}