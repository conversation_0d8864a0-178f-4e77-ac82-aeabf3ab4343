/* Amplify Params - DO NOT EDIT
  API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT
  API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT
  AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID
  ENV
  REGION
Amplify Params - DO NOT EDIT */

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

const AWS = require("aws-sdk");
const uuid = require("uuid");
const R = require("ramda");
AWS.config.update({
  maxRetries: 3,
  httpOptions: { timeout: 30000, connectTimeout: 5000 },
  region: process.env.REGION,
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY,
});
const USERPOOLID = process.env.AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID;
const ddb = new AWS.DynamoDB();
const lambda = new AWS.Lambda();

/*Initializing CognitoIdentityServiceProvider from AWS SDK JS*/
const cognito = new AWS.CognitoIdentityServiceProvider({
  apiVersion: "2016-04-18",
});

exports.handler = async (event, context, callback) => {
  try {
    let CITYID = "";
    let userID = "";
    let date = new Date();
    switch (event.fieldName) {
      case "getCardsData": {
        try {
          console.log("getCardsData");
          console.log("event.arguments?", event.arguments);

          const { memberId, type, cityId } = event.arguments;

          // Fetch points, microcredentials, and categories data in parallel for all cities
          const [pointsData, microcredentialData, categoriesData] =
            await Promise.all([
              Promise.all(cityId.map((city) => fetchPointsData(city))),
              Promise.all(cityId.map((city) => fetchMicrocredentialData(city))),
              fetchCategoriesData(), // Only fetch once
            ]);

          // Flatten and merge the results from all cities
          const pointRes = pointsData.flat();
          const microcredentialRes = microcredentialData.flat();

          // Get associated member IDs
          const associationResponse = await getAssociatedIds(memberId);
          let memberIds = [
            ...new Set(
              associationResponse.map((ex) => Object.values(ex)).flat()
            ),
          ].filter((value) => value !== null && value !== "null");

          memberIds = memberIds.length === 0 ? [memberId] : memberIds;
          console.log("memberIds", memberIds);

          // Filter points data based on associated member IDs only once
          const assocationPointsData = pointRes.filter((ex) =>
            memberIds.includes(ex.memberId)
          );

          // Pre-build categories mapping for efficient access
          const categoriesObj = categoriesData.reduce((obj, ex) => {
            obj[ex.id] = ex.name;
            return obj;
          }, {});

          const categoryNames = {
            cardOne: categoriesObj["7f5bf56a-8f4e-4588-966b-ae23d21e8442"],
            cardTwo: categoriesObj["1e50486b-09a9-4bb6-a493-6986c72404a8"],
            cardThree: categoriesObj["88951680-389f-4a57-8864-46a881f6efd1"],
            cardFour: categoriesObj["8be11523-2614-46c5-9ade-1fcce05b10a6"],
            cardFive: categoriesObj["ce981ce0-7e43-42ef-8a9a-c0492f861d85"],
          };

          const categorySubName = {
            cardOne: "Students Are Prepared For New Economy",
            cardTwo: "Communities Is Healthy And Connected",
            cardThree: "Communities Are Represented In Leadership",
            cardFour: "Communities Actively Celebrate Local History",
            cardFive: "Communities Are Self-Sustaining And Wealthy",
          };

          const cardRes = await Promise.all(
            Object.keys(categoryNames).map(async (key) => {
              const type = categoryNames[key];

              // Filter once for each category type
              const filteredMicrocredentials = microcredentialRes.filter(
                (record) => record.type === type
              );
              const filteredPoints = pointRes.filter(
                (record) => record.category === type
              );
              const filteredAssociationPoints = assocationPointsData.filter(
                (record) => record.category === type
              );

              // Calculate city and member points once for each category
              const cityPoints = calculateCityPoints(filteredPoints);
              const memberPoints = calculateCityPoints(
                filteredAssociationPoints
              );

              // Fetch assignments for all microcredentials in parallel
              const allAssignments = await Promise.all(
                filteredMicrocredentials.map((micro) =>
                  fetchAssignmentsByMicrocredentialId(micro.id, type, memberId)
                )
              );

              return {
                microcredential: allAssignments.flat(),
                categoryName: type,
                categorySubName: categorySubName[key],
                myVillageScore: cityPoints,
                communityScore: memberPoints,
              };
            })
          );

          console.log("cardRes", cardRes);
          callback(null, cardRes);
        } catch (error) {
          const errorRes = createErrorResponse(error);
          callback(errorRes);
        }
        break;
      }
      case "getMembershipData": {
        console.log("getMembershipData");
        const { cityId, userId, userType } = event.arguments;

        const scanParams = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
            ":c": { S: cityId },
            ":u": { S: userId },
          },
          FilterExpression: "#D = :d and #C = :c and #U <> :u",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#U": "personsID",
            "#C": "cityId",
          },
          TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };
        const scanResponse = await ddb.scan(scanParams).promise();
        let records = scanResponse.Items.map((item) =>
          AWS.DynamoDB.Converter.unmarshall(item)
        );

        if (userType != "SUPER_ADMIN") {
          const userParams = {
            ExpressionAttributeValues: {
              ":d": { S: "false" },
            },
            FilterExpression: "#D = :d",
            ExpressionAttributeNames: {
              "#D": "isDeleted",
            },
            ProjectionExpression: "id, isStakeholder, isDeleted",
            TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          };
          const userResponse = await ddb.scan(userParams).promise();
          const userRecords = userResponse.Items.map((item) =>
            AWS.DynamoDB.Converter.unmarshall(item)
          );
          const userStudent = userRecords
            .filter((res) => !res.isStakeholder)
            .map((res) => res.id);
          console.log("userStudent", userStudent);

          records = records.filter((ex) => !userStudent.includes(ex.personsID));
          console.log("records", records);
        }
        const sortAndSlice = (arr, key, num) =>
          arr
            .slice()
            .sort((a, b) => parseFloat(b[key]) - parseFloat(a[key]))
            .slice(0, num);
        const sortByDate = (arr, key) =>
          arr.slice().sort((a, b) => new Date(b[key]) - new Date(a[key]));

        const newResponse = {
          recentMembers: sortByDate(records, "createdAt").slice(0, 2),
          impactLeaders: sortAndSlice(records, "currentImpactScore", 3),
          growingImpact: sortAndSlice(records, "lastAddedImpactScore", 2),
          members: sortByDate(records, "createdAt"),
        };

        callback(null, newResponse);
        break;
      }
      case "getRecentActivity":
        console.log("getRecentActivity");
        CITYID = event.arguments?.cityId;
        userID = event.arguments?.userId;

        var associationPara = {
          ExpressionAttributeValues: {
            ":u": { S: userID },
            ":t": { S: "Person" },
          },
          FilterExpression: "(#P = :u or #OP = :u) and #T = :t",
          ExpressionAttributeNames: {
            "#P": "personsID",
            "#OP": "otherPersonsID",
            "#T": "type",
          },
          ProjectionExpression:
            "personsID, otherPersonsID, organizationID, businessID",
          TableName:
            "Association-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        let associationRes = await ddb.scan(associationPara).promise();
        let associationNewRes = associationRes.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );

        let idsArray = [];
        await associationNewRes.map((rec) => {
          rec.otherPersonsID != "null" && rec.otherPersonsID != null
            ? idsArray.push(rec.otherPersonsID)
            : "";
          rec.businessID != "null" && rec.businessID != null
            ? idsArray.push(rec.businessID)
            : "";
          rec.personsID != "null" && rec.personsID != null
            ? idsArray.push(rec.personsID)
            : "";
          rec.organizationID != "null" && rec.organizationID != null
            ? idsArray.push(rec.organizationID)
            : "";
        });
        let uniqueIdsArray = idsArray.filter(function (elem, pos) {
          return idsArray.indexOf(elem) == pos;
        });
        console.log("uniqueIdsArray", uniqueIdsArray);

        const idsObject = {};
        var index = 0;
        uniqueIdsArray.forEach(function (value) {
          index++;
          var idKey = ":id" + index;
          idsObject[idKey.toString()] = { S: value };
        });
        console.log("idsObject", idsObject);

        let expressionAttributeValuesObj = {};
        expressionAttributeValuesObj = JSON.parse(JSON.stringify(idsObject));
        expressionAttributeValuesObj[":d"] = { S: "false" };
        expressionAttributeValuesObj[":c"] = { S: CITYID };
        console.log("idsObject133", idsObject);

        var params = {
          ExpressionAttributeValues: expressionAttributeValuesObj,
          FilterExpression:
            "((#D = :d and #C = :c) and (#M IN (" +
            Object.keys(idsObject).toString() +
            ") or #R IN (" +
            Object.keys(idsObject).toString() +
            ")))",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#M": "moduleId",
            "#R": "relatedId",
            "#C": "cityId",
          },
          TableName:
            "Activity-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        console.log("params", params);
        const getRecentActivityResponse = await ddb.scan(params).promise();
        const getRecentActivityNewResponse =
          getRecentActivityResponse.Items.map((records) =>
            AWS.DynamoDB.Converter.unmarshall(records)
          );

        const getRecentActivityArr = getRecentActivityNewResponse;
        getRecentActivityArr.sort(function (a, b) {
          return new Date(b.createdAt) - new Date(a.createdAt);
        });
        const newRecentActivityArr = getRecentActivityArr.slice(0, 3);

        let getRecentActivityResponce = {
          data: newRecentActivityArr,
        };

        callback(null, getRecentActivityResponce);
        break;
      case "updatePostViewsData":
        console.log("updatePostViewsData");
        const viewUserId = event.arguments?.viewUserId;
        const postId = event.arguments?.postId;

        // Check postviews table for data already exist or not
        var viewsParams = {
          ExpressionAttributeValues: {
            ":v": { S: viewUserId },
            ":p": { S: postId },
          },
          FilterExpression: "#V = :v and #P = :p",
          ExpressionAttributeNames: {
            "#V": "viewUserId",
            "#P": "postId",
          },
          TableName:
            "PostViews-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        const getviewsResponse = await ddb.scan(viewsParams).promise();
        const viewResponseCount = getviewsResponse?.Count ?? 1;

        // if data are not in postview table then
        if (viewResponseCount === 0) {
          let postViewsId = uuid.v4();
          let pointId = uuid.v4();

          // Create new postviews item
          let postViewsParams = {
            Item: {
              id: {
                S: postViewsId,
              },
              __typename: { S: "PostViews" },
              viewUserId: {
                S: viewUserId,
              },
              postId: {
                S: postId,
              },
              createdAt: { S: date.toISOString() },
              updatedAt: { S: date.toISOString() },
              _version: { N: "1" },
              _lastChangedAt: { N: date.getTime().toString() },
            },
            TableName:
              "PostViews-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
          };
          await ddb.putItem(postViewsParams).promise();

          // Update view count in post
          var params = {
            TableName:
              "Submission-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            Key: {
              id: { S: postId },
            },
            ExpressionAttributeNames: {
              "#VC": "viewCount",
            },
            ExpressionAttributeValues: {
              ":vc": {
                N: "1",
              },
            },
            ReturnValues: "ALL_NEW",
            UpdateExpression: "SET #VC = #VC + :vc",
          };
          let updatePostData = await ddb.updateItem(params).promise();
          const updatedPost = AWS.DynamoDB.Converter.unmarshall(
            updatePostData["Attributes"]
          );

          let memberId = updatedPost?.memberId ?? "";
          let categoryType = updatedPost?.categoryType ?? "";
          let pointType = updatedPost?.projectType ?? "";
          var moduleImageUrl = "";
          if (updatedPost?.images[0]) {
            moduleImageUrl =
              "post/" + updatedPost?.id + "/images/" + updatedPost?.images[0];
          } else if (updatedPost?.[0]) {
            moduleImageUrl =
              "post/" + updatedPost?.id + "/videos/" + updatedPost?.videos[0];
          }

          // Create activity
          let postViewActivityParams = {
            Item: {
              id: {
                S: uuid.v4(),
              },
              __typename: { S: "Activity" },
              activityType: {
                S: "STORYTELLING",
              },
              cityId: {
                S: updatedPost?.cityId,
              },
              createdUserId: {
                S: viewUserId,
              },
              createdUserName: {
                S: "",
              },
              isDeleted: {
                S: "false",
              },
              moduleId: {
                S: updatedPost?.id,
              },
              moduleImageUrl: {
                S: moduleImageUrl,
              },
              moduleName: {
                S: updatedPost?.text,
              },
              moduleType: {
                S: "story",
              },
              relatedId: {
                S: updatedPost?.memberId,
              },
              relatedTo: {
                S: updatedPost?.projectType,
              },
              type: {
                S: "VIEWED",
              },
              createdAt: { S: date.toISOString() },
              updatedAt: { S: date.toISOString() },
              _version: { N: "1" },
              _lastChangedAt: { N: date.getTime().toString() },
            },
            TableName:
              "Activity-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
          };
          await ddb.putItem(postViewActivityParams).promise();

          if (memberId && memberId != "") {
            // Add new points for story view
            let PointsParams = {
              Item: {
                id: {
                  S: pointId,
                },
                __typename: { S: "Points" },
                category: {
                  S: categoryType,
                },
                cityId: {
                  S: "",
                },
                createdBy: {
                  S: viewUserId,
                },
                impactScore: {
                  S: "0.4",
                },
                MVPTokens: {
                  N: "0.1",
                },
                isDeleted: {
                  S: "false",
                },
                memberId: {
                  S: memberId,
                },
                pointType: {
                  S: "story",
                },
                type: {
                  S: pointType,
                },
                status: {
                  S: "CREDITED",
                },
                createdAt: { S: date.toISOString() },
                updatedAt: { S: date.toISOString() },
                _version: { N: "1" },
                _lastChangedAt: { N: date.getTime().toString() },
              },
              TableName:
                "Points-" +
                process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
                "-" +
                process.env.ENV,
            };
            await ddb.putItem(PointsParams).promise();

            // Get membership data Id
            var membershipParams = {
              ExpressionAttributeValues: {
                ":b": { S: memberId },
                ":o": { S: memberId },
                ":p": { S: memberId },
              },
              FilterExpression: "#B = :b or #O = :o or #P = :p",
              ExpressionAttributeNames: {
                "#B": "businessID",
                "#O": "organizationID",
                "#P": "personsID",
              },
              TableName:
                "Membership-" +
                process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
                "-" +
                process.env.ENV,
            };
            const getmembershipResponse = await ddb
              .scan(membershipParams)
              .promise();
            let getmembershipResponseArr =
              await getmembershipResponse.Items.map((records) =>
                AWS.DynamoDB.Converter.unmarshall(records)
              );
            let getMembershipIdData = getmembershipResponseArr[0];
            console.log("getMembershipIdData", getMembershipIdData);
            let membershipId = getMembershipIdData?.id ?? "";
            let currentImpactScore =
              getMembershipIdData?.currentImpactScore ?? 0;
            let MVPTokens = getMembershipIdData?.MVPTokens ?? 0;
            let updatedScore =
              currentImpactScore && currentImpactScore != null
                ? parseFloat(currentImpactScore) + 0.4
                : 0.4;
            let updatedMVPTokens =
              MVPTokens && MVPTokens != null
                ? parseFloat(MVPTokens) + 0.1
                : 0.1;

            if (membershipId && membershipId != "") {
              // Update membership data current impact score
              var membershipUpdateParams = {
                TableName:
                  "Membership-" +
                  process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
                  "-" +
                  process.env.ENV,
                Key: {
                  id: { S: membershipId },
                },
                ExpressionAttributeNames: {
                  "#CIS": "currentImpactScore",
                  "#MVP": "MVPTokens",
                },
                ExpressionAttributeValues: {
                  ":cis": {
                    S: updatedScore.toString(),
                  },
                  ":mvp": {
                    N: updatedMVPTokens.toString(),
                  },
                },
                ReturnValues: "ALL_NEW",
                UpdateExpression: "SET #CIS = :cis, #MVP = :mvp",
              };
              await ddb.updateItem(membershipUpdateParams).promise();
            }
          }
        }

        callback(null, { message: "Stories data updated.", statusCode: 200 });
        break;
      case "updatePostLikesData":
        console.log("updatePostLikesData");
        const likeUserId = event.arguments?.likeUserId;
        console.log('likeUserId: ', likeUserId);
        const submissionPostId = event.arguments?.postId;
        console.log('submissionPostId: ', submissionPostId);
        const isPostLike = event.arguments?.isPostLike;

        // Check post likes table for data already exist or not
        var postLikeParams = {
          ExpressionAttributeValues: {
            ":v": { S: likeUserId },
            ":p": { S: submissionPostId },
          },
          FilterExpression: "#V = :v and #P = :p",
          ExpressionAttributeNames: {
            "#V": "likeUserId",
            "#P": "postId",
          },
          TableName:
            "PostLikes-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        const getLikesResponse = await ddb.scan(postLikeParams).promise();
        console.log('getLikesResponse: ', getLikesResponse);
        const likeResponseCount = getLikesResponse?.Count ?? 1;
        console.log('likeResponseCount: ', likeResponseCount);

        // if data are not in post like table then
        if (likeResponseCount === 0 && isPostLike) {
          let postLikesId = uuid.v4();

          // Create new post likes item
          let postLikesParams = {
            Item: {
              id: {
                S: postLikesId,
              },
              __typename: { S: "PostLikes" },
              likeUserId: {
                S: likeUserId,
              },
              postId: {
                S: submissionPostId,
              },
              createdAt: { S: date.toISOString() },
              updatedAt: { S: date.toISOString() },
              _version: { N: "1" },
              _lastChangedAt: { N: date.getTime().toString() },
            },
            TableName:
              "PostLikes-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
          };
          await ddb.putItem(postLikesParams).promise();

          // Update view count in post
          var params = {
            TableName:
              "Submission-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            Key: {
              id: { S: submissionPostId },
            },
            ExpressionAttributeNames: {
              "#LC": "likeCount",
            },
            ExpressionAttributeValues: {
              ":one": {
                N: "1",
              },
              ":zero": {
                N: "0",
              },
            },
            ReturnValues: "ALL_NEW",
            UpdateExpression: "SET #LC = if_not_exists(#LC, :zero) + :one",
          };
          console.log('params: ', params);
          let updatePostData = await ddb.updateItem(params).promise();
          console.log('updatePostData: ', updatePostData);

        } else if (likeResponseCount === 1 && !isPostLike) {
          // Delete post likes item
          let deletePostLikesParams = {
            Key: {
              id: {
                S: getLikesResponse.Items[0].id.S,
              },
            },
            TableName:
              "PostLikes-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
          };
          await ddb.deleteItem(deletePostLikesParams).promise();

          // Update view count in post
          var params = {
            TableName:
              "Submission-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            Key: {
              id: { S: submissionPostId },
            },
            ExpressionAttributeNames: {
              "#LC": "likeCount",
            },
            ExpressionAttributeValues: {
              ":one": {
                N: "-1",
              },
              ":zero": {
                N: "0",
              },
            },
            ReturnValues: "ALL_NEW",
            UpdateExpression: "SET #LC = if_not_exists(#LC, :zero) + :one",
          };
          console.log('params: ', params);
          let updatePostData = await ddb.updateItem(params).promise();
          console.log('updatePostData: ', updatePostData);
        }

        callback(null, { message: "Stories data updated.", statusCode: 200 });
        break;
      case "updateFCMToken":
        console.log("updateFCMToken");
        console.log("event.arguments", event.arguments);
        const sns = new AWS.SNS();
        const updateUserID = event.arguments?.userId ?? "";
        const { deviceId, FCMToken, loginPlatform } = event.arguments;
        var platform_arn = process.env.PLATFORM_ARN;
        let endpointArn = "";

        if (updateUserID && updateUserID != null) {
          const params = {
            TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
            Key: { id: { S: updateUserID } },
          };
          const getUserResponse = await ddb.getItem(params).promise();
          const userItem = getUserResponse["Item"]
            ? AWS.DynamoDB.Converter.unmarshall(getUserResponse["Item"])
            : null;

          if (userItem && userItem.FCMToken && userItem.FCMToken == FCMToken) {
            // Update user FCM Token
            let userParams = {
              TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              Key: { id: { S: updateUserID } },
              ExpressionAttributeNames: { "#IL": "isLogin" },
              ExpressionAttributeValues: { ":il": { BOOL: true } },
              ReturnValues: "ALL_NEW",
              UpdateExpression: "SET #IL = :il",
            };
            await ddb.updateItem(userParams).promise();
          } else if (userItem) {
            if (
              userItem?.endpointArn &&
              userItem?.endpointArn != null &&
              userItem?.deviceId &&
              userItem?.deviceId != deviceId
            ) {
              let notificationFunName = `notificationFunction-${process.env.ENV}`;
              let logoutPayload = {
                arguments: {
                  input: {
                    title: "Forced Log Out",
                    body: "You've been Logged Out forcefully as new Login was found",
                    taskNotificationsId: "null",
                    notificationType: "forceLogOut",
                    MVPTokens: "0",
                    points: "0",
                    isAnswerable: false,
                    notificationIcon:
                      "notificationIcons/mvpNotificationIcon.png",
                    userList: [
                      {
                        id: updateUserID,
                        endpointArn: userItem.endpointArn,
                        isLogin: userItem.isLogin,
                      },
                    ],
                  },
                },
              };
              let logoutNotificationParams = {
                FunctionName: notificationFunName,
                InvocationType: "RequestResponse",
                LogType: "None",
                Payload: JSON.stringify(logoutPayload),
              };
              console.log("logoutNotificationParams", logoutNotificationParams);
              await lambda.invoke(logoutNotificationParams).promise();
            }

            if (FCMToken && userItem.endpointArn) {
              // Update ARN
              var updateARNParams = {
                Attributes: {
                  Enabled: "true",
                  Token: FCMToken,
                },
                EndpointArn: userItem?.endpointArn,
              };
              endpointArn = userItem?.endpointArn;
              await sns.setEndpointAttributes(updateARNParams).promise();
            } else {
              //Create ARN
              var endpointData = await sns
                .createPlatformEndpoint({
                  PlatformApplicationArn: platform_arn,
                  Token: FCMToken,
                  CustomUserData: updateUserID,
                })
                .promise();
              endpointArn = endpointData?.EndpointArn;
            }
            // Update user FCM Token
            let userParams = {
              TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              Key: {
                id: { S: updateUserID },
              },
              ExpressionAttributeNames: {
                "#DI": "deviceId",
                "#FCM": "FCMToken",
                "#EP": "endpointArn",
                "#LP": "loginPlatform",
                "#IL": "isLogin",
                "#ODI": "otherDeviceId",
                "#OFCM": "otherFCMToken",
                "#ILO": "isLoginOther",
              },
              ExpressionAttributeValues: {
                ":di": { S: deviceId ?? "" },
                ":fcm": { S: FCMToken ?? "" },
                ":ep": { S: endpointArn ?? "" },
                ":lp": { S: loginPlatform ?? "" },
                ":il": { BOOL: true },
                ":odi": {
                  S:
                    userItem?.deviceId && userItem?.deviceId != deviceId
                      ? userItem?.deviceId
                      : "",
                },
                ":ofcm": { S: userItem?.FCMToken ?? "" },
                ":ilo": { BOOL: true },
              },
              ReturnValues: "ALL_NEW",
              UpdateExpression:
                "SET #DI = :di, #FCM = :fcm, #LP = :lp, #EP = :ep, #IL = :il, #ODI = :odi, #OFCM = :ofcm, #ILO = :ilo",
            };
            await ddb.updateItem(userParams).promise();
          }
          callback(null, { message: "FCM token updated.", statusCode: 200 });
        } else {
          callback(null, { message: "User not found.", statusCode: 401 });
        }

        break;
      case "createCourse": {
        console.log("createCourse");
        let courseId = event.arguments?.input?.id ?? uuid.v4();
        let homeworkId = uuid.v4();
        let {
          name,
          shortDescription,
          longDescription,
          type,
          startDateTime,
          endDateTime,
          contactFullName,
          contactEmail,
          contactPhoneNumber,
          contactRole,
          organizations = [],
          cityId,
          streetAddress1,
          streetAddress2,
          city,
          state,
          zipcode,
          status = false,
          structure,
          infoUrl,
          createdBy,
          createdUserName,
          isDeleted = false,
          projectImage,
          categoryID
        } = event.arguments?.input ?? {};
        let files =
          AWS.DynamoDB.Converter.marshall({
            filesObj: event.arguments?.input?.files,
          }) ?? "";
        let eventsOrganizationsData = [];
        let homeworkOrganizationsData = [];
        let pointsData = [];
        let activityData = [];
        console.log("event.arguments?.input", event.arguments?.input);

        let eventParams = {
          Item: {
            id: { S: courseId },
            __typename: { S: "Events" },
            name: { S: name },
            shortDescription: { S: shortDescription ?? "" },
            longDescription: { S: longDescription ?? "" },
            type: { S: type ?? "" },
            categoryID: { S: categoryID ?? "" },
            startDateTime: { S: startDateTime ?? "" },
            endDateTime: { S: endDateTime ?? "" },
            contactFullName: { S: contactFullName ?? "" },
            contactEmail: { S: contactEmail ?? "" },
            contactPhoneNumber: { S: `+1${contactPhoneNumber}` ?? "" },
            contactRole: { S: contactRole ?? "" },
            cityId: { S: cityId ?? "" },
            streetAddress1: { S: streetAddress1 ?? "" },
            streetAddress2: { S: streetAddress2 ?? "" },
            city: { S: city ?? "" },
            state: { S: state ?? "" },
            zipcode: { S: zipcode ?? "" },
            status: { BOOL: status ?? "" },
            structure: { S: structure ?? "" },
            infoUrl: { S: infoUrl ?? "" },
            files: files.filesObj,
            createdBy: { S: createdBy ?? "" },
            isDeleted: { S: "false" },
            projectImage: { S: projectImage ?? "" },
            createdAt: { S: date.toISOString() },
            updatedAt: { S: date.toISOString() },
            _version: { N: "1" },
            _lastChangedAt: { N: date.getTime().toString() },
          },
          TableName:
            "Events-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        await ddb.putItem(eventParams).promise();
        console.log("created");
        // Create activity
        let courseActivityParams = {
          Item: {
            id: { S: uuid.v4() },
            __typename: { S: "Activity" },
            activityType: { S: "MEMBERSHIP" },
            cityId: { S: cityId ?? "" },
            createdUserId: { S: createdBy ?? "" },
            createdUserName: { S: createdUserName ?? "" },
            isDeleted: { S: "false" },
            moduleId: { S: courseId ?? "" },
            moduleImageUrl: { S: projectImage ?? "" },
            moduleName: { S: name ?? "" },
            moduleType: { S: "project" },
            type: { S: "CREATED" },
            createdAt: { S: date.toISOString() },
            updatedAt: { S: date.toISOString() },
            _version: { N: "1" },
            _lastChangedAt: { N: date.getTime().toString() },
          },
          TableName:
            "Activity-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        await ddb.putItem(courseActivityParams).promise();
        console.log("first");
        console.log("organizations", organizations);
        // Create eventsOrganizations items, add points and add related acivities
        organizations.map(async (items) => {
          eventsOrganizationsData.push({
            PutRequest: {
              Item: {
                id: { S: uuid.v4() },
                __typename: { S: "EventsOrganizations" },
                eventsID: { S: courseId ?? "" },
                organizationsID: { S: items ?? "" },
                createdAt: { S: date.toISOString() },
                updatedAt: { S: date.toISOString() },
                _version: { N: "1" },
                _lastChangedAt: { N: date.getTime().toString() },
              },
            },
          });

          pointsData.push({
            PutRequest: {
              Item: {
                id: { S: uuid.v4() },
                __typename: { S: "Points" },
                category: { S: type ?? "" },
                cityId: { S: cityId ?? "" },
                createdBy: { S: createdBy ?? "" },
                impactScore: { S: "100" },
                MVPTokens: { N: "25" },
                isDeleted: { S: "false" },
                memberId: { S: items ?? "" },
                pointType: { S: "course" },
                type: { S: "member" },
                status: { S: "CREDITED" },
                createdAt: { S: date.toISOString() },
                updatedAt: { S: date.toISOString() },
                _version: { N: "1" },
                _lastChangedAt: { N: date.getTime().toString() },
              },
            },
          });

          activityData.push({
            PutRequest: {
              Item: {
                id: { S: uuid.v4() },
                __typename: { S: "Activity" },
                activityType: { S: "MEMBERSHIP" },
                cityId: { S: cityId ?? "" },
                createdUserId: { S: createdBy ?? "" },
                createdUserName: { S: createdUserName ?? "" },
                isDeleted: { S: "false" },
                moduleId: { S: courseId ?? "" },
                moduleImageUrl: { S: projectImage ?? "" },
                moduleName: { S: name ?? "" },
                moduleType: { S: "project" },
                type: { S: "CREATED" },
                relatedId: { S: items ?? "" },
                relatedName: { S: "" },
                relatedTo: { S: "member" },
                createdAt: { S: date.toISOString() },
                updatedAt: { S: date.toISOString() },
                _version: { N: "1" },
                _lastChangedAt: { N: date.getTime().toString() },
              },
            },
          });

          homeworkOrganizationsData.push({
            PutRequest: {
              Item: {
                id: { S: uuid.v4() },
                __typename: { S: "HomeworkOrganizations" },
                homeworkId: { S: homeworkId ?? "" },
                memberId: { S: items ?? "" },
                homeworkStatus: { S: "pending" },
                homeworkAssignDate: { S: date.toISOString() },
                homeworkMembersId: { S: homeworkId ?? "" },
                programsHomeworkOrganizationsId: { S: "null" },
                organizationsHomeworksId: { S: items ?? "" },
                createdAt: { S: date.toISOString() },
                updatedAt: { S: date.toISOString() },
                _version: { N: "1" },
                _lastChangedAt: { N: date.getTime().toString() },
              },
            },
          });
        });

        await ddbBatchWrite(
          `EventsOrganizations-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          eventsOrganizationsData
        );
        await ddbBatchWrite(
          `HomeworkOrganizations-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          homeworkOrganizationsData
        );
        await ddbBatchWrite(
          `Points-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          pointsData
        );
        await ddbBatchWrite(
          `Activity-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          activityData
        );
        console.log("organizations qwe", organizations);
        // Update membership items
        await processMembershipUpdates(organizations)
          .then(async (personsIds) => {
            console.log("Membership updates complete.");
            // Send notification
            await getUserDataForPersonsIds(personsIds)
              .then(async (notificationData) => {
                console.log("notificationData", notificationData);
                await sendNotifications(notificationData, type);
                console.log("Notifications sent.");
              })
              .catch((error) => console.error("Error:", error));
            console.log("Project has been created");
          })
          .catch((error) => console.error("Error:", error));

        callback(null, {
          message: "Course has been created.",
          statusCode: 200,
        });
        break;
      }
      case "updateProject": {
        console.log("updateProject");
        let projectId = event.arguments?.input?.id ?? uuid.v4();
        let {
          name,
          shortDescription,
          longDescription,
          type,
          startDateTime,
          endDateTime,
          contactFullName,
          contactEmail,
          contactPhoneNumber,
          contactRole,
          organizations = [],
          cityId,
          streetAddress1,
          streetAddress2,
          city,
          state,
          zipcode,
          status = false,
          structure,
          infoUrl,
          createdBy,
          createdUserName,
          isDeleted = false,
          projectImage,
          _version,
        } = event.arguments?.input ?? {};
        let files =
          AWS.DynamoDB.Converter.marshall({
            filesObj: event.arguments?.input?.files,
          }) ?? "";
        let eventsOrganizationsData = [];
        let pointsData = [];
        let activityData = [];
        console.log("event.arguments?.input", event.arguments?.input);

        var updateEventParams = {
          TableName: `Events-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          Key: {
            id: { S: projectId },
          },
          ExpressionAttributeNames: {
            "#NAME": "name",
            "#SHORTDESCRIPTION": "shortDescription",
            "#LONGDESCRIPTION": "longDescription",
            "#TYPE": "type",
            "#STARTDATETIME": "startDateTime",
            "#ENDDATETIME": "endDateTime",
            "#CONTACTFULLNAME": "contactFullName",
            "#CONTACTEMAIL": "contactEmail",
            "#CONTACTPHONENUMBER": "contactPhoneNumber",
            "#CONTACTROLE": "contactRole",
            "#CITYID": "cityId",
            "#STREETADDRESS1": "streetAddress1",
            "#STREETADDRESS2": "streetAddress2",
            "#CITY": "city",
            "#STATE": "state",
            "#ZIPCODE": "zipcode",
            "#STATUS": "status",
            "#STRUCTURE": "structure",
            "#INFOURL": "infoUrl",
            "#UPDATEDAT": "updatedAt",
            "#VERSION": "_version",
            "#LASTCHANGEDAT": "_lastChangedAt",
            "#FILES": "files",
            "#PROJECTIMAGE": "projectImage",
          },
          ExpressionAttributeValues: {
            ":name": { S: name ?? "" },
            ":shortDescription": { S: shortDescription ?? "" },
            ":longDescription": { S: longDescription ?? "" },
            ":type": { S: type ?? "" },
            ":startDateTime": { S: startDateTime ?? "" },
            ":endDateTime": { S: endDateTime ?? "" },
            ":contactFullName": { S: contactFullName ?? "" },
            ":contactEmail": { S: contactEmail ?? "" },
            ":contactPhoneNumber": { S: `+1${contactPhoneNumber}` ?? "" },
            ":contactRole": { S: contactRole ?? "" },
            ":cityId": { S: cityId ?? "" },
            ":streetAddress1": { S: streetAddress1 ?? "" },
            ":streetAddress2": { S: streetAddress2 ?? "" },
            ":city": { S: city ?? "" },
            ":state": { S: state ?? "" },
            ":zipcode": { S: zipcode ?? "" },
            ":status": { BOOL: status ?? "" },
            ":structure": { S: structure ?? "" },
            ":infoUrl": { S: infoUrl ?? "" },
            ":projectImage": { S: projectImage ?? "" },
            ":files": files.filesObj,
            ":updatedAt": { S: date.toISOString() },
            ":version": { N: _version ?? "1" },
            ":lastChangedAt": { N: date.getTime().toString() },
          },
          ReturnValues: "ALL_NEW",
          UpdateExpression:
            "SET #NAME = :name, #SHORTDESCRIPTION = :shortDescription, #LONGDESCRIPTION = :longDescription, #TYPE = :type, #STARTDATETIME = :startDateTime, #ENDDATETIME = :endDateTime, #CONTACTFULLNAME = :contactFullName, #CONTACTEMAIL = :contactEmail, #CONTACTPHONENUMBER = :contactPhoneNumber, #CONTACTROLE = :contactRole, #CITYID = :cityId, #STREETADDRESS1 = :streetAddress1, #STREETADDRESS2 = :streetAddress2, #CITY = :city, #STATE = :state, #ZIPCODE = :zipcode, #STATUS = :status, #STRUCTURE = :structure, #INFOURL = :infoUrl, #UPDATEDAT = :updatedAt, #VERSION = :version, #LASTCHANGEDAT = :lastChangedAt, #FILES = :files, #PROJECTIMAGE = :projectImage",
        };
        await ddb.updateItem(updateEventParams).promise();

        // Create activity
        let courseActivityParams = {
          Item: {
            id: { S: uuid.v4() },
            __typename: { S: "Activity" },
            activityType: { S: "MEMBERSHIP" },
            cityId: { S: cityId ?? "" },
            createdUserId: { S: createdBy ?? "" },
            createdUserName: { S: createdUserName ?? "" },
            isDeleted: { S: "false" },
            moduleId: { S: projectId ?? "" },
            moduleImageUrl: { S: projectImage ?? "" },
            moduleName: { S: name ?? "" },
            moduleType: { S: "project" },
            type: { S: "UPDATED" },
            requestStatus: { S: "SYSTEM_APPROVED" },
            createdAt: { S: date.toISOString() },
            updatedAt: { S: date.toISOString() },
            _version: { N: "1" },
            _lastChangedAt: { N: date.getTime().toString() },
          },
          TableName:
            "Activity-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        await ddb.putItem(courseActivityParams).promise();

        let eventsOrganizationsParams = {
          ExpressionAttributeValues: {
            ":p": { S: projectId },
          },
          FilterExpression: "#P = :p",
          ExpressionAttributeNames: {
            "#P": "eventsID",
          },
          ProjectionExpression: "id, eventsID, organizationsID",
          TableName: `EventsOrganizations-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };
        let eventsOrgResponse = await ddb
          .scan(eventsOrganizationsParams)
          .promise();
        let eventsOrgIds = await eventsOrgResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        console.log("eventsOrgIds", eventsOrgIds);

        let oldOrganizationsIds = eventsOrgIds.map((e) => e.organizationsID);
        let removedOrganizationsIds = oldOrganizationsIds.filter(
          (res) => !organizations.includes(res)
        );
        let addedOrganizationsIds = organizations.filter(
          (res) => !oldOrganizationsIds.includes(res)
        );
        let eventOrgIds = eventsOrgIds
          .map((ex) =>
            removedOrganizationsIds.includes(ex.organizationsID) ? ex.id : ""
          )
          .filter((ex) => ex != "");
        console.log("oldOrganizationsIds", oldOrganizationsIds);
        console.log("removedOrganizationsIds", removedOrganizationsIds);
        console.log("addedOrganizationsIds", addedOrganizationsIds);
        console.log("eventOrgIds", eventOrgIds);

        for (let i = 0; i < eventOrgIds.length; i++) {
          let deleteEventOrgParams = {
            TableName: `EventsOrganizations-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
            Key: {
              id: { S: eventOrgIds[i] },
            },
          };
          console.log("deleteEventOrgParams", deleteEventOrgParams);
          await ddb.deleteItem(deleteEventOrgParams).promise();
        }

        console.log("addedOrganizationsIds", addedOrganizationsIds);
        // Create eventsOrganizations items, add points and add related acivities
        addedOrganizationsIds.map(async (items) => {
          eventsOrganizationsData.push({
            PutRequest: {
              Item: {
                id: { S: uuid.v4() },
                __typename: { S: "EventsOrganizations" },
                eventsID: { S: projectId ?? "" },
                organizationsID: { S: items ?? "" },
                createdAt: { S: date.toISOString() },
                updatedAt: { S: date.toISOString() },
                _version: { N: "1" },
                _lastChangedAt: { N: date.getTime().toString() },
              },
            },
          });

          pointsData.push({
            PutRequest: {
              Item: {
                id: { S: uuid.v4() },
                __typename: { S: "Points" },
                category: { S: type ?? "" },
                cityId: { S: cityId ?? "" },
                createdBy: { S: createdBy ?? "" },
                impactScore: { S: "100" },
                MVPTokens: { N: "25" },
                isDeleted: { S: "false" },
                memberId: { S: items ?? "" },
                pointType: { S: "course" },
                type: { S: "member" },
                status: { S: "CREDITED" },
                createdAt: { S: date.toISOString() },
                updatedAt: { S: date.toISOString() },
                _version: { N: "1" },
                _lastChangedAt: { N: date.getTime().toString() },
              },
            },
          });

          activityData.push({
            PutRequest: {
              Item: {
                id: { S: uuid.v4() },
                __typename: { S: "Activity" },
                activityType: { S: "MEMBERSHIP" },
                cityId: { S: cityId ?? "" },
                createdUserId: { S: createdBy ?? "" },
                createdUserName: { S: createdUserName ?? "" },
                isDeleted: { S: "false" },
                moduleId: { S: projectId ?? "" },
                moduleImageUrl: { S: projectImage ?? "" },
                moduleName: { S: name ?? "" },
                moduleType: { S: "course" },
                type: { S: "CREATED" },
                relatedId: { S: items ?? "" },
                relatedName: { S: "" },
                relatedTo: { S: "member" },
                createdAt: { S: date.toISOString() },
                updatedAt: { S: date.toISOString() },
                _version: { N: "1" },
                _lastChangedAt: { N: date.getTime().toString() },
              },
            },
          });
        });
        await ddbBatchWrite(
          `EventsOrganizations-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          eventsOrganizationsData
        );
        await ddbBatchWrite(
          `Points-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          pointsData
        );
        await ddbBatchWrite(
          `Activity-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          activityData
        );

        // Update membership items
        await processMembershipUpdates(addedOrganizationsIds)
          .then(async (personsIds) => {
            console.log("Membership updates complete.");
            // Send notification
            await getUserDataForPersonsIds(personsIds)
              .then(async (notificationData) => {
                console.log("notificationData", notificationData);
                await sendNotifications(notificationData, type);
                console.log("Notifications sent.");
              })
              .catch((error) => console.error("Error:", error));
            console.log("Project updates complete.");
          })
          .catch((error) => console.error("Error:", error));

        callback(null, {
          message: "Project updates complete.",
          statusCode: 200,
        });
        break;
      }
      case "pointsByCategories":
        console.log("pointsByCategories");
        let memberId = event?.arguments?.memberId;
        var params = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
            ":m": { S: memberId },
          },
          FilterExpression: "#D = :d and #M = :m",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#M": "memberId",
            "#S": "status",
          },
          ProjectionExpression: "#S, impactScore, category, MVPTokens",
          TableName:
            "Points-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        const pointsResponse = await ddb.scan(params).promise();
        const pointsNewResponse = (pointsResponse?.Items).map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );

        var categories = {
          STEMCreativity: "STEM & Creativity",
          AcademicsTesting: "Academics & Testing",
          HealthWellness: "Health & Wellness",
          HistoryTradition: "History & Tradition",
          BusinessFinance: "Business & Finance",
        };

        const pointsRes = [];
        for (var key in categories) {
          let creditedAmount = [];
          let debitedAmount = [];
          let creditedAmountSum = 0;
          let debitedAmountSum = 0;
          let MVPTokens = 0;
          let type = categories[key];
          creditedAmount = await pointsNewResponse.filter(function (el) {
            return el.category == type && el.status != "DEBITED";
          });
          creditedAmountSum = Object.keys(creditedAmount).reduce(
            (partialSum, key) =>
              parseFloat(partialSum) +
              parseFloat(creditedAmount[key].MVPTokens),
            0
          );

          debitedAmount = pointsNewResponse.filter(function (el) {
            return el.category == type && el.status == "DEBITED";
          });
          debitedAmountSum = Object.keys(debitedAmount).reduce(
            (partialSum, key) =>
              parseFloat(partialSum) + parseFloat(debitedAmount[key].MVPTokens),
            0
          );

          MVPTokens =
            parseFloat(creditedAmountSum) - parseFloat(debitedAmountSum);
          pointsRes[key] = MVPTokens;
        }

        const pointByCatResponse = {
          STEMCreativity:
            pointsRes["STEMCreativity"] && pointsRes["STEMCreativity"] != null
              ? pointsRes["STEMCreativity"]
              : 0,
          AcademicsTesting:
            pointsRes["AcademicsTesting"] &&
            pointsRes["AcademicsTesting"] != null
              ? pointsRes["AcademicsTesting"]
              : 0,
          HealthWellness:
            pointsRes["HealthWellness"] && pointsRes["HealthWellness"] != null
              ? pointsRes["HealthWellness"]
              : 0,
          HistoryTradition:
            pointsRes["HistoryTradition"] &&
            pointsRes["HistoryTradition"] != null
              ? pointsRes["HistoryTradition"]
              : 0,
          BusinessFinance:
            pointsRes["BusinessFinance"] && pointsRes["BusinessFinance"] != null
              ? pointsRes["BusinessFinance"]
              : 0,
        };
        callback(null, pointByCatResponse);
        break;
      case "checkAssociationExist": {
        console.log("checkAssociationExist");
        let memberID = event.arguments?.memberId;
        userID = event.arguments?.userId;
        let rescheckAssociationExistResponse = "";
        var params = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
            ":u": { S: userID },
            ":m": { S: memberID },
          },
          FilterExpression:
            "(#U = :u or #U = :m) and (#B = :m or #O = :m or #OP = :m or #OP = :u) and #D = :d",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#U": "personsID",
            "#B": "businessID",
            "#O": "organizationID",
            "#OP": "otherPersonsID",
          },
          TableName:
            "Association-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        const associationResponse = await ddb.scan(params).promise();
        const associationNewResponse = associationResponse.Items.map(
          (records) => AWS.DynamoDB.Converter.unmarshall(records)
        );
        console.log("associationNewResponse", associationNewResponse);
        if (associationNewResponse && associationNewResponse.length > 0) {
          rescheckAssociationExistResponse = {
            isExist: 1,
            message: "Associatopn already exist.",
          };
        } else {
          rescheckAssociationExistResponse = {
            isExist: 0,
            message: "Associatopn does not exist.",
          };
        }
        callback(null, rescheckAssociationExistResponse);
        break;
      }
      case "createTaskRelationships":
        console.log("createTaskRelationships");
        console.log("event.arguments?.input", event.arguments?.input);
        let data = event?.arguments?.input ?? [];
        let dataSegments = R.splitEvery(25, data);
        let taskUserTableName =
          "TaskUser-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV;

        for (let i = 0; i < dataSegments.length; i++) {
          let segment = dataSegments[i];
          let taskRelationshipsParams = {
            RequestItems: {
              [taskUserTableName]: segment.map((item) => ({
                PutRequest: {
                  Item: {
                    id: {
                      S: uuid.v4(),
                    },
                    __typename: { S: "TaskUser" },
                    taskID: {
                      S: item?.taskId,
                    },
                    userID: {
                      S: item?.memberId,
                    },
                    createdAt: { S: date.toISOString() },
                    updatedAt: { S: date.toISOString() },
                    _version: { N: "1" },
                    _lastChangedAt: { N: date.getTime().toString() },
                  },
                },
              })),
            },
          };
          try {
            let response = await ddb
              .batchWriteItem(taskRelationshipsParams)
              .promise();
            while (!R.isEmpty(response.UnprocessedItems)) {
              let count = response.UnprocessedItems[tableName].length;
              console.log(`${count} unprocessed item(s) left, retrying...`);
              let unprocessedParams = {
                RequestItems: response.UnprocessedItems,
              };
              response = await ddb.batchWriteItem(unprocessedParams);
            }
          } catch (error) {
            console.log(error, error.stack);
          }
        }
        callback(null, {
          message: "Task relationships created.",
          statusCode: 200,
        });
        break;
      case "deleteOldFeedback":
        console.log("deleteOldFeedback");
        console.log("event.arguments?.input", event.arguments?.input);
        let notificationIds = event?.arguments?.input?.notificationIds ?? [];
        let notificationTableName =
          "Notifications-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV;

        for (let i = 0; i < notificationIds.length; i++) {
          var deleteOldFeedbackParams = {
            TableName: notificationTableName,
            Key: {
              id: { S: notificationIds[i] },
            },
          };
          try {
            await ddb.deleteItem(deleteOldFeedbackParams).promise();
          } catch (error) {
            console.log(error, error.stack);
          }
        }
        callback(null, { message: "Old feedback deleted.", statusCode: 200 });
        break;
      case "createStudent":
        console.log("createStudent");
        console.log(
          "event.arguments?.input?.items",
          event.arguments?.input?.items
        );
        try {
          const studentData = event.arguments?.input?.items;
          let userExist = 1;
          let listExistUserResponse = "";
          let phoneNumber = "";
          let cognitoResponse = "";
          let phoneNo = "+10000000000";
          let pointsData = [];
          let newImageUrl = "";
          let userType = "student";

          if (studentData.email && !/\S+@\S+\.\S+/.test(studentData.email)) {
            callback(null, {
              isExist: false,
              message: "Please enter valid email.",
              statusCode: 409,
            });
          }

          if (
            studentData?.birthday &&
            !!isNaN(new Date(studentData?.birthday))
          ) {
            callback(null, {
              isExist: false,
              message: "Please enter valid birthday.",
              statusCode: 409,
            });
          }

          if (studentData.email) {
            userType = "loginUser";

            let cognitoUserExistListParams = {
              AttributesToGet: ["name"],
              Filter: 'email = "' + studentData?.email + '"',
              UserPoolId: USERPOOLID,
            };
            listExistUserResponse = await cognito
              .listUsers(cognitoUserExistListParams)
              .promise();
            userExist = listExistUserResponse?.Users?.length ?? 0;
            console.log("userExist", userExist);

            phoneNumber = studentData?.phoneNumber.replace(/-/g, "");
            phoneNumber = phoneNumber.replace(/\s/g, "");
            phoneNumber = phoneNumber.replace("+1", "");
            phoneNumber = "+1" + phoneNumber;
            console.log("phoneNumber", phoneNumber);
            if (
              /^\+[1-9][0-9]{0,17}$/.test(phoneNumber) &&
              phoneNumber.length > 3
            ) {
              phoneNo = phoneNumber;
            }

            if (
              userExist == 0 &&
              studentData?.email &&
              studentData?.givenName &&
              studentData?.familyName &&
              studentData?.ethnicity &&
              studentData?.gender &&
              studentData?.status
            ) {
              var cognitoParams = {
                UserPoolId: USERPOOLID /* required */,
                Username: studentData?.email /* required */,
                // DesiredDeliveryMediums: [
                //   "EMAIL"
                // ],
                ForceAliasCreation: true,
                TemporaryPassword: studentData?.password ?? "Test@1234",
                UserAttributes: [
                  { Name: "email", /* required */ Value: studentData?.email },
                  { Name: "email_verified", Value: "true" },
                  { Name: "phone_number_verified", Value: "true" },
                  {
                    Name: "given_name",
                    /* required */ Value: studentData?.givenName,
                  },
                  {
                    Name: "family_name",
                    /* required */ Value: studentData?.familyName,
                  },
                  { Name: "phone_number", /* required */ Value: phoneNo },
                  { Name: "name", /* required */ Value: studentData?.name },
                  { Name: "custom:role", Value: "SUBSCRIBER" },
                  { Name: "custom:registeredFrom", Value: "MOBILE" },
                ],
              };
              cognitoResponse = await cognito
                .adminCreateUser(cognitoParams)
                .promise();
              console.log("cognitoResponse", cognitoResponse);
            }
          }
          if (userExist == 0 || !studentData.email) {
            const createdUserId = cognitoResponse?.User?.Username ?? uuid.v4();
            const memberId = uuid.v4();
            let phoneNodb = "";
            phoneNodb = studentData?.phoneNumber.replace(/-/g, "");
            phoneNodb = phoneNodb.replace(/\s/g, "");
            // phoneNodb = phoneNodb.replace("+1", "");
            if (
              createdUserId &&
              studentData?.givenName &&
              studentData?.familyName &&
              studentData?.ethnicity &&
              studentData?.gender &&
              studentData?.status
            ) {
              // Generate image URL
              if (studentData?.imageUrl) {
                newImageUrl =
                  "profile/" + createdUserId + "/" + studentData?.imageUrl;
              }

              const scanParams = {
                ExpressionAttributeValues: { ":d": { S: "false" } },
                FilterExpression: "#D = :d",
                ExpressionAttributeNames: { "#D": "isDeleted" },
                ProjectionExpression: "memberCode",
                TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              };
              const scanResponse = await ddb.scan(scanParams).promise();
              const newResponse = scanResponse.Items.map((records) =>
                AWS.DynamoDB.Converter.unmarshall(records)
              );
              const memberCodes = newResponse.map(
                (item) => parseInt(item.memberCode) || 0
              );
              let highestMemberCode = Math.max(...memberCodes);

              // Create membership table Data
              var memberPerms = {
                Item: {
                  id: { S: memberId },
                  __typename: { S: "Membership" },
                  name: { S: studentData?.name ?? "" },
                  personsID: { S: createdUserId ?? "" },
                  MVPTokens: { S: "5.00" },
                  cityId: { S: studentData?.cityId ?? "" },
                  imageUrl: { S: newImageUrl ?? "" },
                  currentImpactScore: { S: "5.00" },
                  lastAddedImpactScore: { S: "5.00" },
                  type: { S: "User" },
                  memberCode: {
                    S:
                      (highestMemberCode + 1).toString().padStart(4, "0") ?? "",
                  },
                  isDeleted: { S: "false" },
                  createdAt: { S: date.toISOString() },
                  updatedAt: { S: date.toISOString() },
                  _version: { N: "1" },
                  _lastChangedAt: { N: date.getTime().toString() },
                },
                ReturnConsumedCapacity: "TOTAL",
                TableName:
                  "Membership-" +
                  process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
                  "-" +
                  process.env.ENV,
              };
              let memberCreate = await ddb.putItem(memberPerms).promise();
              console.log("memberCreate", memberCreate);

              // Create user table Data
              var params = {
                Item: {
                  id: { S: createdUserId },
                  __typename: { S: "User" },
                  email: { S: studentData?.email ?? "" },
                  familyName: { S: studentData?.familyName ?? "" },
                  givenName: { S: studentData?.givenName ?? "" },
                  phoneNumber: { S: `+1${phoneNodb}` ?? "" },
                  name: { S: studentData?.name ?? "" },
                  imageUrl: { S: newImageUrl ?? "" },
                  cityId: { S: studentData?.cityId ?? "" },
                  registeredFrom: {
                    S: studentData?.registeredFrom ?? "MOBILE",
                  },
                  isDeleted: { S: "false" },
                  ethnicity: { S: studentData?.ethnicity ?? "" },
                  gender: { S: studentData?.gender ?? "NONE" },
                  birthday: { S: studentData?.birthday ?? "" },
                  streetAddressOne: { S: studentData?.streetAddressOne ?? "" },
                  streetAddressTwo: { S: studentData?.streetAddressTwo ?? "" },
                  type: { S: studentData?.type ?? "" },
                  role: { S: "SUBSCRIBER" },
                  state: { S: studentData?.state ?? "" },
                  zipCode: { S: studentData?.zipCode ?? "" },
                  status: { S: studentData?.status ?? "enrolled" },
                  userType: { S: userType ?? "" },
                  membershipId: { S: memberId ?? "" },
                  isAssociated: { BOOL: true },
                  isStakeholder: { BOOL: false },
                  memberCode: {
                    S:
                      (highestMemberCode + 1).toString().padStart(4, "0") ?? "",
                  },
                  createdAt: { S: date.toISOString() },
                  updatedAt: { S: date.toISOString() },
                  _version: { N: "0" },
                  _lastChangedAt: { N: date.getTime().toString() },
                },
                ReturnConsumedCapacity: "TOTAL",
                TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              };
              let createUserData = await ddb.putItem(params).promise();
              console.log("createUserData", createUserData);

              // Create activity table user Data
              var userCreateActivityParam = {
                Item: {
                  id: { S: uuid.v4() },
                  __typename: { S: "Activity" },
                  activityType: { S: "MEMBERSHIP" ?? "" },
                  createdUserId: { S: studentData?.createdUserId ?? "" },
                  createdUserName: { S: studentData?.createdUserName ?? "" },
                  cityId: { S: studentData?.cityId ?? "" },
                  moduleId: { S: createdUserId ?? "" },
                  moduleImageUrl: { S: newImageUrl ?? "" },
                  moduleName: { S: studentData?.name ?? "" },
                  moduleType: { S: "student" },
                  type: { S: "CREATED" },
                  activityTokens: { N: "0" },
                  isDeleted: { S: "false" },
                  createdAt: { S: date.toISOString() },
                  updatedAt: { S: date.toISOString() },
                  _version: { N: "1" },
                  _lastChangedAt: { N: date.getTime().toString() },
                },
                ReturnConsumedCapacity: "TOTAL",
                TableName:
                  "Activity-" +
                  process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
                  "-" +
                  process.env.ENV,
              };
              let createActivityData = await ddb
                .putItem(userCreateActivityParam)
                .promise();
              console.log("createActivityData", createActivityData);

              // Create association table Data
              var associationParams = {
                Item: {
                  id: { S: uuid.v4() },
                  __typename: { S: "Association" },
                  otherPersonsID: { S: createdUserId },
                  personsID: { S: studentData?.createdUserId ?? "" },
                  relationType: { S: studentData?.associationType ?? "" },
                  status: { BOOL: true },
                  type: { S: "Person" },
                  cityId: { S: studentData?.cityId ?? "" },
                  userAssociationsId: { S: createdUserId },
                  isDeleted: { S: "false" },
                  userUserAssociationsId: {
                    S: studentData?.createdUserId ?? "",
                  },
                  createdAt: { S: date.toISOString() },
                  updatedAt: { S: date.toISOString() },
                  _version: { N: "0" },
                  _lastChangedAt: { N: date.getTime().toString() },
                },
                ReturnConsumedCapacity: "TOTAL",
                TableName:
                  "Association-" +
                  process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
                  "-" +
                  process.env.ENV,
              };
              let createAssociationData = await ddb
                .putItem(associationParams)
                .promise();
              console.log("createAssociationData", createAssociationData);

              // Update membership table Data for MVP token
              var membershipParams = {
                ExpressionAttributeValues: {
                  ":p": { S: studentData?.createdUserId },
                },
                FilterExpression: "#P = :p",
                ExpressionAttributeNames: {
                  "#P": "personsID",
                  "#N": "name",
                },
                ProjectionExpression: "id, currentImpactScore, #N, MVPTokens",
                TableName:
                  "Membership-" +
                  process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
                  "-" +
                  process.env.ENV,
              };
              let membershipResponse = await ddb
                .scan(membershipParams)
                .promise();
              let membershipIds = await membershipResponse.Items.map(
                (records) => AWS.DynamoDB.Converter.unmarshall(records)
              );
              console.log("membershipIds", membershipIds);
              let membershipIdData = membershipIds[0];
              console.log("membershipIdData", membershipIdData);
              let impactScore =
                membershipIdData?.currentImpactScore &&
                membershipIdData?.currentImpactScore != null
                  ? parseFloat(membershipIdData?.currentImpactScore)
                  : 0;
              let MVPTokens =
                membershipIdData?.MVPTokens &&
                membershipIdData?.MVPTokens != null
                  ? parseFloat(membershipIdData?.MVPTokens)
                  : 0;
              let currentImpactScore =
                impactScore && impactScore != null ? impactScore + 5 : 5;
              let currentMVPTokens =
                MVPTokens && MVPTokens != null ? MVPTokens + 1.25 : 1.25;

              if (currentImpactScore && membershipIdData?.id) {
                var updateMemberParams = {
                  TableName:
                    "Membership-" +
                    process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
                    "-" +
                    process.env.ENV,
                  Key: {
                    id: { S: membershipIdData.id },
                  },
                  ExpressionAttributeNames: {
                    "#CIS": "currentImpactScore",
                    "#LAIS": "lastAddedImpactScore",
                    "#MVP": "MVPTokens",
                  },
                  ExpressionAttributeValues: {
                    ":cis": {
                      S: currentImpactScore.toString(),
                    },
                    ":lais": {
                      S: "100",
                    },
                    ":mvp": {
                      N: currentMVPTokens.toString(),
                    },
                  },
                  ReturnValues: "ALL_NEW",
                  UpdateExpression:
                    "SET #CIS = :cis, #LAIS = :lais, #MVP = :mvp",
                };
                await ddb.updateItem(updateMemberParams).promise();
              }

              // Create points table data
              let studentIds = [studentData?.createdUserId, createdUserId];
              studentIds.map(async (items) => {
                pointsData.push({
                  PutRequest: {
                    Item: {
                      id: { S: uuid.v4() },
                      __typename: { S: "Points" },
                      category: { S: "History & Tradition" },
                      cityId: { S: studentData?.cityId ?? "" },
                      createdBy: { S: studentData?.createdUserId ?? "" },
                      impactScore: { S: "5" },
                      MVPTokens: { N: "1.25" },
                      isDeleted: { S: "false" },
                      memberId: { S: items },
                      pointType: { S: "association" },
                      type: { S: "student" },
                      status: { S: "CREDITED" },
                      createdAt: { S: date.toISOString() },
                      updatedAt: { S: date.toISOString() },
                      _version: { N: "1" },
                      _lastChangedAt: { N: date.getTime().toString() },
                    },
                  },
                });
              });

              let pointsTableName =
                "Points-" +
                process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
                "-" +
                process.env.ENV;
              if (pointsData && pointsData.length) {
                let pointsParams = {
                  RequestItems: {
                    [pointsTableName]: pointsData,
                  },
                };
                await ddb.batchWriteItem(pointsParams).promise();
              }

              // Create activity table assocation Data
              var associationCreateActivityParam = {
                Item: {
                  id: { S: uuid.v4() },
                  __typename: { S: "Activity" },
                  activityType: { S: "MEMBERSHIP" ?? "" },
                  createdUserId: { S: studentData?.createdUserId ?? "" },
                  createdUserName: { S: studentData?.createdUserName ?? "" },
                  cityId: { S: studentData?.cityId ?? "" },
                  moduleId: { S: createdUserId ?? "" },
                  moduleImageUrl: { S: newImageUrl ?? "" },
                  moduleName: { S: studentData?.name ?? "" },
                  relatedId: { S: studentData?.createdUserId ?? "" },
                  relatedName: { S: studentData?.createdUserName ?? "" },
                  relatedTo: { S: "student" },
                  moduleType: { S: "association" },
                  isRelationship: { BOOL: true },
                  type: { S: "ADDED" },
                  activityTokens: { N: "0" },
                  isDeleted: { S: "false" },
                  createdAt: { S: date.toISOString() },
                  updatedAt: { S: date.toISOString() },
                  _version: { N: "1" },
                  _lastChangedAt: { N: date.getTime().toString() },
                },
                ReturnConsumedCapacity: "TOTAL",
                TableName:
                  "Activity-" +
                  process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
                  "-" +
                  process.env.ENV,
              };
              let associationCreateActivityData = await ddb
                .putItem(associationCreateActivityParam)
                .promise();
              console.log(
                "associationCreateActivityData",
                associationCreateActivityData
              );
            } else {
              callback(null, {
                isExist: false,
                message: "Unable to create user.",
                statusCode: 409,
              });
            }
            callback(null, {
              isExist: false,
              message: "Student Created successfully.",
              statusCode: 200,
              userId: createdUserId,
            });
          } else {
            callback(null, {
              isExist: true,
              message: "Email already exist.",
              statusCode: 403,
            });
          }
        } catch (error) {
          callback(null, {
            isExist: true,
            message: error?.message,
            statusCode: error.statusCode,
          });
        }
        break;
      case "deleteAssociations": {
        console.log("deleteAssociations");
        console.log("event.arguments?.input", event.arguments?.input);

        const associationIds = event?.arguments?.input?.associationIds ?? [];
        const associationTable = `Association-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

        const deleteAssociation = async (id) => {
          const deleteParams = {
            TableName: associationTable,
            Key: {
              id: { S: id },
            },
          };
          try {
            await ddb.deleteItem(deleteParams).promise();
          } catch (error) {
            console.log(error, error.stack);
          }
        };

        await Promise.all(associationIds.map(deleteAssociation));

        callback(null, {
          message: "Associations are deleted.",
          statusCode: 200,
        });
        break;
      }
      case "deleteFavoriteEventsData": {
        console.log("deleteFavoriteEvents");
        console.log("event.arguments?.input", event.arguments?.input);

        const favoriteEventsId =
          event?.arguments?.input?.favoriteEventsId ?? [];
        const favoriteEventsTable = `FavoriteEvents-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

        const deleteFavoriteEvents = async (id) => {
          const deleteParams = {
            TableName: favoriteEventsTable,
            Key: {
              id: { S: id },
            },
          };
          try {
            await ddb.deleteItem(deleteParams).promise();
          } catch (error) {
            console.log(error, error.stack);
          }
        };

        await Promise.all(favoriteEventsId.map(deleteFavoriteEvents));

        callback(null, {
          message: "Associations are deleted.",
          statusCode: 200,
        });
        break;
      }
      case "deleteNotification": {
        console.log("deleteNotification");
        console.log("event.arguments?.input", event.arguments?.input);
        let userId = event?.arguments?.input?.userId ?? "";
        let notificationTable = `Notifications-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

        const notificationParams = {
          ExpressionAttributeValues: {
            ":u": { S: userId },
          },
          FilterExpression: "#U = :u",
          ExpressionAttributeNames: {
            "#U": "userId",
          },
          ProjectionExpression: "id",
          TableName: notificationTable,
        };

        try {
          const notificationResponse = await ddb
            .scan(notificationParams)
            .promise();
          const notificationDataRes = notificationResponse.Items.map(
            (records) => AWS.DynamoDB.Converter.unmarshall(records)
          );

          const deletePromises = notificationDataRes.map((record) => {
            const deleteNotificationParams = {
              TableName: notificationTable,
              Key: {
                id: { S: record.id },
              },
            };
            return ddb.deleteItem(deleteNotificationParams).promise();
          });

          await Promise.all(deletePromises);

          callback(null, {
            message: "Notifications are deleted.",
            statusCode: 200,
          });
        } catch (error) {
          console.log(error, error.stack);
          callback(error, {
            message: "Failed to delete notifications.",
            statusCode: 500,
          });
        }
        break;
      }
      case "deleteHomeworkRelations": {
        console.log("deleteHomeworkRelations");
        console.log("event.arguments?.input", event.arguments?.input);

        const memberRelationIds =
          event?.arguments?.input?.memberRelationIds ?? [];
        const userRelationIds = event?.arguments?.input?.userRelationIds ?? [];

        const HomeworkOrgTableName = `HomeworkOrganizations-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        const HomeworkUsersTableName = `HomeworkUsers-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

        const deleteItems = async (tableName, ids) => {
          const promises = ids.map(async (id) => {
            const deleteParams = {
              TableName: tableName,
              Key: {
                id: { S: id },
              },
            };
            try {
              await ddb.deleteItem(deleteParams).promise();
            } catch (error) {
              console.log(error, error.stack);
            }
          });
          await Promise.all(promises);
        };

        await Promise.all([
          deleteItems(HomeworkOrgTableName, memberRelationIds),
          deleteItems(HomeworkUsersTableName, userRelationIds),
        ]);

        callback(null, {
          message: "Homework relations are deleted.",
          statusCode: 200,
        });
        break;
      }
      case "deletePerquisitesData": {
        console.log("deletePerquisitesData");
        console.log("event.arguments?.input", event.arguments?.input);

        const input = event?.arguments?.input || {};
        const { programId = "", deleteType = "", perquisitesId = [] } = input;
        const perquisitesTable = `Perquisites-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        let perquisiteDataIds = [];

        if (programId && deleteType === "program_delete") {
          const perquisitesParams = {
            ExpressionAttributeValues: {
              ":p": { S: programId },
            },
            FilterExpression: "#P = :p",
            ExpressionAttributeNames: {
              "#P": "programId",
            },
            ProjectionExpression: "id",
            TableName: perquisitesTable,
          };

          try {
            const perquisitesResponse = await ddb
              .scan(perquisitesParams)
              .promise();
            perquisiteDataIds = perquisitesResponse.Items.map(
              (records) => AWS.DynamoDB.Converter.unmarshall(records).id
            );
          } catch (error) {
            console.error(error);
            return callback({
              error: "An error occurred while scanning perquisites",
            });
          }
        }

        const deletePerquisites = async (id) => {
          const deleteParams = {
            TableName: perquisitesTable,
            Key: {
              id: { S: id },
            },
          };

          try {
            await ddb.deleteItem(deleteParams).promise();
          } catch (error) {
            console.error(error);
            return callback({
              error: "An error occurred while deleting perquisites",
            });
          }
        };

        try {
          await Promise.all(
            [...perquisitesId, ...perquisiteDataIds].map(deletePerquisites)
          );
          callback(null, {
            message: "Perquisites are deleted.",
            statusCode: 200,
          });
        } catch (error) {
          console.error(error);
          callback({ error: "An error occurred while deleting perquisites" });
        }

        break;
      }
      case "viewMicrocredentials": {
        console.log("viewMicrocredentials");
        console.log("event.arguments?", event.arguments);
        const memberId = event.arguments?.memberId;
        const type = event.arguments?.type;
        const isCompleted = event.arguments?.isCompleted;
        const homeworkOrganizationsTable = `HomeworkOrganizations-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        const homeworkUsersTable = `HomeworkUsers-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        let completedMicrocredentails = [];
        let pendingMicrocredentails = [];
        let pendingMicrocredentailsArr = [];
        let resultArray = [];
        let data = [];
        const params = {
          ExpressionAttributeValues: {
            ":m": { S: memberId },
          },
          FilterExpression: "#M = :m",
          ExpressionAttributeNames: {
            "#M": type === "member" ? "memberId" : "studentStakeholderId",
          },
          TableName:
            type === "member" ? homeworkOrganizationsTable : homeworkUsersTable,
        };

        try {
          const homeworkResponse = await ddb.scan(params).promise();
          const homeworkRes = homeworkResponse.Items.map((records) =>
            AWS.DynamoDB.Converter.unmarshall(records)
          );

          const completedHomework = homeworkRes.filter(
            (record) => record.homeworkStatus === "completed"
          );
          const inReviewHomework = homeworkRes.filter(
            (record) => record.homeworkStatus !== "completed"
          );
          console.log("completedHomework", completedHomework);
          console.log("inReviewHomework", inReviewHomework);

          const microcredentialParams = {
            ExpressionAttributeValues: {
              ":d": { S: "false" },
            },
            FilterExpression: "#D = :d",
            ExpressionAttributeNames: {
              "#D": "isDeleted",
            },
            TableName: `Programs-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          };

          const microcredentialResponse = await ddb
            .scan(microcredentialParams)
            .promise();
          const microcredentialRes = microcredentialResponse.Items.map(
            (records) => AWS.DynamoDB.Converter.unmarshall(records)
          );

          if (completedHomework.length > 0) {
            const completedHomeworkResponse = await fetchHomeworkData(
              completedHomework
            );
            console.log("completedHomeworkResponse", completedHomeworkResponse);
            const microcredentialIds = {};

            completedHomeworkResponse.forEach((item) => {
              const key = item.microcredentialId;
              if (!microcredentialIds[key]) {
                microcredentialIds[key] = {
                  assignmentPoints: 0,
                  completedHomeworkCount: 0,
                  oldestHomeworkAssignDate: item.homeworkAssignDate,
                };
              }
              microcredentialIds[key].assignmentPoints += item.assignmentPoints;
              microcredentialIds[key].completedHomeworkCount += 1;
              if (
                item.homeworkAssignDate <
                microcredentialIds[key].oldestHomeworkAssignDate
              ) {
                microcredentialIds[key].oldestHomeworkAssignDate =
                  item.homeworkAssignDate;
              }
              microcredentialIds[key].microcredentialId =
                item.microcredentialId;
            });
            resultArray = Object.values(microcredentialIds);
            console.log("resultArray", resultArray);
            for (const outer of resultArray) {
              const obj = microcredentialRes.find(
                (inner) => inner.id === outer.microcredentialId
              );

              if (obj) {
                const receivedPoints = outer.assignmentPoints;
                const homeworkAssignDate = outer.oldestHomeworkAssignDate;
                const completedHomeworkCount = outer.completedHomeworkCount;
                const remainingPoints =
                  obj.totalPoints <= receivedPoints
                    ? 0
                    : obj.totalPoints - receivedPoints;

                if (obj.totalPoints <= receivedPoints) {
                  completedMicrocredentails.push({
                    ...obj,
                    receivedPoints,
                    remainingPoints,
                    homeworkAssignDate,
                    completedHomeworkCount,
                  });
                } else {
                  pendingMicrocredentails.push({
                    ...obj,
                    receivedPoints,
                    remainingPoints,
                    homeworkAssignDate,
                    completedHomeworkCount,
                  });
                }
              }
            }
            console.log("completedMicrocredentails", completedMicrocredentails);
            console.log("pendingMicrocredentails", pendingMicrocredentails);
          }
          if (inReviewHomework.length > 0) {
            let inReviewHomeworkResponse = await fetchHomeworkData(
              inReviewHomework
            );
            const microcredentialIdsToRemove = resultArray.map(
              (cmchw) => cmchw.microcredentialId
            );
            inReviewHomeworkResponse = inReviewHomeworkResponse.filter(
              (irmchw) =>
                !microcredentialIdsToRemove.includes(irmchw.microcredentialId)
            );
            console.log("inReviewHomeworkResponse", inReviewHomeworkResponse);
            const microcredentialInreviewIds = {};

            inReviewHomeworkResponse.forEach((item) => {
              const key = item.microcredentialId;
              if (!microcredentialInreviewIds[key]) {
                microcredentialInreviewIds[key] = {
                  assignmentPoints: 0,
                  oldestHomeworkAssignDate: item.homeworkAssignDate,
                };
              }
              microcredentialInreviewIds[key].assignmentPoints +=
                item.assignmentPoints;
              if (
                item.homeworkAssignDate <
                microcredentialInreviewIds[key].oldestHomeworkAssignDate
              ) {
                microcredentialInreviewIds[key].oldestHomeworkAssignDate =
                  item.homeworkAssignDate;
              }
              microcredentialInreviewIds[key].microcredentialId =
                item.microcredentialId;
            });
            const resultArr = Object.values(microcredentialInreviewIds);
            console.log(resultArr);
            pendingMicrocredentailsArr = resultArr.map((outer) => {
              const obj = microcredentialRes.find(
                (inner) => inner.id === outer.microcredentialId
              );
              return {
                ...obj,
                receivedPoints: 0,
                remainingPoints: obj?.totalPoints || 0, // Ensure remaningPoints defaults to 0 if obj.totalPoints is undefined
                homeworkAssignDate: outer.oldestHomeworkAssignDate,
                completedHomeworkCount: 0,
              };
            });
            console.log(
              "pendingMicrocredentailsArr",
              pendingMicrocredentailsArr
            );
          }

          let pendingMicrocredentailsData = [
            ...pendingMicrocredentails,
            ...pendingMicrocredentailsArr,
          ];
          console.log(
            "pendingMicrocredentailsData",
            pendingMicrocredentailsData
          );
          let pendingMicroData = {};
          pendingMicrocredentailsData.map((ex, index) => {
            if (Object.keys(pendingMicroData).indexOf(ex.id) === -1) {
              pendingMicroData[ex.id] = ex;
            } else {
              let data = pendingMicroData[ex.id];
              if (data.receivedPoints < ex.receivedPoints) {
                pendingMicroData[ex.id].receivedPoints = ex.receivedPoints;
              }
              if (
                new Date(data.homeworkAssignDate) >
                new Date(ex.homeworkAssignDate)
              ) {
                pendingMicroData[ex.id].homeworkAssignDate =
                  ex.homeworkAssignDate;
              }
            }
          });

          if (isCompleted) {
            data = completedMicrocredentails;
          } else {
            data = Object.values(pendingMicroData);
          }

          callback(null, { data: data });
        } catch (error) {
          console.error(error);
          callback({
            error: "An error occurred while fetching microcredentials.",
          });
        }
        break;
      }
      case "checkEmailPhoneNumberExist": {
        console.log("checkEmailPhoneNumberExist");
        const email = event.arguments?.email;
        const phoneNumber = event.arguments?.phoneNumber;
        console.log("phoneNumber", phoneNumber);
        console.log("email", email);

        let isEmailExist = 0;
        let isPhoneNumberExist = 0;

        try {
          if (email) {
            const cognitoEmailParams = {
              AttributesToGet: ["name"],
              Filter: `email = "${email}"`,
              UserPoolId: USERPOOLID,
            };

            const listExistEmailResponse = await cognito
              .listUsers(cognitoEmailParams)
              .promise();
            isEmailExist = listExistEmailResponse?.Users?.length || 0;
          }

          if (phoneNumber) {
            const cognitoPhoneNumberParams = {
              AttributesToGet: ["phone_number"],
              Filter: `phone_number = "+1${phoneNumber}"`,
              UserPoolId: USERPOOLID,
            };

            const listExistPhoneNumberResponse = await cognito
              .listUsers(cognitoPhoneNumberParams)
              .promise();
            isPhoneNumberExist =
              listExistPhoneNumberResponse?.Users?.length || 0;
          }

          const message =
            isEmailExist && isPhoneNumberExist
              ? "Email and phone number both are already exist."
              : isEmailExist
              ? "Email already exists."
              : isPhoneNumberExist
              ? "Phone number already exists."
              : "No data found.";

          const rescheckEmailPhoneNumberExistResponse = {
            isExist: isEmailExist || isPhoneNumberExist ? 1 : 0,
            message,
          };

          callback(null, rescheckEmailPhoneNumberExistResponse);
        } catch (error) {
          console.error("Error:", error);
          callback(error, null);
        }
        break;
      }
      case "updateHomeworkStatus": {
        console.log("updateHomeworkStatus");
        console.log("event.arguments?.input", event.arguments?.input);
        let homeworkId = event?.arguments?.input?.homeworkId ?? "";
        let entityId = event?.arguments?.input?.entityId ?? "";
        let entityType = event?.arguments?.input?.entityType ?? "";
        const homeworkOrganizationsTable = `HomeworkOrganizations-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        const homeworkUsersTable = `HomeworkUsers-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        const params = {
          ExpressionAttributeValues: {
            ":m": { S: entityId },
            ":h": { S: homeworkId },
          },
          FilterExpression: "#M = :m and #H = :h",
          ExpressionAttributeNames: {
            "#M": entityType === "member" ? "memberId" : "studentStakeholderId",
            "#H": "homeworkId",
          },
          TableName:
            entityType === "member"
              ? homeworkOrganizationsTable
              : homeworkUsersTable,
        };
        try {
          const homeworkResponse = await ddb.scan(params).promise();
          const homeworkRes = homeworkResponse.Items.map((records) =>
            AWS.DynamoDB.Converter.unmarshall(records)
          );

          var homeworkparams = {
            TableName:
              entityType === "member"
                ? homeworkOrganizationsTable
                : homeworkUsersTable,
            Key: {
              id: { S: homeworkRes[0]?.id },
            },
            ExpressionAttributeNames: {
              "#HS": "homeworkStatus",
            },
            ExpressionAttributeValues: {
              ":hs": {
                S: "in-review",
              },
            },
            ReturnValues: "ALL_NEW",
            UpdateExpression: "SET #HS = :hs",
          };
          await ddb.updateItem(homeworkparams).promise();

          callback(null, {
            message: "Homework status updated.",
            statusCode: 200,
            data: { id: homeworkRes[0]?.id ?? "" },
          });
        } catch (error) {
          console.error(error);
          callback({
            error: "An error occurred while fetching microcredentials.",
          });
        }
        break;
      }
      case "latestAndPopularHomeworks": {
        console.log("latestAndPopularHomeworks");
        console.log("event.arguments?.input", event.arguments);
        let {
          memberId,
          cityId,
          memberType,
          type,
          assignmentType,
          isStakeholder,
        } = event?.arguments || {};
        let entity = "member";
        if (memberType.toLowerCase() === "person") {
          entity = isStakeholder ? "stakeholder" : "student";
        }
        //memberType = Person/Organization
        const homeworkTable = `Homework-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        const homeworkOrganizationsTable = `HomeworkOrganizations-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        const homeworkUsersTable = `HomeworkUsers-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        const associationTable = `Association-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

        try {
          const flattenedHomeworkRes = await Promise.all(
            cityId.map(async (cityId) => {
              const params = {
                ExpressionAttributeValues: {
                  ":d": { S: "false" },
                  ":c": { S: cityId },
                },
                FilterExpression: "#D = :d and #C = :c",
                ExpressionAttributeNames: {
                  "#D": "isDeleted",
                  "#C": "cityId",
                  "#V": "_version",
                  "#DD": "_deleted",
                  "#N": "name",
                },
                ProjectionExpression:
                  "id,#D, #N, imageUrl, dueDate, assignmentPoints, assignmentType, createdAt, #V, #DD, shortDescription, longDescription, coCreationType, cityId, microcredentialId, entityType",
                TableName: homeworkTable,
              };
              const homeworkResponse = await ddb.scan(params).promise();
              return homeworkResponse.Items.map((records) =>
                AWS.DynamoDB.Converter.unmarshall(records)
              );
            })
          );
          const homeworkRes = flattenedHomeworkRes.flat();
          console.log("homeworkRes", homeworkRes);

          const homeworkOrgParams = {
            ProjectionExpression: "id, homeworkId, homeworkStatus, memberId",
            TableName: homeworkOrganizationsTable,
          };
          const homeworkOrgResponse = await ddb
            .scan(homeworkOrgParams)
            .promise();
          const homeworkOrgRes = homeworkOrgResponse.Items.map((records) =>
            AWS.DynamoDB.Converter.unmarshall(records)
          );
          // console.log('homeworkOrgRes', homeworkOrgRes);

          const homeworkUserParams = {
            ProjectionExpression:
              "id, homeworkId, homeworkStatus, studentStakeholderId",
            TableName: homeworkUsersTable,
          };
          const homeworkUserResponse = await ddb
            .scan(homeworkUserParams)
            .promise();
          const homeworkUserRes = homeworkUserResponse.Items.map((records) =>
            AWS.DynamoDB.Converter.unmarshall(records)
          );
          // console.log('homeworkUserRes', homeworkUserRes);

          let associationParams = {};
          let expressionAttributeValues = {};
          let filterExpression = "";
          let expressionAttributeNames = {};
          let projectionExpression =
            "personsID, otherPersonsID, organizationID";

          if (memberType === "Person") {
            expressionAttributeValues = {
              ":u": { S: memberId },
              ":t": { S: "Person" },
            };
            filterExpression = "(#P = :u or #OP = :u) and #T = :t";
            expressionAttributeNames = {
              "#P": "personsID",
              "#OP": "otherPersonsID",
              "#T": "type",
            };
          } else {
            expressionAttributeValues = {
              ":o": { S: memberId },
              ":t": { S: "Organization" },
            };
            filterExpression = "#O = :o and #T = :t";
            expressionAttributeNames = {
              "#O": "organizationID",
              "#T": "type",
            };
          }

          associationParams = {
            ExpressionAttributeValues: expressionAttributeValues,
            FilterExpression: filterExpression,
            ExpressionAttributeNames: expressionAttributeNames,
            ProjectionExpression: projectionExpression,
            TableName: associationTable,
          };

          try {
            const associationRes = await ddb.scan(associationParams).promise();
            const associationNewRes = associationRes.Items.map((records) =>
              AWS.DynamoDB.Converter.unmarshall(records)
            );
            let assocationData = [
              ...new Set(
                associationNewRes.map((ex) => Object.values(ex)).flat()
              ),
            ];
            // console.log('associationNewRes', associationNewRes);

            let data = {};
            let homeworkAssignData = [...homeworkOrgRes, ...homeworkUserRes];

            homeworkAssignData.map((ex) => {
              if (Object.keys(data).indexOf(ex.homeworkId) === -1) {
                if (ex.hasOwnProperty("memberId")) {
                  data[ex.homeworkId] = [
                    { id: ex.memberId, status: ex.homeworkStatus },
                  ];
                } else if (ex.hasOwnProperty("studentStakeholderId")) {
                  data[ex.homeworkId] = [
                    { id: ex.studentStakeholderId, status: ex.homeworkStatus },
                  ];
                }
              } else {
                if (ex.hasOwnProperty("memberId")) {
                  data[ex.homeworkId].push({
                    id: ex.memberId,
                    status: ex.homeworkStatus,
                  });
                } else if (ex.hasOwnProperty("studentStakeholderId")) {
                  data[ex.homeworkId].push({
                    id: ex.studentStakeholderId,
                    status: ex.homeworkStatus,
                  });
                }
              }
            });

            Object.keys(data).map((ex) =>
              data[ex].map((ey) => {
                assocationData.indexOf(ey.id) === -1
                  ? (ey["association"] = false)
                  : (ey["association"] = true);
              })
            );
            // console.log('data', data);

            let popularData = data;
            let completeCount = {};
            Object.keys(popularData).map(
              (ex) =>
                (completeCount[ex] = popularData[ex].filter(
                  (ey) => ey.status === "completed"
                ).length)
            );
            // console.log('completeCount', completeCount)

            let newData = Object.fromEntries(
              Object.keys(data).map((ex) => [
                ex,
                data[ex].find((ey) => ey.association) === undefined
                  ? {}
                  : data[ex].find((ey) => ey.association),
              ])
            );
            // console.log('newData', newData)

            let keys = Object.keys(newData);
            let homeworkWithAssociatedData = homeworkRes.map((ex) =>
              keys.indexOf(ex.id) === -1
                ? { ...ex, associationId: null, association: false }
                : {
                    ...ex,
                    associationId: newData[ex.id]?.id,
                    association: newData[ex.id]?.association ?? false,
                  }
            );
            // console.log('homeworkWithAssociatedData', homeworkWithAssociatedData)

            let countKeys = Object.keys(completeCount);
            let homeworkWithCountData = homeworkWithAssociatedData.map((ex) =>
              countKeys.indexOf(ex.id) === -1
                ? { ...ex, completeCount: 0 }
                : { ...ex, completeCount: completeCount[ex.id] }
            );
            // console.log('homeworkWithCountData', homeworkWithCountData)

            if (type == "popular") {
              let popularHomeworkData = homeworkWithCountData;
              popularHomeworkData.sort(function (a, b) {
                return parseInt(b.completeCount) - parseInt(a.completeCount);
              });
              let filteredPopularHomeworkData = popularHomeworkData ?? [];

              // console.log('popularHomeworkData', R.splitEvery(5, popularHomeworkData));
              // return
              if (assignmentType != "all") {
                filteredPopularHomeworkData = popularHomeworkData.filter(
                  (data) =>
                    data.assignmentType === assignmentType &&
                    data.entityType === entity
                );
                // console.log('filteredPopularHomeworkData', filteredPopularHomeworkData);
              } else {
                console.log("entity", entity);
                console.log("popularHomeworkData", popularHomeworkData);
                filteredPopularHomeworkData = popularHomeworkData.filter(
                  (data) => data.entityType === entity
                );
              }

              // console.log('filteredPopularHomeworkData', filteredPopularHomeworkData)
              let popularHomeworkResponse = filteredPopularHomeworkData.map(
                (ex) => {
                  return {
                    homeworkStatus: null,
                    homeworkData: ex,
                    _deleted: null,
                  };
                }
              );
              console.log("popularHomeworkResponse", popularHomeworkResponse);
              callback(null, { items: popularHomeworkResponse });
            } else {
              const sortByDate = (arr, key) =>
                arr.slice().sort((a, b) => new Date(b[key]) - new Date(a[key]));

              let latesthomeworkData = sortByDate(
                homeworkWithCountData,
                "createdAt"
              );
              let filteredLatesthomeworkData = latesthomeworkData ?? [];
              // console.log('latesthomeworkData', latesthomeworkData);

              if (assignmentType != "all") {
                filteredLatesthomeworkData = latesthomeworkData.filter(
                  (data) =>
                    data.assignmentType === assignmentType &&
                    data.entityType === entity
                );
              } else {
                filteredLatesthomeworkData = latesthomeworkData.filter(
                  (data) => data.entityType === entity
                );
              }
              // console.log('filteredLatesthomeworkData', filteredLatesthomeworkData)
              let latesthomeworkResponse = filteredLatesthomeworkData.map(
                (ex) => {
                  return {
                    homeworkStatus: null,
                    homeworkData: ex,
                    _deleted: null,
                  };
                }
              );
              console.log("latesthomeworkResponse", latesthomeworkResponse);
              callback(null, { items: latesthomeworkResponse });
            }
          } catch (error) {
            console.error("Error scanning the association table:", error);
          }
        } catch (error) {
          console.error(error);
          callback({
            error: "An error occurred while fetching microcredentials.",
          });
        }
        break;
      }
      case "sendTokensToMembers": {
        console.log("sendTokensToMembers");
        console.log("event.arguments?.input", event.arguments?.input);
        let tokenAmount = event?.arguments?.input?.tokenAmount ?? 0;
        let projectId = event?.arguments?.input?.projectId ?? "";
        let membersId = event?.arguments?.input?.membersId ?? [];
        // let membersId = ["8573046a-d60f-4823-8848-472b4eea3f12", "d973b592-4b37-44ef-b979-d36851b0e84b", "34805acb-fe4e-4ff9-95ab-2d7775f2a753"] ?? "";
        let associationId = event?.arguments?.input?.associationId ?? "";
        const membershipTable = `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

        // Get Membership Data
        const params = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
            ":t": { S: "school" },
          },
          FilterExpression: "#D = :d and #T <> :t",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#T": "type",
            "#N": "name",
          },
          ProjectionExpression:
            "id, #N, personsID, organizationID, MVPTokens, fundTokens, currentImpactScore, cityId, imageUrl, #T",
          TableName: membershipTable,
        };
        try {
          const membershipResponse = await ddb.scan(params).promise();
          const membershipRes = membershipResponse.Items.map((records) =>
            AWS.DynamoDB.Converter.unmarshall(records)
          );
          // console.log('membershipRes', membershipRes);

          let associationData = membershipRes.find(
            (ex) =>
              ex?.personsID === associationId ||
              ex?.organizationID === associationId
          );
          console.log("associationData", associationData);

          // Get user Data
          const userParams = {
            ExpressionAttributeValues: {
              ":d": { S: "false" },
            },
            FilterExpression: "#D = :d",
            ExpressionAttributeNames: {
              "#D": "isDeleted",
              "#N": "name",
            },
            ProjectionExpression: "id, #N, isStakeholder, cityId, imageUrl",
            TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          };
          const userResponse = await ddb.scan(userParams).promise();
          const userRes = userResponse.Items.map((records) =>
            AWS.DynamoDB.Converter.unmarshall(records)
          );

          let moduleType = "member";
          if (associationData.type === "User") {
            let userData = userRes.find(
              (ex) => ex?.id === associationData.personsID
            );
            moduleType =
              userData.isStakeholder === true ? "stakeholder" : "student";
          }

          // Check token balance
          if (
            associationData &&
            associationData.fundTokens >= tokenAmount &&
            membersId.length
          ) {
            let activityData = [];
            let transactionData = [];

            // Get project detail
            const projectParams = {
              ExpressionAttributeValues: {
                ":d": { S: "false" },
                ":i": { S: projectId },
              },
              FilterExpression: "#D = :d and #I = :i",
              ExpressionAttributeNames: {
                "#D": "isDeleted",
                "#N": "name",
                "#I": "id",
              },
              ProjectionExpression: "id, #N",
              TableName: `Events-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
            };
            const projectResponse = await ddb.scan(projectParams).promise();
            const projectRes = projectResponse.Items.map((records) =>
              AWS.DynamoDB.Converter.unmarshall(records)
            );

            // Update donar balance
            let oldAssociationFund = associationData.fundTokens ?? 0;
            let currentAssociationFundTokens =
              parseFloat(parseFloat(oldAssociationFund) - tokenAmount).toFixed(
                2
              ) ?? 0;
            const updateMemberPara = {
              TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              Key: {
                id: { S: associationData.id },
              },
              ExpressionAttributeNames: {
                "#FT": "fundTokens",
              },
              ExpressionAttributeValues: {
                ":ft": { N: currentAssociationFundTokens.toString() },
              },
              ReturnValues: "ALL_NEW",
              UpdateExpression: "SET #FT = :ft",
            };
            let test = await ddb.updateItem(updateMemberPara).promise();
            console.log("test", test);

            let membersData = membershipRes.filter(
              (ex) =>
                membersId.indexOf(ex.personsID) !== -1 ||
                membersId.indexOf(ex.organizationID) !== -1
            );

            transactionData.push({
              PutRequest: {
                Item: {
                  id: { S: uuid.v4() },
                  __typename: { S: "CityFundTransactions" },
                  cityId: { S: associationData?.cityId ?? "" },
                  name: { S: associationData?.name ?? "" },
                  type: { S: "tokenTransfer" },
                  amount: { N: tokenAmount.toFixed(2).toString() },
                  amountStatus: { S: "DEBITED" },
                  recurring: { BOOL: false },
                  createdAt: { S: date.toISOString() },
                  updatedAt: { S: date.toISOString() },
                  _version: { N: "1" },
                  _lastChangedAt: { N: date.getTime().toString() },
                },
              },
            });

            for (const member of membersData) {
              let oldFund = member.fundTokens ?? 0;
              let currentToken =
                (tokenAmount / membersId.length).toFixed(2) ?? 0;
              let currentFundTokens =
                parseFloat(parseFloat(oldFund) + currentToken).toFixed(2) ?? 0;
              console.log("member", member);

              const updateMemberParams = {
                TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
                Key: {
                  id: { S: member.id },
                },
                ExpressionAttributeNames: {
                  "#FT": "fundTokens",
                },
                ExpressionAttributeValues: {
                  ":ft": { N: currentFundTokens.toString() },
                },
                ReturnValues: "ALL_NEW",
                UpdateExpression: "SET #FT = :ft",
              };
              let test2 = await ddb.updateItem(updateMemberParams).promise();
              console.log("test2", test2);

              let relatedTo = "member";
              if (member.type === "User") {
                let userData = userRes.find(
                  (ex) => ex?.id === member.personsID
                );
                relatedTo =
                  userData.isStakeholder === true ? "stakeholder" : "student";
              }

              activityData.push({
                PutRequest: {
                  Item: {
                    id: { S: uuid.v4() },
                    __typename: { S: "Activity" },
                    activityType: { S: "FUNDING" },
                    cityId: { S: associationData?.cityId ?? "" },
                    createdUserId: { S: associationData?.id ?? "" },
                    createdUserName: { S: associationData?.name ?? "" },
                    isDeleted: { S: "false" },
                    moduleId: { S: associationData?.id ?? "" },
                    moduleImageUrl: { S: associationData?.imageUrl ?? "" },
                    moduleName: { S: associationData?.name ?? "" },
                    moduleType: { S: moduleType ?? "" },
                    type: { S: "TOKENS" },
                    relatedId: {
                      S:
                        member?.type === "member"
                          ? member?.organizationID
                          : member?.personsID,
                    },
                    relatedName: { S: member?.name ?? "" },
                    relatedTo: { S: relatedTo ?? "" },
                    isRelationship: { BOOL: true },
                    requestStatus: { S: "SYSTEM_APPROVED" },
                    updatedData: {
                      M: {
                        id: { S: projectId },
                        givenName: { S: projectRes[0].name ?? "" },
                      },
                    },
                    activityTokens: { N: currentToken.toString() },
                    createdAt: { S: date.toISOString() },
                    updatedAt: { S: date.toISOString() },
                    _version: { N: "1" },
                    _lastChangedAt: { N: date.getTime().toString() },
                  },
                },
              });

              transactionData.push({
                PutRequest: {
                  Item: {
                    id: { S: uuid.v4() },
                    __typename: { S: "CityFundTransactions" },
                    cityId: { S: associationData?.cityId ?? "" },
                    name: { S: associationData?.name ?? "" },
                    type: { S: "tokenTransfer" },
                    memberId: {
                      S:
                        member?.type === "member"
                          ? member?.organizationID
                          : member?.personsID,
                    },
                    amount: { N: currentToken.toString() },
                    amountStatus: { S: "CREDITED" },
                    recurring: { BOOL: false },
                    createdAt: { S: date.toISOString() },
                    updatedAt: { S: date.toISOString() },
                    _version: { N: "1" },
                    _lastChangedAt: { N: date.getTime().toString() },
                  },
                },
              });
            }
            await ddbBatchWrite(
              `Activity-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              activityData
            );
            await ddbBatchWrite(
              `CityFundTransactions-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              transactionData
            );
          } else {
            callback(null, {
              message: "Insufficient MVP tokens.",
              statusCode: 400,
              data: {
                isBalance: false,
                fundTokens: associationData.fundTokens ?? 0,
              },
            });
          }

          callback(null, {
            message: "Tokens are successfully sent to members.",
            statusCode: 200,
            data: {
              isBalance: false,
              fundTokens: associationData.fundTokens ?? 0,
            },
          });
        } catch (error) {
          console.error(error);
          callback({
            error: "An error occurred while fetching microcredentials.",
          });
        }
        break;
      }
      case "deleteUserRelations": {
        console.log("deleteUserRelations");
        console.log("event.arguments?.input", event.arguments?.input);

        const homeworkRelationIds =
          event?.arguments?.input?.homeworkRelationIds ?? [];

        const HomeworkUsersTableName = `HomeworkUsers-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

        const deleteItems = async (tableName, ids) => {
          const promises = ids.map(async (id) => {
            const deleteParams = {
              TableName: tableName,
              Key: {
                id: { S: id },
              },
            };
            try {
              await ddb.deleteItem(deleteParams).promise();
            } catch (error) {
              console.log(error, error.stack);
            }
          });
          await Promise.all(promises);
        };

        await Promise.all([
          deleteItems(HomeworkUsersTableName, homeworkRelationIds),
        ]);

        callback(null, {
          message: "User relations are deleted.",
          statusCode: 200,
        });
        break;
      }
      case "clearChatByUser": {
        console.log("clearChatByUser");
        console.log("event.arguments?.input", event.arguments?.input);

        const userId = event?.arguments?.input?.userId ?? "";
        const chatType = event?.arguments?.input?.chatType ?? "";

        const ChatGPTTableName = `ChatGPT-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        let ChatGPTDataIds = [];

        const ChatGPTsParams = {
          ExpressionAttributeValues: {
            ":u": { S: userId },
            ":ct": { S: chatType },
          },
          FilterExpression: "#U = :u and #CT = :ct",
          ExpressionAttributeNames: {
            "#U": "userId",
            "#CT": "chatType",
          },
          ProjectionExpression: "id",
          TableName: ChatGPTTableName,
        };

        try {
          const ChatGPTsResponse = await ddb.scan(ChatGPTsParams).promise();
          ChatGPTDataIds = ChatGPTsResponse.Items.map(
            (records) => AWS.DynamoDB.Converter.unmarshall(records).id
          );
          console.log("ChatGPTDataIds", ChatGPTDataIds);
        } catch (error) {
          console.error(error);
          return callback({
            error: "An error occurred while scanning ChatGPT data",
          });
        }

        const deleteItems = async (tableName, ids) => {
          const promises = ids.map(async (id) => {
            const deleteParams = {
              TableName: tableName,
              Key: {
                id: { S: id },
              },
            };
            try {
              await ddb.deleteItem(deleteParams).promise();
            } catch (error) {
              console.log(error, error.stack);
            }
          });
          await Promise.all(promises);
        };

        await Promise.all([deleteItems(ChatGPTTableName, ChatGPTDataIds)]);

        callback(null, { message: "Chat has been cleared.", statusCode: 200 });
        break;
      }
      case "createChannelUser": {
        console.log("createChannelUser");
        console.log("event.arguments", event.arguments);
        const { userId } = event.arguments;

        const params = {
          TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          Key: { id: { S: userId } },
        };
        const getUserResponse = await ddb.getItem(params).promise();
        console.log("getUserResponse", getUserResponse);
        const userItem = getUserResponse["Item"]
          ? AWS.DynamoDB.Converter.unmarshall(getUserResponse["Item"])
          : null;

        const scanParams = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
            ":r": { S: "SUPER_ADMIN" },
          },
          FilterExpression: "#D = :d and #R = :r",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#R": "role",
          },
          ProjectionExpression: "id",
          TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };
        const scanResponse = await ddb.scan(scanParams).promise();
        let records = scanResponse.Items.map((item) =>
          AWS.DynamoDB.Converter.unmarshall(item)
        );

        console.log("records", records);

        if (userItem) {
          const createChannel = await channelCreate(userId, records);
          callback(null, {
            message: "channel has been cleared.",
            statusCode: 200,
          });
        } else {
          callback(null, { message: "User not found.", statusCode: 401 });
        }
      }

      case "deleteUserMessages": {
        const { userId } = event.arguments;
        await deleteUserMessage(userId);
        callback(null, {
          message: "messgae has been cleared.",
          statusCode: 200,
        });
      }
      default:
        callback("Unknown field, unable to resolve" + event.fieldName, null);
        break;
    }
  } catch (error) {
    const err = createErrorResponse(error);
    console.log("error", err);
    callback(err);
  }
};

function calculateCityPoints(data) {
  let points = 0;

  data.forEach((record) => {
    if (record.impactScore) points += parseFloat(record.impactScore);
  });

  return points || 0.0;
}

function createErrorResponse(error) {
  return {
    isSuccess: false,
    statusCode: error.statusCode ? error.statusCode : 400,
    message: error.message,
  };
}

const ddbBatchWrite = async (tableName, data) => {
  if (data && data.length) {
    const params = {
      RequestItems: {
        [tableName]: data,
      },
    };
    await ddb.batchWriteItem(params).promise();
  }
};

const updateMembership = async (organizationsIds, personsIds) => {
  const membershipParams = {
    ExpressionAttributeValues: {
      ":o": { S: organizationsIds },
    },
    FilterExpression: "#O = :o",
    ExpressionAttributeNames: {
      "#O": "organizationID",
      "#N": "name",
    },
    ProjectionExpression: "id, currentImpactScore, #N, MVPTokens",
    TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
  };

  const membershipResponse = await ddb.scan(membershipParams).promise();
  const membershipIds = membershipResponse.Items.map((record) =>
    AWS.DynamoDB.Converter.unmarshall(record)
  );

  if (membershipIds.length > 0) {
    const membershipIdData = membershipIds[0];
    console.log("membershipIdData", membershipIdData);
    const impactScore = parseFloat(membershipIdData?.currentImpactScore) || 0;
    const MVPTokens = parseFloat(membershipIdData?.MVPTokens) || 0;
    const currentImpactScore = impactScore + 100;
    const currentMVPTokens = MVPTokens + 25;

    const updateMemberParams = {
      TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
      Key: {
        id: { S: membershipIdData.id },
      },
      ExpressionAttributeNames: {
        "#CIS": "currentImpactScore",
        "#LAIS": "lastAddedImpactScore",
        "#MVP": "MVPTokens",
      },
      ExpressionAttributeValues: {
        ":cis": { S: currentImpactScore.toString() },
        ":lais": { S: "100" },
        ":mvp": { N: currentMVPTokens.toString() },
      },
      ReturnValues: "ALL_NEW",
      UpdateExpression: "SET #CIS = :cis, #LAIS = :lais, #MVP = :mvp",
    };

    await ddb.updateItem(updateMemberParams).promise();

    const params = {
      ExpressionAttributeValues: {
        ":o": { S: organizationsIds },
        ":t": { S: "Organization" },
      },
      FilterExpression: "#O = :o and #T = :t",
      ExpressionAttributeNames: {
        "#O": "organizationID",
        "#T": "type",
      },
      ProjectionExpression: "personsID",
      TableName: `Association-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };

    const assocResponse = await ddb.scan(params).promise();
    const assocNewResponse = assocResponse.Items.map((record) =>
      AWS.DynamoDB.Converter.unmarshall(record)
    );
    personsIds[membershipIdData?.name] = assocNewResponse;
  }
};

const processMembershipUpdates = async (organizations) => {
  const personsIds = {};

  for (const organizationsIds of organizations) {
    await updateMembership(organizationsIds, personsIds);
  }

  return personsIds;
};

const getUserDataForPersonsIds = async (personsIds) => {
  console.log("personsIds function", personsIds);
  const notificationData = {};

  for (const key in personsIds) {
    const personData = [];

    for (const personId of personsIds[key]) {
      const params = {
        ExpressionAttributeValues: {
          ":p": { S: personId.personsID },
          ":e": { S: "arn:aws:sns" },
        },
        FilterExpression: "#P = :p and contains(#E, :e) ",
        ExpressionAttributeNames: {
          "#P": "id",
          "#E": "endpointArn",
        },
        ProjectionExpression: "id, endpointArn, isLogin",
        TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
      };

      const userResponse = await ddb.scan(params).promise();
      const userNewResponse = userResponse.Items.map((record) =>
        AWS.DynamoDB.Converter.unmarshall(record)
      );
      personData.push(...userNewResponse);
    }

    notificationData[key] = personData;
  }

  return notificationData;
};

const sendNotifications = async (notificationData, type) => {
  for (const notification in notificationData) {
    console.log("type", type);
    const notificationFunctionName = `notificationFunction-${process.env.ENV}`;
    const payload = {
      arguments: {
        input: {
          title: `${notification} received 25 MVP tokens for ${type}`,
          body: "",
          taskNotificationsId: "null",
          notificationType: "points",
          MVPTokens: "25",
          points: "100",
          isAnswerable: false,
          notificationIcon: "notificationIcons/mvpNotificationIcon.png",
          userList: notificationData[notification],
        },
      },
    };
    const notificationParams = {
      FunctionName: notificationFunctionName,
      InvocationType: "RequestResponse",
      LogType: "None",
      Payload: JSON.stringify(payload),
    };

    await lambda.invoke(notificationParams).promise();
  }
};

async function fetchHomeworkData(homeworkArray) {
  let homeworkData = [];
  for (const homeworkId of homeworkArray) {
    const params = {
      ExpressionAttributeValues: {
        ":i": { S: homeworkId.homeworkId },
      },
      FilterExpression: "#I = :i",
      ExpressionAttributeNames: {
        "#I": "id",
      },
      ProjectionExpression: "id, microcredentialId, assignmentPoints",
      TableName: `Homework-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };

    const response = await ddb.scan(params).promise();
    let newResponse = response.Items.map((record) =>
      AWS.DynamoDB.Converter.unmarshall(record)
    );
    if (newResponse.length > 0) {
      newResponse[0]["homeworkAssignDate"] =
        homeworkId?.homeworkAssignDate ?? homeworkId?.createdAt;
      homeworkData.push(...newResponse);
    }
  }
  return homeworkData;
}

async function fetchPointsData(cityId) {
  const params = {
    ExpressionAttributeValues: {
      ":d": { S: "false" },
      ":c": { S: cityId },
    },
    FilterExpression: "#D = :d and #C = :c",
    ExpressionAttributeNames: {
      "#D": "isDeleted",
      "#C": "cityId",
      "#T": "type",
    },
    ProjectionExpression:
      "id, impactScore, memberId, category, MVPTokens, #T, cityId",
    TableName: `Points-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
  };
  const pointsRes = await ddb.scan(params).promise();
  const pointsResponse = pointsRes.Items.map((records) =>
    AWS.DynamoDB.Converter.unmarshall(records)
  );
  return pointsResponse;
}

async function fetchMicrocredentialData(cityId) {
  const microcredentialParams = {
    ExpressionAttributeValues: {
      ":d": { S: "false" },
      ":c": { S: cityId },
    },
    FilterExpression: "#D = :d and #C = :c",
    ExpressionAttributeNames: {
      "#D": "isDeleted",
      "#C": "cityId",
    },
    TableName: `Programs-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
  };
  const microcredentialResponse = await ddb
    .scan(microcredentialParams)
    .promise();
  const microcredentialRes = microcredentialResponse.Items.map((records) =>
    AWS.DynamoDB.Converter.unmarshall(records)
  );
  return microcredentialRes;
}

async function getAssociatedIds(memberId) {
  var associationPara = {
    ExpressionAttributeValues: {
      ":m": { S: memberId },
    },
    FilterExpression: "#P = :m or #OP = :m or #O = :m",
    ExpressionAttributeNames: {
      "#P": "personsID",
      "#OP": "otherPersonsID",
      "#O": "organizationID",
    },
    ProjectionExpression: "personsID, otherPersonsID, organizationID",
    TableName:
      "Association-" +
      process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
      "-" +
      process.env.ENV,
  };
  let associationRes = await ddb.scan(associationPara).promise();
  let associationResponse = associationRes.Items.map((records) =>
    AWS.DynamoDB.Converter.unmarshall(records)
  );
  return associationResponse;
}

async function fetchCategoriesData() {
  const params = {
    ExpressionAttributeValues: {
      ":d": { S: "false" },
    },
    FilterExpression: "#D = :d",
    ExpressionAttributeNames: {
      "#D": "isDeleted",
      "#N": "name",
    },
    ProjectionExpression: "id, #N",
    TableName: `Categories-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
  };
  const categoriesRes = await ddb.scan(params).promise();
  const categoriesResponse = categoriesRes.Items.map((records) =>
    AWS.DynamoDB.Converter.unmarshall(records)
  );
  return categoriesResponse;
}

async function fetchAssignmentsByMicrocredentialId(id, type, memberId) {
  try {
    console.log("Fetching assignments for ID:", id, "Type:", type);

    const homeworkArraycredentialParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":m": { S: id },
      },
      FilterExpression: "#D = :d and #M = :m",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#M": "microcredentialId",
      },
      TableName: `Homework-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };

    const homeworkArraycredentialResponse = await ddb
      .scan(homeworkArraycredentialParams)
      .promise();

    const homeworkArraycredentialRes =
      homeworkArraycredentialResponse.Items.map((records) =>
        AWS.DynamoDB.Converter.unmarshall(records)
      );

    if (
      !Array.isArray(homeworkArraycredentialRes) ||
      homeworkArraycredentialRes.length === 0
    ) {
      return []; // Early exit if no records found
    }

    const tableName =
      // type === "member"
      //   ? `HomeworkOrganizations-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`
      `Submission-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

    const results = await Promise.all(
      homeworkArraycredentialRes.map(async (record) => {
        const subParams = {
          ExpressionAttributeValues: {
            ":m": { S: record.id }, // Use the 'id' from the current record
            ":hs": { S: "APPROVED" },
            ":pi": { S: memberId },
          },
          FilterExpression: "#M = :m and #HS = :hs and #PI = :pi",
          ExpressionAttributeNames: {
            "#M": "projectId",
            "#HS": "submissionStatus",
            "#PI": "memberId",
          },
          TableName: tableName,
        };

        const subResponse = await ddb.scan(subParams).promise();
        const subRes = subResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );

        if (subRes.length > 0) {
          return {
            id: record.id,
            name: record.name,
            imageUrl: record.imageUrl,
          };
        }

        return null; // Return null for empty results to filter out later
      })
    );

    return results.filter((res) => res !== null); // Filter out null values
  } catch (error) {
    console.error("Error fetching assignments:", error);
    throw error;
  }
}

async function channelCreate(userId, adminIds = []) {
  try {
    // adminIds.push({ id: userId })

    let date = new Date();
    const createChannelParams = {
      Item: {
        id: { S: userId },
        __typename: { S: "Channel" },
        name: { S: userId },
        createdAt: { S: date.toISOString() },
        updatedAt: { S: date.toISOString() },
        _version: { N: "0" },
        _lastChangedAt: { N: date.getTime().toString() },
      },
      TableName: `Channel-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };

    let createChannel = await ddb.putItem(createChannelParams).promise();

    // for (const admin of adminIds) {
    //   const createUserChannelParams = {
    //     Item: {
    //       id: { S: `${admin.id}-${userId}` },
    //       __typename: { S: "UserChannel" },
    //       userId: { S: admin.id },
    //       channelId: { S: userId },
    //       isDeleted: { S: "false" },
    //       createdAt: { S: date.toISOString() },
    //       updatedAt: { S: date.toISOString() },
    //       _version: { N: "0" },
    //       _lastChangedAt: { N: date.getTime().toString() }
    //     },
    //     TableName: `UserChannel-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    //   };

    //   await ddb.putItem(createUserChannelParams).promise();
    // }
    return true;
  } catch (error) {
    const err = createErrorResponse(error);
    console.log("error", err);
    return err;
  }
}

async function deleteUserMessage(userId) {
  try {
    var params = {
      TableName:
        "Channel-" +
        process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
        "-" +
        process.env.ENV,
      Key: {
        id: { S: userId },
      },
      ExpressionAttributeNames: {
        "#D": "isDeleted",
      },
      ExpressionAttributeValues: {
        ":d": {
          S: "true",
        },
      },
      ReturnValues: "ALL_NEW",
      UpdateExpression: "SET #D = :d",
    };

    await ddb.updateItem(params).promise();

    let getMessage = {
      ExpressionAttributeValues: {
        ":c": { S: userId },
      },
      FilterExpression: "#C = :c",
      ExpressionAttributeNames: {
        "#C": "channelId",
      },
      ProjectionExpression: "id",
      TableName: `Message-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };
    let getMessageRes = await ddb.scan(getMessage).promise();
    let data = await getMessageRes.Items.map((records) =>
      AWS.DynamoDB.Converter.unmarshall(records)
    );
    console.log("data", data);

    if (data && data.length > 0) {
      await Promise.all(
        data.map(async (item) => {
          let params = {
            TableName:
              "Message-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            Key: {
              id: { S: item.id },
            },
            ExpressionAttributeNames: {
              "#D": "isDeleted",
            },
            ExpressionAttributeValues: {
              ":d": {
                S: "true",
              },
            },
            ReturnValues: "ALL_NEW",
            UpdateExpression: "SET #D = :d",
          };

          await ddb.updateItem(params).promise();
        })
      );
    }
    return true;
  } catch (error) {
    const err = createErrorResponse(error);
    console.log("error", err);
    return err;
  }
}
