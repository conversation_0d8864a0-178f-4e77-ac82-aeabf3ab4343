const hre = require("hardhat");
const readline = require("readline");
const { upgrades } = hre;

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

async function ask(question) {
  return new Promise((resolve) => rl.question(question, resolve));
}

async function verifyImplementation(implAddress) {
  try {
    console.log(`🔍 Verifying implementation at ${implAddress}...`);
    await hre.run("verify:verify", {
      address: implAddress,
      constructorArguments: [],
    });
    console.log("✅ Verified on Etherscan!");
  } catch (error) {
    console.error("❌ Verification failed:", error.message || error);
  }
}

async function main() {
  const mode = await ask("🛠 Is this a (n)ew deployment or an (u)pgrade? (n/u): ");

  const [deployer] = await hre.ethers.getSigners();
  console.log("✅ Deployer:", await deployer.getAddress());

  if (mode.toLowerCase() === "n") {
    // 🔹 Deploy MVTToken as Proxy
    console.log("🚀 Deploying MVTToken (Proxy)...");
    const MVTToken = await hre.ethers.getContractFactory("MVTToken");
    const mvtToken = await upgrades.deployProxy(MVTToken, ["MVT Token", "MVT"], {
      initializer: "initialize",
      kind: "transparent",
    });
    await mvtToken.waitForDeployment();
    const mvtTokenAddress = await mvtToken.getAddress();
    console.log("✅ MVTToken Proxy deployed at:", mvtTokenAddress);

    const mvtImpl = await upgrades.erc1967.getImplementationAddress(mvtTokenAddress);
    console.log("🔧 MVTToken Impl at:", mvtImpl);
    await verifyImplementation(mvtImpl);

    // 🔹 Deploy WithdrawMVTToken as Proxy
    const usdcTokenAddress = "******************************************";
    console.log("🚀 Deploying WithdrawMVTToken (Proxy)...");
    const Withdraw = await hre.ethers.getContractFactory("WithdrawMVTToken");
    const withdraw = await upgrades.deployProxy(Withdraw, [mvtTokenAddress, usdcTokenAddress], {
      initializer: "initialize",
      kind: "transparent",
    });
    await withdraw.waitForDeployment();
    const withdrawAddress = await withdraw.getAddress();
    console.log("✅ WithdrawMVTToken Proxy deployed at:", withdrawAddress);

    const withdrawImpl = await upgrades.erc1967.getImplementationAddress(withdrawAddress);
    console.log("🔧 WithdrawMVTToken Impl at:", withdrawImpl);
    await verifyImplementation(withdrawImpl);

    // ✅ Display Summary
    console.log("\n📌 Final Deployed Contract Addresses:");
    console.log("➡️ MVTToken Proxy:       ", mvtTokenAddress);
    console.log("➡️ WithdrawMVTToken Proxy:", withdrawAddress);

  } else if (mode.toLowerCase() === "u") {
    const contractToUpgrade = await ask("🔁 Which contract do you want to upgrade? (1) MVTToken (2) WithdrawMVTToken: ");

    if (contractToUpgrade === "1") {
      const proxyAddress = await ask("🔗 Enter existing MVTToken proxy address: ");
      const NewMVTToken = await hre.ethers.getContractFactory("MVTTokenV2");
      const upgraded = await upgrades.upgradeProxy(proxyAddress, NewMVTToken, {
        kind: "transparent",
      });
      console.log("✅ MVTToken upgraded at:", await upgraded.getAddress());

      const impl = await upgrades.erc1967.getImplementationAddress(proxyAddress);
      console.log("🔧 New Impl at:", impl);
      await verifyImplementation(impl);

    } else if (contractToUpgrade === "2") {
      const proxyAddress = await ask("🔗 Enter existing WithdrawMVTToken proxy address: ");
      const NewWithdraw = await hre.ethers.getContractFactory("WithdrawMVTTokenV2");
      const upgraded = await upgrades.upgradeProxy(proxyAddress, NewWithdraw, {
        kind: "transparent",
      });
      console.log("✅ WithdrawMVTToken upgraded at:", await upgraded.getAddress());

      const impl = await upgrades.erc1967.getImplementationAddress(proxyAddress);
      console.log("🔧 New Impl at:", impl);
      await verifyImplementation(impl);
    } else {
      console.log("❌ Invalid option.");
    }
  } else {
    console.log("❌ Invalid input. Exiting.");
  }

  rl.close();
}

main().catch((error) => {
  console.error("❌ Deployment failed:", error);
  process.exitCode = 1;
});
