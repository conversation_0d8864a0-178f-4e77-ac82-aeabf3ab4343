/* Amplify Params - DO NOT EDIT
  API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT
  API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT
  AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID
  ENV
  REGION
  STORAGE_S3MYVILLAGEPROJECTADMINPORTAL<PERSON><PERSON>ROFI<PERSON>LOGO_BUCKETNAME
Amplify Params - DO NOT EDIT */

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
const AWS = require("aws-sdk");
const uuid = require("uuid");
const R = require("ramda");
const axios = require("axios");

console.log(" region: process.env.REGION: ", process.env.REGION);
console.log("ACCESS_KEY_ID", process.env.ACCESS_KEY_ID);
console.log("process.env.SECRET_ACCESS_KEY: ", process.env.SECRET_ACCESS_KEY);

AWS.config.update({
  maxRetries: 3,
  httpOptions: { timeout: 30000, connectTimeout: 5000 },
  region: process.env.REGION,
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY,
});

const USERPOOLID = process.env.AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID;
const ddb = new AWS.DynamoDB();
const s3 = new AWS.S3();
const lambda = new AWS.Lambda();

/*Initializing CognitoIdentityServiceProvider from AWS SDK JS*/
const cognito = new AWS.CognitoIdentityServiceProvider({
  apiVersion: "2016-04-18",
});

const ddbBatchWrite = async (tableName, data) => {
  if (data?.length) {
    const params = {
      RequestItems: {
        [tableName]: data,
      },
      ReturnConsumedCapacity: "INDEXES",
    };
    const addData = await ddb.batchWriteItem(params).promise();
    return addData.ConsumedCapacity[0].GlobalSecondaryIndexes
      ? addData.ConsumedCapacity[0].GlobalSecondaryIndexes
      : 0;
  }
};

exports.handler = async (event, context, callback) => {
  try {
    let date = new Date();
    switch (event.fieldName) {
      case "importOrganizations": {
        console.log("importOrganizations");
        let items = event.arguments?.input?.items;
        const cityArray = [];

        let params = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
          },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#N": "name",
          },
          ProjectionExpression: "id, #N",
          TableName:
            "City-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        const citiesTableResponse = await ddb.scan(params).promise();
        const citiesResponse = await citiesTableResponse?.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        console.log("citiesResponse", citiesResponse);
        citiesResponse.map((rec) => {
          console.log(
            "rec?.name?.toLowerCase().replace",
            rec?.name?.toLowerCase().replace(" ", "_")
          );
          let cityNameKey = rec?.name?.toLowerCase().replace(" ", "_");
          cityArray[cityNameKey] = rec?.id;
        });

        let failDataCount = 0;
        let createOrg;
        let failArray = [];
        let failToUpload = 0;

        console.log("items", items);
        const scanParams = {
          ExpressionAttributeValues: { ":d": { S: "false" } },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: { "#D": "isDeleted" },
          ProjectionExpression: "memberCode",
          TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };
        const scanResponse = await ddb.scan(scanParams).promise();

        const newResponse = scanResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        const memberCodes = newResponse.map(
          (item) => parseInt(item.memberCode) || 0
        );
        let highestMemberCode = Math.max(...memberCodes);
        console.log("highestMemberCode: ", highestMemberCode);

        let itemSegments = R.splitEvery(25, items);
        console.log("itemSegments", itemSegments);
        for (const element of itemSegments) {
          console.log("itemSegments[i]", element);
          let organizationData = [];
          let userData = [];
          let associationData = [];
          let membershipOrgData = [];
          let membershipUserData = [];

          for (let rec of element) {
            highestMemberCode = highestMemberCode + 1;

            let organizationId = uuid.v4();
            let membershipUserId = uuid.v4();
            let membershipOrgId = uuid.v4();
            let associationDataId = uuid.v4();
            let userExist = 1;
            let existEmailList = [];
            let membershipOrgMVP = "0.0";
            let userId = uuid.v4();
            let contactFullName = "";

            if (rec?.contactEmail) {
              const checkUserExistParams = {
                ExpressionAttributeValues: {
                  ":d": { S: "false" },
                  ":e": { S: rec.contactEmail.toLowerCase() },
                },
                FilterExpression: "#D = :d and #E = :e",
                ExpressionAttributeNames: {
                  "#D": "isDeleted",
                  "#E": "email",
                },
                TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              };

              const checkUserExistParamsRes = await ddb
                .scan(checkUserExistParams)
                .promise();
              let records = checkUserExistParamsRes.Items.map((item) =>
                AWS.DynamoDB.Converter.unmarshall(item)
              );
              userExist = records.length ?? 0;
              console.log("userExist", userExist);
              failToUpload += userExist;
              if (userExist > 0) {
                existEmailList.push(rec?.contactEmail);
              }

              let cityName = rec?.cityId
                ? rec.cityId.toLowerCase().replace(" ", "_")
                : "";
              let cityId = cityArray[cityName];
              let phoneNoFordb = "0000000000";
              phoneNoFordb = rec?.contactPhoneNumber.replace(/-/g, "");
              phoneNoFordb = phoneNoFordb.replace(/\s/g, "");
              phoneNoFordb = phoneNoFordb.replace("+1", "");
              contactFullName =
                rec?.contactFirstName && rec?.contactLastName
                  ? rec?.contactFirstName + " " + rec?.contactLastName
                  : "";

              organizationData.push({
                PutRequest: {
                  Item: {
                    id: { S: organizationId },
                    __typename: { S: "Organizations" },
                    type: { S: rec?.type ?? "" },
                    teamSize: { S: rec?.teamSize ?? "" },
                    name: { S: rec?.name ?? "" },
                    organizationStructure: {
                      S: rec?.organizationStructure ?? "",
                    },
                    chapterName: { S: rec?.chapterName ?? "" },
                    organizationFunction: {
                      S: rec?.organizationFunction ?? "",
                    },
                    shortDescription: { S: rec?.shortDescription ?? "" },
                    longDescription: { S: rec?.longDescription ?? "" },
                    estimatedAnnualBudget: {
                      N: rec?.estimatedAnnualBudget
                        ? rec.estimatedAnnualBudget.toString()
                        : "",
                    },
                    organizationImpact: { S: rec?.organizationImpact ?? "" },
                    contactFullName: { S: contactFullName ?? "" },
                    contactEmail: { S: rec?.contactEmail.toLowerCase() ?? "" },
                    contactPhoneNumber: { S: `+1${phoneNoFordb || ""}` },
                    contactRole: { S: "MEMBER" },
                    isActive: { BOOL: rec?.isActive ?? "" },
                    MOUSigned: { BOOL: rec?.MOUSigned ?? false },
                    boardConfirmed: { BOOL: rec?.boardConfirmed ?? false },
                    cityId: { S: cityId ?? cityArray["jacksonville"] },
                    status: { S: rec?.status ?? "" },
                    imageUrl: { S: rec?.imageUrl ?? "" },
                    membershipId: { S: membershipOrgId ?? "" },
                    createdBy: { S: rec?.createdBy ?? "" },
                    organizationUserId: { S: userId ?? "" },
                    isDeleted: { S: "false" },
                    createdAt: { S: date.toISOString() },
                    updatedAt: { S: date.toISOString() },
                    _version: { N: "1" },
                    _lastChangedAt: { N: date.getTime().toString() },
                  },
                },
              });

              if (userId) {
                membershipOrgMVP = "1.25";
                if (userExist == 0) {
                  userData.push({
                    PutRequest: {
                      Item: {
                        id: { S: userId },
                        __typename: { S: "User" },
                        email: { S: rec?.contactEmail.toLowerCase() ?? "" },
                        familyName: { S: rec?.contactLastName ?? "" },
                        givenName: { S: rec?.contactFirstName ?? "" },
                        phoneNumber: { S: `+1${phoneNoFordb ?? ""}` },
                        name: { S: contactFullName ?? "" },
                        role: { S: "MEMBER" },
                        assignedRole: { S: "MEMBER" },
                        cityId: { S: cityId ?? cityArray["jacksonville"] },
                        isAssociated: { BOOL: true },
                        registeredFrom: { S: "WEB" },
                        isDeleted: { S: "false" },
                        membershipId: { S: membershipUserId ?? "" },
                        isStakeholder: { BOOL: true },
                        stackholderCities: { L: [{ S: cityArray[cityName] }] },
                        userAddedFrom: { S: "USER_IMPORTED_MEMBER" },
                        gender: { S: "NONE" },
                        userType: {
                          S: "loginUser",
                        },
                        memberCode: {
                          S:
                            highestMemberCode.toString().padStart(4, "0") ?? "",
                        },
                        status: { S: "inactive" },
                        isSignup: { BOOL: false },

                        createdAt: { S: date.toISOString() },
                        updatedAt: { S: date.toISOString() },
                        _version: { N: "1" },
                        _lastChangedAt: { N: date.getTime().toString() },
                      },
                    },
                  });

                  membershipUserData.push({
                    PutRequest: {
                      Item: {
                        id: { S: membershipUserId },
                        __typename: { S: "Membership" },
                        type: { S: "User" },
                        name: { S: contactFullName ?? "" },
                        personsID: { S: userId ?? "" },
                        cityId: { S: cityId ?? cityArray["jacksonville"] },
                        // lastAddedImpactScore: { S: "5" },
                        // currentImpactScore: { S: "5" },
                        // MVPTokens: { N: "1.25" },
                        memberCode: {
                          S:
                            highestMemberCode.toString().padStart(4, "0") ?? "",
                        },
                        isDeleted: { S: "false" },
                        createdAt: { S: date.toISOString() },
                        updatedAt: { S: date.toISOString() },
                        _version: { N: "1" },
                        _lastChangedAt: { N: date.getTime().toString() },
                      },
                    },
                  });
                }

                associationData.push({
                  PutRequest: {
                    Item: {
                      id: { S: associationDataId ?? uuid.v4() },
                      __typename: { S: "Association" },
                      type: { S: "Organization" },
                      relationType: { S: "Organization Leadership" },
                      status: { BOOL: false },
                      cityId: { S: cityId ?? cityArray["jacksonville"] },
                      organizationID: { S: organizationId ?? "" },
                      organizationsAssociationsId: { S: organizationId ?? "" },
                      userUserAssociationsId: { S: userId ?? "" },
                      personsID: { S: userId ?? "" },
                      isDeleted: { S: "false" },
                      createdAt: { S: date.toISOString() },
                      updatedAt: { S: date.toISOString() },
                      _version: { N: "1" },
                      _lastChangedAt: { N: date.getTime().toString() },
                    },
                  },
                });
              }

              membershipOrgData.push({
                PutRequest: {
                  Item: {
                    id: { S: membershipOrgId },
                    __typename: { S: "Membership" },
                    type: { S: "member" },
                    name: { S: rec?.name ?? "" },
                    imageUrl: { S: rec?.imageUrl ?? "" },
                    organizationID: { S: organizationId ?? "" },
                    cityId: { S: cityId ?? cityArray["jacksonville"] },
                    shortDescription: { S: rec?.shortDescription ?? "" },
                    // lastAddedImpactScore: { S: membershipOrgScore ?? "0.0" },
                    // currentImpactScore: { S: membershipOrgScore ?? "0.0" },
                    MVPTokens: { N: membershipOrgMVP ?? "0.0" },
                    isDeleted: { S: "false" },
                    createdAt: { S: date.toISOString() },
                    updatedAt: { S: date.toISOString() },
                    _version: { N: "1" },
                    _lastChangedAt: { N: date.getTime().toString() },
                  },
                },
              });
            }
          }

          createOrg = await ddbBatchWrite(
            "Organizations-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            organizationData
          );

          if (failDataCount >= 1) {
            failArray.push(existEmailList);
            console.log("failArray", failArray);
            if (failDataCount === 1) {
              failArray.push(`${failDataCount} organization is not uploaded`);
              failArray.push(existEmailList);
              console.log("failArray", failArray);
            } else {
              failArray.push(`${failDataCount} organizations are not uploaded`);
            }
          }

          await ddbBatchWrite(
            "User-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            userData
          );
          await ddbBatchWrite(
            "Association-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            associationData
          );
          await ddbBatchWrite(
            "Membership-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            membershipUserData
          );
          await ddbBatchWrite(
            "Membership-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            membershipOrgData
          );
        }

        callback(null, {
          message: "Organization import successfully.",
          statusCode: 200,
          data: {
            success: createOrg?.organizationsByDate?.CapacityUnits || 0,
            fail: failArray.length > 0 ? failArray : [],
          },
        });
        break;
      }
      case "importStudents": {
        console.log("importStudents");
        let stockholderItems = event.arguments?.input?.items;
        console.log("stockholderItems", stockholderItems);
        const cityArr = [];

        let params = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
          },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#N": "name",
          },
          ProjectionExpression: "id, #N",
          TableName:
            "City-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        const cityTableResponse = await ddb.scan(params).promise();
        const cityResponse = cityTableResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        cityResponse.map((rec) => {
          let cityNameKey = rec?.name?.toLowerCase()?.replace(" ", "_");
          cityArr[cityNameKey] = rec?.id;
        });

        const scanParams = {
          ExpressionAttributeValues: { ":d": { S: "false" } },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: { "#D": "isDeleted" },
          ProjectionExpression: "memberCode",
          TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };
        const scanResponse = await ddb.scan(scanParams).promise();
        const newResponse = scanResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        const memberCodes = newResponse.map(
          (item) => parseInt(item.memberCode) || 0
        );
        let highestMemberCode = Math.max(...memberCodes);

        let stockholderItemsSegments = R.splitEvery(25, stockholderItems);
        let createUser;
        let failToUpload = 0;
        let failDataCount = 0;
        let failArrayEmail = [];
        for (const element of stockholderItemsSegments) {
          let studentData = [];
          let membershipData = [];
          let index = 0;
          for (let studentRec of element) {
            console.log(`Index: ${index}`);
            index++;
            let studentId = uuid.v4();
            let membershipId = uuid.v4();
            let userExist = studentRec?.email ? 1 : 0;
            let phoneNo = "+10000000000";
            highestMemberCode = highestMemberCode + 1;

            if (studentRec?.email) {
              const checkUserExistParams = {
                ExpressionAttributeValues: {
                  ":d": { S: "false" },
                  ":e": { S: studentRec?.email?.toLowerCase() },
                },
                FilterExpression: "#D = :d and #E = :e",
                ExpressionAttributeNames: {
                  "#D": "isDeleted",
                  "#E": "email",
                },
                TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              };

              const checkUserExistParamsRes = await ddb
                .scan(checkUserExistParams)
                .promise();
              let records = checkUserExistParamsRes.Items.map((item) =>
                AWS.DynamoDB.Converter.unmarshall(item)
              );
              userExist = records.length ?? 0;
              failToUpload += userExist;
              if (userExist > 0) {
                failArrayEmail.push(studentRec?.email);
              }
            }
            console.log("userExist", userExist);
            if (userExist == 0) {
              let cityName =
                studentRec?.cityId !== undefined && studentRec?.cityId !== null
                  ? studentRec.cityId.toLowerCase().replace(" ", "_")
                  : "";
              let cityId = cityArr[cityName];
              let phoneNoFordb = "";
              phoneNoFordb = studentRec?.phoneNumber?.replace(/-/g, "") ?? "";
              phoneNoFordb = phoneNoFordb?.replace(/\s/g, "") ?? "";
              phoneNoFordb = phoneNoFordb?.replace("+1", "") ?? "";
              studentData.push({
                PutRequest: {
                  Item: {
                    id: { S: studentId },
                    __typename: { S: "User" },
                    email: { S: studentRec?.email?.toLowerCase() ?? "" },
                    familyName: { S: studentRec?.familyName ?? "" },
                    givenName: { S: studentRec?.givenName ?? "" },
                    name: {
                      S:
                        studentRec?.givenName + " " + studentRec?.familyName ??
                        "",
                    },
                    phoneNumber: {
                      S: studentRec.phoneNumber ? `+1${phoneNoFordb}` : phoneNo,
                    },
                    cityId: { S: cityId ?? cityArr["jacksonville"] },
                    imageUrl: { S: studentRec?.imageUrl ?? "" },
                    streetAddressOne: { S: studentRec?.streetAddressOne ?? "" },
                    streetAddressTwo: { S: studentRec?.streetAddressTwo ?? "" },
                    city: { S: studentRec?.city ?? "" },
                    state: { S: studentRec?.state ?? "" },
                    zipCode: { S: studentRec?.zipCode ?? "" },
                    countryCode: { S: studentRec?.countryCode ?? "" },
                    role: { S: studentRec?.role ?? "SUBSCRIBER" },
                    birthday: { S: studentRec?.birthday ?? "" },
                    ethnicity: { S: studentRec?.ethnicity ?? "" },
                    gender: { S: studentRec?.gender ?? "NONE" },
                    profileUrl: { S: studentRec?.profileUrl ?? "" },
                    type: { S: studentRec?.type ?? "" },
                    status: { S: studentRec?.status ?? "" },
                    registeredFrom: { S: studentRec?.registeredFrom ?? "WEB" },
                    isAssociated: { BOOL: studentRec?.isAssociated ?? false },
                    isStakeholder: { BOOL: studentRec?.isStakeholder ?? false },
                    memberCode: {
                      S: highestMemberCode.toString().padStart(4, "0") ?? "",
                    },
                    isSignup: { BOOL: false },
                    userType: {
                      S: studentRec?.email ? "loginUser" : "student",
                    },
                    membershipId: { S: membershipId },
                    isDeleted: { S: "false" },
                    createdAt: { S: date.toISOString() },
                    updatedAt: { S: date.toISOString() },
                    _version: { N: "1" },
                    _lastChangedAt: { N: date.getTime().toString() },
                  },
                },
              });

              membershipData.push({
                PutRequest: {
                  Item: {
                    id: { S: membershipId },
                    __typename: { S: "Membership" },
                    type: { S: "User" },
                    name: {
                      S:
                        studentRec?.givenName + " " + studentRec?.familyName ??
                        "",
                    },
                    imageUrl: { S: studentRec?.imageUrl ?? "" },
                    personsID: {
                      S: studentId,
                    },
                    isActive: { BOOL: true },
                    lastAddedImpactScore: { S: "0.00" },
                    currentImpactScore: { S: "0.00" },
                    MVPTokens: { N: "0.00" },
                    cityId: { S: cityId ?? cityArr["jacksonville"] },
                    memberCode: {
                      S: highestMemberCode.toString().padStart(4, "0") ?? "",
                    },
                    isDeleted: { S: "false" },
                    createdAt: { S: date.toISOString() },
                    updatedAt: { S: date.toISOString() },
                    _version: { N: "1" },
                    _lastChangedAt: { N: date.getTime().toString() },
                  },
                },
              });
            }
          }
          createUser = await ddbBatchWrite(
            "User-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            studentData
          );
          console.log("createUser", createUser);
          failDataCount =
            studentData.length - createUser?.userByDate?.CapacityUnits || 0;
          await ddbBatchWrite(
            "Membership-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            membershipData
          );
        }
        let failArray = [];

        if (failDataCount >= 1) {
          if (failDataCount === 1) {
            failArray.push(`${failDataCount} student is not uploaded`);
          } else {
            failArray.push(`${failDataCount} students are not uploaded`);
          }
        }
        if (failToUpload >= 1) {
          function formatArray(arr) {
            if (arr.length === 0) return ""; // Empty array
            if (arr.length === 1) return arr[0]; // Single item, return as is
            if (arr.length === 2) return `${arr[0]} and ${arr[1]}`; // Two items, join with 'and'

            // For three or more items, join with commas and 'and' before the last item
            return arr.slice(0, -1).join(", ") + ", and " + arr[arr.length - 1];
          }
          if (failToUpload === 1) {
            failArray.push(
              `${formatArray(failArrayEmail)} student is already exist`
            );
            console.log("failArrayEmail", failArrayEmail);
          } else {
            failArray.push(
              `${formatArray(failArrayEmail)} students are already exist`
            );
          }
        }

        callback(null, {
          message: "Student import successfully.",
          statusCode: 200,
          data: {
            success: createUser?.userByDate?.CapacityUnits
              ? createUser?.userByDate?.CapacityUnits
              : 0,
            fail: failArray.length > 0 ? failArray : [],
          },
        });
        break;
      }

      case "importStakeholders": {
        console.log("importStakeholders");
        let stakeholderItems = event.arguments?.input?.items;
        console.log("stakeholderItems", stakeholderItems);
        const cityArr = [];

        let params = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
          },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#N": "name",
          },
          ProjectionExpression: "id, #N",
          TableName:
            "City-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        const cityTableResponse = await ddb.scan(params).promise();
        const cityResponse = cityTableResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        cityResponse.map((rec) => {
          let cityNameKey = rec?.name?.toLowerCase()?.replace(" ", "_");
          cityArr[cityNameKey] = rec?.id;
        });

        const scanParams = {
          ExpressionAttributeValues: { ":d": { S: "false" } },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: { "#D": "isDeleted" },
          ProjectionExpression: "memberCode",
          TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };
        const scanResponse = await ddb.scan(scanParams).promise();
        const newResponse = scanResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        const memberCodes = newResponse.map(
          (item) => parseInt(item.memberCode) || 0
        );
        let highestMemberCode = Math.max(...memberCodes);

        let stakeholderItemsSegments = R.splitEvery(25, stakeholderItems);
        let createUser;
        let failToUpload = 0;
        let failToUploadPhone = 0;
        let failDataCount = 0;
        let failArrayEmail = [];
        let failArrayPhone = [];
        for (const element of stakeholderItemsSegments) {
          let stakeholderData = [];
          let membershipData = [];

          for (let stakeholderRec of element) {
            let membershipId = uuid.v4();
            let userExist = 1;
            let userExistPhone = 1;
            highestMemberCode = highestMemberCode + 1;

            let createId = uuid.v4();

            const checkUserExistParams = {
              ExpressionAttributeValues: {
                ":d": { S: "false" },
                ":e": { S: stakeholderRec.email.toLowerCase() },
              },
              FilterExpression: "#D = :d and #E = :e",
              ExpressionAttributeNames: {
                "#D": "isDeleted",
                "#E": "email",
              },
              TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
            };

            const checkUserExistParamsRes = await ddb
              .scan(checkUserExistParams)
              .promise();
            let records = checkUserExistParamsRes.Items.map((item) =>
              AWS.DynamoDB.Converter.unmarshall(item)
            );

            const checkUserExistParamsPhone = {
              ExpressionAttributeValues: {
                ":d": { S: "false" },
                ":ph": { S: `+1${stakeholderRec.phoneNumber}` },
              },
              FilterExpression: "#D = :d and #PH = :ph",
              ExpressionAttributeNames: {
                "#D": "isDeleted",
                "#PH": "phoneNumber",
              },
              TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
            };

            console.log("checkUserExistParamsPhone", checkUserExistParamsPhone);

            const checkUserExistParamsResPhone = await ddb
              .scan(checkUserExistParamsPhone)
              .promise();

            console.log(
              "checkUserExistParamsResPhone",
              checkUserExistParamsResPhone
            );
            let recordsPhone = checkUserExistParamsResPhone.Items.map((item) =>
              AWS.DynamoDB.Converter.unmarshall(item)
            );

            console.log("recordsPhone", recordsPhone);

            userExist = records.length ?? 0;
            failToUpload += userExist;
            if (userExist > 0) {
              failArrayEmail.push(stakeholderRec?.email);
            }
            userExistPhone = recordsPhone.length ?? 0;
            failToUploadPhone += userExistPhone;
            if (userExistPhone > 0) {
              failArrayPhone.push(stakeholderRec.phoneNumber);
            }
            if (userExist == 0 && stakeholderRec.email && userExistPhone == 0) {
              let cityName =
                stakeholderRec?.cityId === undefined ||
                stakeholderRec?.cityId === null
                  ? ""
                  : stakeholderRec.cityId.toLowerCase().replace(" ", "_");
              console.log("cityName", cityName);
              let cityId = cityArr[cityName];
              let phoneNoFordb = "0000000000";
              phoneNoFordb =
                stakeholderRec?.phoneNumber?.replace(/-/g, "") ?? "";
              phoneNoFordb = phoneNoFordb?.replace(/\s/g, "") ?? "";
              phoneNoFordb = phoneNoFordb?.replace("+1", "") ?? "";

              stakeholderData.push({
                PutRequest: {
                  Item: {
                    id: { S: createId },
                    __typename: { S: "User" },
                    email: { S: stakeholderRec.email.toLowerCase() },
                    familyName: { S: stakeholderRec?.familyName ?? "" },
                    givenName: { S: stakeholderRec?.givenName ?? "" },
                    name: {
                      S:
                        stakeholderRec?.givenName +
                          " " +
                          stakeholderRec?.familyName ?? "",
                    },
                    phoneNumber: {
                      S: stakeholderRec.phoneNumber
                        ? `+1${phoneNoFordb}`
                        : "+10000000000",
                    },
                    cityId: { S: cityId ?? cityArr["jacksonville"] },
                    imageUrl: { S: stakeholderRec?.imageUrl ?? "" },
                    streetAddressOne: {
                      S: stakeholderRec?.streetAddressOne ?? "",
                    },
                    streetAddressTwo: {
                      S: stakeholderRec?.streetAddressTwo ?? "",
                    },
                    city: { S: stakeholderRec?.city ?? "" },
                    state: { S: stakeholderRec?.state ?? "" },
                    zipCode: { S: stakeholderRec?.zipCode ?? "" },
                    countryCode: { S: stakeholderRec?.countryCode ?? "" },
                    role: { S: stakeholderRec?.role ?? "SUBSCRIBER" },
                    assignedRole: { S: stakeholderRec?.role ?? "SUBSCRIBER" },
                    birthday: { S: stakeholderRec?.birthday ?? "" },
                    gender: { S: stakeholderRec?.gender ?? "NONE" },
                    profileUrl: { S: stakeholderRec?.profileUrl ?? "" },
                    type: { S: stakeholderRec?.type ?? "" },
                    status: { S: stakeholderRec?.status ?? "inactive" },
                    registeredFrom: {
                      S: stakeholderRec?.registeredFrom ?? "WEB",
                    },
                    stackholderCities: { L: [{ S: cityArr[cityName] }] },
                    isAssociated: {
                      BOOL: stakeholderRec?.isAssociated ?? false,
                    },
                    isStakeholder: {
                      BOOL: stakeholderRec?.isStakeholder ?? true,
                    },
                    memberCode: {
                      S: highestMemberCode.toString().padStart(4, "0") ?? "",
                    },
                    userType: {
                      S: "loginUser",
                    },
                    isSignup: { BOOL: false },
                    membershipId: { S: membershipId },
                    userAddedFrom: { S: "STAKEHOLDER_IMPORTED" },
                    isDeleted: { S: "false" },
                    createdAt: { S: date.toISOString() },
                    updatedAt: { S: date.toISOString() },
                    _version: { N: "1" },
                    _lastChangedAt: { N: date.getTime().toString() },
                  },
                },
              });

              membershipData.push({
                PutRequest: {
                  Item: {
                    id: { S: membershipId },
                    __typename: { S: "Membership" },
                    type: { S: "User" },
                    name: {
                      S:
                        stakeholderRec?.givenName +
                          " " +
                          stakeholderRec?.familyName ?? "",
                    },
                    imageUrl: { S: stakeholderRec?.imageUrl ?? "" },
                    personsID: {
                      S: createId,
                    },
                    isActive: { BOOL: true },
                    lastAddedImpactScore: { S: "0.00" },
                    currentImpactScore: { S: "0.00" },
                    MVPTokens: { N: "0.00" },
                    cityId: { S: cityId ?? cityArr["jacksonville"] },
                    memberCode: {
                      S: highestMemberCode.toString().padStart(4, "0") ?? "",
                    },
                    isDeleted: { S: "false" },
                    createdAt: { S: date.toISOString() },
                    updatedAt: { S: date.toISOString() },
                    _version: { N: "1" },
                    _lastChangedAt: { N: date.getTime().toString() },
                  },
                },
              });
            }
          }
          createUser = await ddbBatchWrite(
            "User-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            stakeholderData
          );
          await Promise.all(
            stakeholderData.map(async (items) => {
              console.log(
                "items.PutRequest.Item.id",
                items.PutRequest.Item.id.S
              );

              const params = {
                TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
                Key: { id: { S: items.PutRequest.Item.id.S } },
              };
              const getUserResponse = await ddb.getItem(params).promise();
              console.log("getUserResponse", getUserResponse);
              const userItem = getUserResponse["Item"]
                ? AWS.DynamoDB.Converter.unmarshall(getUserResponse["Item"])
                : null;
              console.log("userItem", userItem);
              const scanParams = {
                ExpressionAttributeValues: {
                  ":d": { S: "false" },
                  ":r": { S: "SUPER_ADMIN" },
                },
                FilterExpression: "#D = :d and #R = :r",
                ExpressionAttributeNames: {
                  "#D": "isDeleted",
                  "#R": "role",
                },
                ProjectionExpression: "id",
                TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              };
              const scanResponse = await ddb.scan(scanParams).promise();
              let records = scanResponse.Items.map((item) =>
                AWS.DynamoDB.Converter.unmarshall(item)
              );

              console.log("records", records);

              if (userItem) {
                console.log("here in if");
                let sendMailFunction = `sendEmail-${process.env.ENV}`;
                let mailPayload = {
                  arguments: {
                    input: {
                      firstName: userItem.givenName,
                      memberCode: userItem.memberCode,
                      // cityLocation: userItem.city,
                      to: userItem.email,
                    },
                  },
                  fieldName: "welcomeMail",
                };

                let sendMailParams = {
                  FunctionName: sendMailFunction,
                  InvocationType: "RequestResponse",
                  LogType: "None",
                  Payload: JSON.stringify(mailPayload),
                };
                console.log("sendMailParams", sendMailParams);
                await lambda.invoke(sendMailParams).promise();
                return true;
              }
            })
          );
          failDataCount =
            stakeholderData.length - createUser?.userByDate?.CapacityUnits || 0;
          await ddbBatchWrite(
            "Membership-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            membershipData
          );
        }
        let failArray = [];
        if (failDataCount >= 1) {
          if (failDataCount === 1) {
            failArray.push(`${failDataCount} stakeholder is not uploaded`);
          } else {
            failArray.push(`${failDataCount} stakeholders are not uploaded`);
          }
        }
        function formatArray(arr) {
          if (arr.length === 0) return ""; // Empty array
          if (arr.length === 1) return arr[0]; // Single item, return as is
          if (arr.length === 2) return `${arr[0]} and ${arr[1]}`; // Two items, join with 'and'

          // For three or more items, join with commas and 'and' before the last item
          return arr.slice(0, -1).join(", ") + ", and " + arr[arr.length - 1];
        }
        if (failToUpload === 1) {
          failArray.push(
            `${formatArray(failArrayEmail)} stakeholder is already exist`
          );
        } else if (failToUpload > 1) {
          failArray.push(
            `${formatArray(failArrayEmail)} stakeholders are already exist`
          );
        }
        console.log("failToUploadPhone", failToUploadPhone);
        console.log("failArrayPhone", failArrayPhone);
        if (failToUploadPhone === 1) {
          failArray.push(
            `${formatArray(failArrayPhone)} phone number is already exist`
          );
        } else if (failToUploadPhone > 1) {
          failArray.push(
            `${formatArray(failArrayPhone)} phone numbers are already exist`
          );

          console.log("failArray phone ", failArray);
          // }
        }

        callback(null, {
          message: "Stakeholders import successfully.",
          statusCode: 200,
          data: {
            success: createUser?.userByDate?.CapacityUnits
              ? createUser?.userByDate?.CapacityUnits
              : 0,
            fail: failArray.length > 0 ? failArray : [],
          },
        });
        break;
      }

      case "importTransactions": {
        console.log("importTransactions");
        const cities = {
          Jacksonville: "8f6c4c07-dd62-480d-a376-ef221133f0e2",
          Tampa: "c37768c1-da38-41a3-8191-46d5c259eb0e",
          Orlando: "d37334c6-b2b8-4e98-83a8-83448fd94582",
          Gainesville: "96b436e1-ea2b-4803-8563-3c33b0f1322c",
          Miami: "ed5ddcd2-2682-4fdb-885f-511d2a41285c",
        };
        const transactionItems = event.arguments?.input?.items;
        const createdUserId = event.arguments?.input?.createdUserId;
        const importCityId = event.arguments?.input?.cityId;
        const createdUserName = event.arguments?.input?.createdUserName;
        console.log("transactionItems", transactionItems);
        const cityTableName = `City-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        const cityFundTableName = `CityFundTransactions-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        const activityTableName = `Activity-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        const transactionType = [
          "subscription",
          "Donor",
          "Foundation",
          "Buyer",
          "Government",
        ];

        const cityArr = [];
        let createTran;
        let params = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
          },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#N": "name",
          },
          ProjectionExpression: "id, #N",
          TableName: cityTableName,
        };
        const cityTableResponse = await ddb.scan(params).promise();
        const cityResponse = cityTableResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        cityResponse.map((rec) => {
          let cityNameKey = rec.name.toLowerCase().replace(" ", "_");
          cityArr[cityNameKey] = rec.id;
        });

        // Get all member code
        const memberCodes = await getMemberCode();
        console.log("memberCodes", memberCodes);

        // Get city old balance
        let cityFundParams = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
          },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#N": "name",
          },
          ProjectionExpression: "id, #N, amount, amountStatus, cityId",
          TableName: cityFundTableName,
        };
        const cityFundRes = await ddb.scan(cityFundParams).promise();
        const cityFundResponse = cityFundRes.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );

        let cityCurrentData = {};
        let failDataCount = 0;

        cityFundResponse.map((city) => {
          if (cityCurrentData.hasOwnProperty(city.cityId)) {
            if (city.amountStatus === "DEBITED") {
              cityCurrentData[city.cityId] -= parseFloat(city.amount);
            } else {
              cityCurrentData[city.cityId] += parseFloat(city.amount);
            }
          } else if (city.amountStatus === "DEBITED") {
            cityCurrentData[city.cityId] = -Math.abs(parseFloat(city.amount));
          } else {
            cityCurrentData[city.cityId] = parseFloat(city.amount);
          }
        });

        console.log("cityCurrentData", cityCurrentData);

        let transactionItemsSegments = R.splitEvery(25, transactionItems);
        console.log("transactionItemsSegments", transactionItemsSegments);
        for (const element of transactionItemsSegments) {
          console.log("transactionItemsSegments[i]", element);

          let transactionData = [];

          for (let transactionRec of element) {
            let transactionId = uuid.v4();

            if (
              (transactionRec &&
                transactionType.includes(transactionRec?.type) &&
                transactionRec?.amountStatus != "DEBITED") ||
              (!transactionType.includes(transactionRec?.type) &&
                transactionRec?.amountStatus == "DEBITED")
            ) {
              let cityName = transactionRec?.cityId
                ? transactionRec.cityId.toLowerCase().replace(" ", "_")
                : undefined;
              let cityId = cityArr[cityName];
              console.log("cityId", cityId);
              let previousAmount = cityCurrentData[cityId] ?? 0;
              let currentAmount =
                transactionRec?.amountStatus === "DEBITED"
                  ? (parseFloat(cityCurrentData[cityId]) || 0) -
                    (parseFloat(transactionRec?.amount) || 0)
                  : parseFloat(cityCurrentData[cityId] || 0) +
                    parseFloat(transactionRec?.amount || 0);
              console.log("previousAmount", previousAmount);
              console.log("currentAmount", currentAmount);
              let memberID =
                memberCodes.find(
                  (ex) =>
                    Number(ex.memberCode) ===
                      Number(transactionRec?.memberId) &&
                    ex.cityId === cities[transactionRec?.cityId]
                ) ?? "";

              if (memberID) {
                transactionData.push({
                  PutRequest: {
                    Item: {
                      id: { S: transactionId ?? uuid.v4() },
                      __typename: { S: "CityFundTransactions" },
                      amountStatus: { S: transactionRec?.amountStatus ?? "" },
                      amount: {
                        N:
                          (transactionRec?.amount === undefined
                            ? "0"
                            : transactionRec?.amount.toString()) ?? "0",
                      },
                      previousAmount: { N: previousAmount.toString() ?? "0" },
                      currentAmount: { N: currentAmount.toString() ?? "0" },
                      cityId: { S: cityId ?? cityArr["jacksonville"] },
                      invoiceDatetime: { S: date.toISOString() },
                      memberId: {
                        S:
                          memberID?.organizationID ?? memberID?.personsID ?? "",
                      },
                      name: {
                        S: (transactionRec?.name || memberID?.name) ?? "",
                      },
                      productName: { S: transactionRec?.productName ?? "" },
                      description: { S: transactionRec?.description ?? "" },
                      recurring: { BOOL: transactionRec?.recurring ?? "" },
                      type: { S: transactionRec?.type ?? "" },
                      isDeleted: { S: "false" },
                      createdAt: { S: date.toISOString() },
                      updatedAt: { S: date.toISOString() },
                      _version: { N: "1" },
                      _lastChangedAt: { N: date.getTime().toString() },
                    },
                  },
                });
              }
              if (cityId) {
                cityCurrentData[cityId] = currentAmount ?? 0;
                console.log("cityCurrentData", cityCurrentData);
              }
            }
          }
          createTran = await ddbBatchWrite(cityFundTableName, transactionData);

          failDataCount =
            element.length -
            (createTran?.cityFundTransactionsByDate?.CapacityUnits || 0);
          console.log("failDataCount", failDataCount);
        }

        // Record an activity for this import
        let transactionsActivityParam = {
          Item: {
            id: { S: uuid.v4() },
            __typename: { S: "Activity" },
            activityType: { S: "MEMBERSHIP" },
            createdUserId: { S: createdUserId ?? "" },
            createdUserName: { S: createdUserName ?? "" },
            cityId: { S: importCityId ?? "" },
            moduleId: { S: createdUserId ?? "" },
            moduleName: { S: createdUserName ?? "" },
            moduleType: { S: "fundTransactions" },
            isRelationship: { BOOL: false },
            type: { S: "ADDED" },
            activityTokens: { N: "0" },
            requestStatus: { S: "SYSTEM_APPROVED" },
            isDeleted: { S: "false" },
            createdAt: { S: date.toISOString() },
            updatedAt: { S: date.toISOString() },
            _version: { N: "1" },
            _lastChangedAt: { N: date.getTime().toString() },
          },
          ReturnConsumedCapacity: "TOTAL",
          TableName: activityTableName,
        };
        let transactionsActivityData = await ddb
          .putItem(transactionsActivityParam)
          .promise();
        console.log("transactionsActivityData", transactionsActivityData);

        let failArray = [];
        if (failDataCount >= 1) {
          if (failDataCount === 1) {
            failArray.push(`${failDataCount} fund transaction is not uploaded`);
          } else {
            failArray.push(
              `${failDataCount} fund transactions are not uploaded`
            );
          }
        }

        callback(null, {
          message: "Import transactions successfully.",
          statusCode: 200,
          data: {
            success: createTran?.cityFundTransactionsByDate?.CapacityUnits || 0,
            fail: failArray.length > 0 ? failArray : [],
          },
        });
        break;
      }
      case "importAssignments": {
        console.log("importAssignments");
        const homeworkItems = event.arguments?.input?.items;
        const createdUserId = event.arguments?.input?.createdUserId;
        const importCityId = event.arguments?.input?.cityId;
        const createdUserName = event.arguments?.input?.createdUserName;
        const cityTableName = `City-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        const homeworkTableName = `Homework-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        const activityTableName = `Activity-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        let failDataCount = 0;
        let homeworkAdd;
        let contactFirstName = "";
        let contactLastName = "";
        const cityArray = [];

        const scanParams = {
          ExpressionAttributeValues: { ":d": { S: "false" } },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: { "#D": "isDeleted" },
          TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };
        const scanResponse = await ddb.scan(scanParams).promise();

        const newResponse = scanResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );

        const memberCodes = newResponse.map((member) => {
          member.memberCode = isNaN(parseInt(member.memberCode, 10))
            ? 0
            : parseInt(member.memberCode, 10);
          return member;
        });
        const memberCodesArr = memberCodes.map((member) => member.memberCode);
        let highestMemberCode = Math.max(...memberCodesArr);
        const microCredentialCodes = await getMicroCredentialCode();

        let failArray = [];
        let failToUpload = 0;

        let params = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
          },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#N": "name",
          },
          ProjectionExpression: "id, #N",
          TableName: cityTableName,
        };
        const cityTableResponse = await ddb.scan(params).promise();
        const cityResponse = cityTableResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        cityResponse.map((cityRes) => {
          let cityNameKey = cityRes.name.toLowerCase().replace(" ", "_");
          cityArray[cityNameKey] = cityRes.id;
        });

        let homeworkItemsSegments = R.splitEvery(25, homeworkItems);
        for (const [i, element] of homeworkItemsSegments.entries()) {
          let homeworkData = [];
          let homeworkUserIds = [];
          let homeworkOrganizationIds = [];
          let homeworkDataArray = [];
          let userData = [];
          let membershipUserData = [];

          for (let homeworkRec of element) {
            let homeworkId = uuid.v4();
            let membershipUserId = uuid.v4();
            let userId = uuid.v4();
            let userExist = 1;
            let existEmailList = [];
            highestMemberCode = highestMemberCode + 1;

            homeworkDataArray[homeworkId] = homeworkRec;
            if (homeworkRec?.contactEmail) {
              const checkUserExistParams = {
                ExpressionAttributeValues: {
                  ":d": { S: "false" },
                  ":e": { S: homeworkRec.contactEmail.toLowerCase() },
                },
                FilterExpression: "#D = :d and #E = :e",
                ExpressionAttributeNames: {
                  "#D": "isDeleted",
                  "#E": "email",
                },
                TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              };

              const checkUserExistParamsRes = await ddb
                .scan(checkUserExistParams)
                .promise();
              let records = checkUserExistParamsRes.Items.map((item) =>
                AWS.DynamoDB.Converter.unmarshall(item)
              );
              userExist = records.length ?? 0;
              failToUpload += userExist;
              if (userExist > 0) {
                existEmailList.push(homeworkRec?.contactEmail);
              }

              let cityName =
                homeworkRec?.cityId?.toLowerCase().replace(" ", "_") ?? "";
              let cityId = cityArray[cityName];
              const nameParts =
                homeworkRec?.contactFullName?.trim()?.split(" ") || [];
              contactFirstName = nameParts[0] || "";
              contactLastName = nameParts.slice(1).join(" ") || "";

              let phoneNoFordb = "0000000000";
              phoneNoFordb = homeworkRec?.contactPhoneNumber.replace(/-/g, "");
              phoneNoFordb = phoneNoFordb.replace(/\s/g, "");
              phoneNoFordb = phoneNoFordb.replace("+1", "");

              if (userExist == 0) {
                userData.push({
                  PutRequest: {
                    Item: {
                      id: { S: userId },
                      __typename: { S: "User" },
                      email: {
                        S: homeworkRec?.contactEmail.toLowerCase() ?? "",
                      },
                      familyName: { S: contactLastName ?? "" },
                      givenName: { S: contactFirstName ?? "" },
                      phoneNumber: {
                        S: phoneNoFordb ? `+1${phoneNoFordb}` : "",
                      },
                      name: { S: homeworkRec?.contactFullName ?? "" },
                      role: { S: "MEMBER" },
                      assignedRole: { S: "MEMBER" },
                      cityId: {
                        S: cityArray[cityId] ?? cityArray["jacksonville"],
                      },
                      isAssociated: { BOOL: true },
                      registeredFrom: { S: "WEB" },
                      isDeleted: { S: "false" },
                      membershipId: { S: membershipUserId ?? "" },
                      isStakeholder: { BOOL: true },
                      stackholderCities: { L: [{ S: cityArray[cityName] }] },
                      userAddedFrom: { S: "USER_IMPORTED_ASSIGNMENT" },
                      gender: { S: "NONE" },
                      userType: {
                        S: "loginUser",
                      },
                      memberCode: {
                        S: highestMemberCode.toString().padStart(4, "0") ?? "",
                      },
                      status: { S: "inactive" },
                      isSignup: { BOOL: false },

                      createdAt: { S: date.toISOString() },
                      updatedAt: { S: date.toISOString() },
                      _version: { N: "1" },
                      _lastChangedAt: { N: date.getTime().toString() },
                    },
                  },
                });

                membershipUserData.push({
                  PutRequest: {
                    Item: {
                      id: { S: membershipUserId },
                      __typename: { S: "Membership" },
                      type: { S: "User" },
                      name: { S: homeworkRec?.contactFullName ?? "" },
                      personsID: { S: userId ?? "" },
                      cityId: { S: cityId ?? cityArray["jacksonville"] },
                      memberCode: {
                        S: highestMemberCode.toString().padStart(4, "0") ?? "",
                      },
                      isDeleted: { S: "false" },
                      createdAt: { S: date.toISOString() },
                      updatedAt: { S: date.toISOString() },
                      _version: { N: "1" },
                      _lastChangedAt: { N: date.getTime().toString() },
                    },
                  },
                });
              }

              if (
                homeworkRec?.name &&
                homeworkRec?.dueDate &&
                homeworkRec?.assignmentType &&
                homeworkRec?.assignmentPoints !== undefined
              ) {
                let cityName = (homeworkRec?.cityId ?? "")
                  .toLowerCase()
                  .replace(" ", "_");
                let cityId = cityArray[cityName];
                let primaryContact = userExist == 0 ? userId : records[0]?.id;
                let microcredentialData = findMicroCredentialData(
                  microCredentialCodes,
                  homeworkRec?.microcredentialId
                );
                let imageKey = homeworkRec?.imageUrl
                  ? await uploadImage(homeworkRec?.imageUrl, homeworkId, date)
                  : "";

                if (
                  microcredentialData?.id !== undefined &&
                  (homeworkRec.memberIds ||
                    homeworkRec.studentIds ||
                    homeworkRec.stakeholderIds)
                ) {
                  homeworkData.push({
                    PutRequest: {
                      Item: {
                        id: { S: homeworkId ?? "" },
                        __typename: { S: "Homework" },
                        name: { S: homeworkRec?.name ?? "" },
                        shortDescription: {
                          S: homeworkRec?.shortDescription ?? "",
                        },
                        longDescription: {
                          S: homeworkRec?.longDescription ?? "",
                        },
                        dueDate: { S: homeworkRec?.dueDate },
                        imageUrl: { S: imageKey?.imageKey ?? "" },
                        coCreationType: {
                          S: homeworkRec?.coCreationType ?? "",
                        },
                        assignmentType: {
                          S: homeworkRec?.assignmentType ?? "",
                        },
                        microcredentialId: { S: microcredentialData?.id ?? "" },
                        programsHomeworkId: {
                          S: microcredentialData?.id ?? "",
                        },
                        entityType: { S: homeworkRec?.entityType ?? "" },
                        assignmentPoints: {
                          N:
                            homeworkRec?.assignmentPoints !== undefined
                              ? homeworkRec.assignmentPoints.toString()
                              : "0",
                        },
                        primaryContact: { S: primaryContact ?? "" },
                        cityId: { S: cityId ?? cityArray["jacksonville"] },
                        createdBy: { S: createdUserId ?? "" },
                        isDeleted: { S: "false" },
                        createdAt: { S: date.toISOString() },
                        updatedAt: { S: date.toISOString() },
                        _version: { N: "1" },
                        _lastChangedAt: { N: date.getTime().toString() },
                      },
                    },
                  });
                  let memberIdsArr = extractIds(homeworkRec, "memberIds");
                  let stakeholderIdsArr = extractIds(
                    homeworkRec,
                    "stakeholderIds"
                  );
                  let studentIdsArr = extractIds(homeworkRec, "studentIds");
                  let studentstakeholderArr = [
                    ...stakeholderIdsArr,
                    ...studentIdsArr,
                  ];

                  processIds(
                    studentstakeholderArr,
                    homeworkId,
                    microcredentialData?.id,
                    memberCodes,
                    homeworkUserIds,
                    "studentStakeholderId"
                  );
                  processIds(
                    memberIdsArr,
                    homeworkId,
                    microcredentialData?.id,
                    memberCodes,
                    homeworkOrganizationIds,
                    "memberId"
                  );
                }
              }
            }
          }

          const userPersonIds = {};

          homeworkUserIds.forEach((ex) => {
            const { homeworkId, studentStakeholderId } = ex || {};

            if (!(homeworkId in userPersonIds)) {
              userPersonIds[homeworkId] = [];
            }

            userPersonIds[homeworkId].push({ personsID: studentStakeholderId });
          });
          let orgPersonsIds = await getAssociatedUserIds(
            homeworkOrganizationIds
          );

          // Merge arrays with unique personsID
          const mergedIds = {};

          // Combine userPersonIds and orgPersonsIds
          const allKeys = new Set([
            ...Object.keys(userPersonIds),
            ...Object.keys(orgPersonsIds),
          ]);

          allKeys.forEach((key) => {
            const userArray = userPersonIds[key] || [];
            const orgArray = orgPersonsIds[key] || [];

            const mergedArray = [...userArray, ...orgArray];
            const uniquePersonsIds = Array.from(
              new Set(mergedArray.map((item) => item.personsID))
            );
            mergedIds[key] = uniquePersonsIds.map((personsID) => ({
              personsID,
            }));
          });

          let notificationData = await getUserDataForPersonsIds(mergedIds);

          homeworkAdd = await ddbBatchWrite(homeworkTableName, homeworkData);
          await ddbBatchWrite(
            "User-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            userData
          );
          await ddbBatchWrite(
            "Membership-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            membershipUserData
          );

          failDataCount =
            homeworkItemsSegments[i].length -
            (homeworkAdd?.homeworkByDate?.CapacityUnits || 0);

          await createHomeworkUser(homeworkUserIds);
          await createHomeworkOrganization(homeworkOrganizationIds);
          await sendNotifications(notificationData, homeworkDataArray);
        }

        // Record an activity for this import
        let assignmentsActivityParam = {
          Item: {
            id: { S: uuid.v4() },
            __typename: { S: "Activity" },
            activityType: { S: "MEMBERSHIP" },
            createdUserId: { S: createdUserId ?? "" },
            createdUserName: { S: createdUserName ?? "" },
            cityId: { S: importCityId ?? "" },
            moduleId: { S: createdUserId ?? "" },
            moduleName: { S: createdUserName ?? "" },
            moduleType: { S: "homeworkImport" },
            isRelationship: { BOOL: false },
            type: { S: "ADDED" },
            activityTokens: { N: "0" },
            requestStatus: { S: "SYSTEM_APPROVED" },
            isDeleted: { S: "false" },
            createdAt: { S: date.toISOString() },
            updatedAt: { S: date.toISOString() },
            _version: { N: "1" },
            _lastChangedAt: { N: date.getTime().toString() },
          },
          ReturnConsumedCapacity: "TOTAL",
          TableName: activityTableName,
        };
        await ddb.putItem(assignmentsActivityParam).promise();
        if (failDataCount >= 1) {
          if (failDataCount === 1) {
            failArray.push(`${failDataCount} fund transaction is not uploaded`);
          } else {
            failArray.push(
              `${failDataCount} fund transactions are not uploaded`
            );
          }
        }

        callback(null, {
          message: "Import assignments successfully.",
          statusCode: 200,
          data: {
            success: homeworkAdd?.homeworkByDate?.CapacityUnits || 0,
            fail: failArray.length > 0 ? failArray : [],
          },
        });
        break;
      }

      case "importSchool": {
        console.log("importSchools");
        let schoolItems = event.arguments?.input?.items;
        const cityArray = [];
        let contactFirstName = "";
        let contactLastName = "";

        const scanParams = {
          ExpressionAttributeValues: { ":d": { S: "false" } },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: { "#D": "isDeleted" },
          TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };
        const scanResponse = await ddb.scan(scanParams).promise();

        const newResponse = scanResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );

        const memberCodes = newResponse.map((member) => {
          member.memberCode = isNaN(parseInt(member.memberCode, 10))
            ? 0
            : parseInt(member.memberCode, 10);
          return member;
        });
        const memberCodesArr = memberCodes.map((member) => member.memberCode);
        let highestMemberCode = Math.max(...memberCodesArr);

        let params = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
          },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#N": "name",
          },
          ProjectionExpression: "id, #N",
          TableName:
            "City-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        const cityTableResponse = await ddb.scan(params).promise();
        const cityResponse = cityTableResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        cityResponse.map((rec) => {
          let cityNameKey = rec?.name?.trim()?.toLowerCase()?.replace(" ", "_");
          cityArray[cityNameKey] = rec?.id;
        });
        let schoolItemsSegments = R.splitEvery(25, schoolItems);
        let createSchool;
        let failToUpload = 0;
        let failDataCount = 0;
        for (const element of schoolItemsSegments) {
          let schoolData = [];
          let membershipData = [];
          let userData = [];
          let membershipUserData = [];

          for (let schoolRec of element) {
            let schoolId = uuid.v4();
            let membershipId = uuid.v4();
            let cityName = schoolRec?.cityId
              ? schoolRec.cityId.toLowerCase().replace(" ", "_")
              : "";
            let cityId = cityArray[cityName];
            let userExist = 1;
            let existEmailList = [];
            let userId = uuid.v4();
            let membershipUserId = uuid.v4();
            highestMemberCode = highestMemberCode + 1;

            let phoneNoFordb = "0000000000";
            phoneNoFordb = schoolRec?.contactPhoneNumber.replace(/-/g, "");
            phoneNoFordb = phoneNoFordb.replace(/\s/g, "");
            phoneNoFordb = phoneNoFordb.replace("+1", "");

            if (schoolRec?.contactEmail) {
              const checkUserExistParams = {
                ExpressionAttributeValues: {
                  ":d": { S: "false" },
                  ":e": { S: schoolRec.contactEmail.toLowerCase() },
                },
                FilterExpression: "#D = :d and #E = :e",
                ExpressionAttributeNames: {
                  "#D": "isDeleted",
                  "#E": "email",
                },
                TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              };

              const checkUserExistParamsRes = await ddb
                .scan(checkUserExistParams)
                .promise();
              let records = checkUserExistParamsRes.Items.map((item) =>
                AWS.DynamoDB.Converter.unmarshall(item)
              );
              userExist = records.length ?? 0;
              console.log("userExist shool", userExist);
              failToUpload += userExist;
              if (userExist > 0) {
                existEmailList.push(schoolRec?.contactEmail);
              }

              let cityName = schoolRec?.cityId
              ? schoolRec.cityId.toLowerCase().replace(" ", "_")
              : "";
              let cityId = cityArray[cityName];

              const nameParts = schoolRec?.contactFullName?.trim().split(" ");
              contactFirstName = nameParts[0];
              contactLastName = nameParts[1];

              if (userId) {
                console.log("userExist shool: ", userExist);
                if (userExist == 0) {
                  userData.push({
                    PutRequest: {
                      Item: {
                        id: { S: userId },
                        __typename: { S: "User" },
                        email: {
                          S: schoolRec?.contactEmail.toLowerCase() ?? "",
                        },
                        familyName: { S: contactLastName ?? "" },
                        givenName: { S: contactFirstName ?? "" },
                        phoneNumber: { S: `+1${phoneNoFordb || ""}` },
                        name: { S: schoolRec?.contactFullName ?? "" },
                        role: { S: "MEMBER" },
                        assignedRole: { S: "MEMBER" },
                        cityId: { S: cityId ?? cityArray["jacksonville"] },
                        isAssociated: { BOOL: true },
                        registeredFrom: { S: "WEB" },
                        isDeleted: { S: "false" },
                        membershipId: { S: membershipUserId ?? "" },
                        isStakeholder: { BOOL: true },
                        stackholderCities: { L: [{ S: cityArray[cityName] }] },
                        userAddedFrom: { S: "USER_IMPORTED_SCHOOL" },
                        gender: { S: "NONE" },
                        userType: {
                          S: "loginUser",
                        },
                        memberCode: {
                          S:
                            highestMemberCode.toString().padStart(4, "0") ?? "",
                        },
                        status: { S: "inactive" },
                        isSignup: { BOOL: false },

                        createdAt: { S: date.toISOString() },
                        updatedAt: { S: date.toISOString() },
                        _version: { N: "1" },
                        _lastChangedAt: { N: date.getTime().toString() },
                      },
                    },
                  });

                  membershipUserData.push({
                    PutRequest: {
                      Item: {
                        id: { S: membershipUserId },
                        __typename: { S: "Membership" },
                        type: { S: "User" },
                        name: { S: schoolRec?.contactFullName ?? "" },
                        personsID: { S: userId ?? "" },
                        cityId: { S: cityId ?? cityArray["jacksonville"] },
                        memberCode: {
                          S:
                            highestMemberCode.toString().padStart(4, "0") ?? "",
                        },
                        isDeleted: { S: "false" },
                        createdAt: { S: date.toISOString() },
                        updatedAt: { S: date.toISOString() },
                        _version: { N: "1" },
                        _lastChangedAt: { N: date.getTime().toString() },
                      },
                    },
                  });
                }
              }
            }

            const item = {
              id: { S: schoolId },
              cityId: { S: cityId ?? cityArray["jacksonville"] },
              type: { S: schoolRec?.type ?? "" },
              name: { S: schoolRec?.name ?? "" },
              shortDescription: { S: schoolRec?.shortDescription ?? "" },
              minAge: { S: schoolRec?.minAge ?? "" },
              maxAge: { S: schoolRec?.maxAge ?? "" },
              estTotalStudents: { S: schoolRec?.estTotalStudents ?? "" },
              mvpMember: { BOOL: schoolRec?.mvpMember ?? false },
              schoolAddressOne: { S: schoolRec?.schoolAddressOne ?? "" },
              schoolAddressTwo: { S: schoolRec?.schoolAddressTwo ?? "" },
              city: { S: schoolRec?.city ?? "" },
              state: { S: schoolRec?.state ?? "" },
              zipCode: { S: schoolRec?.zipCode ?? "" },
              phoneNumber: { S: phoneNoFordb ?? "" },
              businessUserId: { S: schoolRec.businessUserId ?? "" },
              longDescription: { S: schoolRec.longDescription ?? "" },
              isDeleted: { S: "false" },
              createdAt: { S: date.toISOString() },
              updatedAt: { S: date.toISOString() },
              _version: { N: "1" },
              _lastChangedAt: { N: date.getTime().toString() },
            };

            schoolData.push({
              PutRequest: {
                Item: item,
              },
            });

            console.log("schoolData: ", JSON.stringify(schoolData));

            membershipData.push({
              PutRequest: {
                Item: {
                  id: { S: membershipId },
                  __typename: { S: "Membership" },
                  type: { S: "school" },
                  name: { S: schoolRec?.name ?? "" },
                  imageUrl: { S: schoolRec?.imageUrl ?? "" },
                  businessID: { S: schoolId },
                  shortDescription: { S: schoolRec?.shortDescription ?? "" },
                  lastAddedImpactScore: { S: "0.00" },
                  currentImpactScore: { S: "0.00" },
                  cityId: { S: cityId ?? cityArray["jacksonville"] },
                  isDeleted: { S: "false" },
                  createdAt: { S: date.toISOString() },
                  updatedAt: { S: date.toISOString() },
                  _version: { N: "1" },
                  _lastChangedAt: { N: date.getTime().toString() },
                },
              },
            });
          }
          createSchool = await ddbBatchWrite(
            "Business-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            schoolData
          );
          failDataCount =
            schoolData.length - createSchool?.businessByDate?.CapacityUnits ||
            0;

          await ddbBatchWrite(
            "User-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            userData
          );
          await ddbBatchWrite(
            "Membership-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            membershipData
          );
        }
        let failArray = [];
        if (failDataCount >= 1) {
          if (failDataCount === 1) {
            failArray.push(`${failDataCount} school is not uploaded`);
          } else {
            failArray.push(`${failDataCount} schools are not uploaded`);
          }
        }

        callback(null, {
          message: "School import successfully.",
          statusCode: 200,
          data: {
            success: createSchool?.businessByDate?.CapacityUnits
              ? createSchool?.businessByDate?.CapacityUnits
              : 0,
            fail: failArray.length > 0 ? failArray : [],
          },
        });
        break;
      }

      case "importEvents": {
        console.log("importEvents");
        let eventItems = event.arguments?.input?.items;
        const cityArr = [];

        let params = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
          },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#N": "name",
          },
          ProjectionExpression: "id, #N",
          TableName:
            "City-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        const cityTableResponse = await ddb.scan(params).promise();
        const cityResponse = cityTableResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        cityResponse.map((rec) => {
          let cityNameKey = rec?.name?.toLowerCase()?.replace(" ", "_");
          cityArr[cityNameKey] = rec?.id;
        });
        let eventItemsSegments = R.splitEvery(25, eventItems);
        let createEvent;
        let failToUpload = 0;
        let failDataCount = 0;
        const scanParams = {
          ExpressionAttributeValues: { ":d": { S: "false" } },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: { "#D": "isDeleted" },
          ProjectionExpression: "memberCode",
          TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };
        const scanResponse = await ddb.scan(scanParams).promise();

        const newResponse = scanResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        const memberCodes = newResponse.map(
          (item) => parseInt(item.memberCode) || 0
        );
        let highestMemberCode = Math.max(...memberCodes);
        for (const element of eventItemsSegments) {
          let eventData = [];
          let userData = [];
          let membershipUserData = [];

          for (let eventRec of element) {
            let eventId = uuid.v4();
            let cityName = eventRec?.cityId
              ? eventRec.cityId.toLowerCase().replace(" ", "_")
              : "";
            let cityId = cityArr[cityName];
            let userExist = 1;
            let userId = uuid.v4();
            let membershipUserId = uuid.v4();
            highestMemberCode = highestMemberCode + 1;

            eventData.push({
              PutRequest: {
                Item: {
                  id: { S: eventId },
                  __typename: { S: "Events" },
                  cityId: { S: cityId ?? cityArr["jacksonville"] },
                  type: { S: eventRec?.type ?? "" },
                  name: { S: eventRec?.name ?? "" },
                  shortDescription: { S: eventRec?.shortDescription ?? "" },
                  longDescription: { S: eventRec?.longDescription ?? "" },
                  startDateTime: { S: eventRec?.startDateTime },
                  endDateTime: { S: eventRec?.endDateTime },
                  contactFullName: { S: eventRec?.contactFullName ?? "" },
                  contactEmail: {
                    S: eventRec?.contactEmail.toLowerCase() ?? "",
                  },
                  contactPhoneNumber: { S: eventRec?.contactPhoneNumber ?? "" },
                  contactRole: { S: eventRec?.contactRole ?? "" },
                  streetAddress1: {
                    S:
                      eventRec?.structure === "virtual"
                        ? ""
                        : eventRec?.streetAddress1,
                  },
                  streetAddress2: {
                    S:
                      eventRec?.structure === "virtual"
                        ? ""
                        : eventRec?.streetAddress2,
                  },

                  city: {
                    S: eventRec?.structure === "virtual" ? "" : eventRec?.city,
                  },
                  state: {
                    S: eventRec?.structure === "virtual" ? "" : eventRec?.state,
                  },
                  zipcode: {
                    S:
                      eventRec?.structure === "virtual"
                        ? ""
                        : eventRec?.zipcode,
                  },
                  status: { BOOL: eventRec?.status ?? "" },
                  structure: { S: eventRec?.structure ?? "" },
                  infoUrl: { S: eventRec?.infoUrl ?? "" },
                  isDeleted: { S: "false" },
                  createdAt: { S: date.toISOString() },
                  updatedAt: { S: date.toISOString() },
                  _version: { N: "1" },
                  _lastChangedAt: { N: date.getTime().toString() },
                },
              },
            });

            console.log("eventRec?.contactEmail", eventRec?.contactEmail);
            if (eventRec?.contactEmail) {
              const checkUserExistParams = {
                ExpressionAttributeValues: {
                  ":d": { S: "false" },
                  ":e": { S: eventRec?.contactEmail?.toLowerCase() },
                },
                FilterExpression: "#D = :d and #E = :e",
                ExpressionAttributeNames: {
                  "#D": "isDeleted",
                  "#E": "email",
                },
                TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              };

              const checkUserExistParamsRes = await ddb
                .scan(checkUserExistParams)
                .promise();
              let records = checkUserExistParamsRes.Items.map((item) =>
                AWS.DynamoDB.Converter.unmarshall(item)
              );
              userExist = records.length ?? 0;
              failToUpload += userExist;

              let phoneNoFordb = "0000000000";
              phoneNoFordb = eventRec?.contactPhoneNumber.replace(/-/g, "");
              phoneNoFordb = phoneNoFordb.replace(/\s/g, "");
              phoneNoFordb = phoneNoFordb.replace("+1", "");
              let contactFullName =
                eventRec?.contactFirstName && eventRec?.contactLastName
                  ? eventRec?.contactFirstName + " " + eventRec?.contactLastName
                  : "";

              if (userId) {
                if (userExist == 0) {
                  const nameParts = eventRec?.contactFullName
                    ?.trim()
                    .split(" ");
                  userData.push({
                    PutRequest: {
                      Item: {
                        id: { S: userId },
                        __typename: { S: "User" },
                        email: {
                          S: eventRec?.contactEmail?.toLowerCase() ?? "",
                        },
                        familyName: {
                          S: nameParts.length > 1 ? nameParts[1] : "",
                        },
                        givenName: {
                          S: eventRec?.contactFullName ? nameParts[0] : "",
                        },
                        phoneNumber: {
                          S: phoneNoFordb ? `+1${phoneNoFordb}` : "",
                        },
                        name: { S: eventRec?.contactFullName ?? "" },
                        role: { S: "MEMBER" },
                        assignedRole: { S: "MEMBER" },
                        cityId: { S: cityId ?? cityArr["jacksonville"] },
                        isAssociated: { BOOL: true },
                        registeredFrom: { S: "WEB" },
                        isDeleted: { S: "false" },
                        membershipId: { S: membershipUserId ?? "" },
                        isStakeholder: { BOOL: true },
                        stackholderCities: { L: [{ S: cityArr[cityName] }] },
                        userAddedFrom: { S: "USER_IMPORTED_EVENT" },
                        gender: { S: "NONE" },
                        userType: {
                          S: "loginUser",
                        },
                        memberCode: {
                          S:
                            highestMemberCode.toString().padStart(4, "0") ?? "",
                        },
                        status: { S: "inactive" },
                        isSignup: { BOOL: false },

                        createdAt: { S: date.toISOString() },
                        updatedAt: { S: date.toISOString() },
                        _version: { N: "1" },
                        _lastChangedAt: { N: date.getTime().toString() },
                      },
                    },
                  });

                  membershipUserData.push({
                    PutRequest: {
                      Item: {
                        id: { S: membershipUserId },
                        __typename: { S: "Membership" },
                        type: { S: "User" },
                        name: { S: contactFullName ?? "" },
                        personsID: { S: userId ?? "" },
                        cityId: { S: cityId ?? cityArr["jacksonville"] },
                        memberCode: {
                          S:
                            highestMemberCode.toString().padStart(4, "0") ?? "",
                        },
                        isDeleted: { S: "false" },
                        createdAt: { S: date.toISOString() },
                        updatedAt: { S: date.toISOString() },
                        _version: { N: "1" },
                        _lastChangedAt: { N: date.getTime().toString() },
                      },
                    },
                  });
                }
              }
            }
          }
          createEvent = await ddbBatchWrite(
            "Events-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            eventData
          );
          failDataCount =
            eventData.length - createEvent?.eventsByDate?.CapacityUnits || 0;

          await ddbBatchWrite(
            "User-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            userData
          );

          await ddbBatchWrite(
            "Membership-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            membershipUserData
          );
        }
        let failArray = [];
        if (failDataCount >= 1) {
          if (failDataCount === 1) {
            failArray.push(`${failDataCount} event is not uploaded`);
          } else {
            failArray.push(`${failDataCount} events are not uploaded`);
          }
        }

        callback(null, {
          message: "Event import successfully.",
          statusCode: 200,
          data: {
            success: createEvent?.eventsByDate?.CapacityUnits
              ? createEvent?.eventsByDate?.CapacityUnits
              : 0,
            fail: failArray.length > 0 ? failArray : [],
          },
        });
        break;
      }
      case "importIdeas": {
        let ideaItems = event.arguments?.input?.items;
        const cityArr = [];
        console.log("importIdeas");

        let params = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
          },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#N": "name",
          },
          ProjectionExpression: "id, #N",
          TableName:
            "City-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        const cityTableResponse = await ddb.scan(params).promise();
        const cityResponse = cityTableResponse.Items.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        cityResponse.map((rec) => {
          let cityNameKey = rec?.name?.toLowerCase()?.replace(" ", "_");
          cityArr[cityNameKey] = rec?.id;
        });

        console.log("cities", cityArr);

        // Define city abbreviations mapping city IDs to their respective abbreviations
        const cityAbbreviations = {
          "8f6c4c07-dd62-480d-a376-ef221133f0e2": "JAX",
          "c37768c1-da38-41a3-8191-46d5c259eb0e": "TPA",
          "d37334c6-b2b8-4e98-83a8-83448fd94582": "ORD",
          "96b436e1-ea2b-4803-8563-3c33b0f1322c": "GNV",
          "ed5ddcd2-2682-4fdb-885f-511d2a41285c": "MIA",
        };

        // Function to get the highest existing ideaID for a given cityId
        const getHighestIdeaIdForCity = async (cityId) => {
          const scanParams = {
            ExpressionAttributeValues: {
              ":d": { S: "false" },
              ":c": { S: cityId },
            },
            FilterExpression: "#D = :d and #C=:c",
            ExpressionAttributeNames: { "#D": "isDeleted", "#C": "cityId" },
            TableName: `Ideas-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          };

          const scanResponse = await ddb.scan(scanParams).promise();
          const newResponse = scanResponse.Items.map((records) =>
            AWS.DynamoDB.Converter.unmarshall(records)
          );
          const ideaIDs = newResponse.map((item) =>
            item.ideaID ? parseInt(item.ideaID.split("-")[1], 10) : 0
          );
          return Math.max(...ideaIDs, 0);
        };

        const getMember = async (memberCode, cityId) => {
          const scanParams = {
            ExpressionAttributeValues: {
              ":d": { S: "false" },
              ":m": { S: memberCode },
              ":c": { S: cityId },
            },
            FilterExpression: "#D = :d and #M=:m and contains(#S, :c)",
            ExpressionAttributeNames: {
              "#D": "isDeleted",
              "#M": "memberCode",
              "#S": "stackholderCities",
            },
            TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          };

          const scanResponse = await ddb.scan(scanParams).promise();
          const newResponse = scanResponse.Items.map((records) =>
            AWS.DynamoDB.Converter.unmarshall(records)
          );
          return newResponse.map((item) => item.id);
        };

        for (let i = 0; i < ideaItems.length; i++) {
          let item = ideaItems[i];
          let cityName = item.cityId.toLowerCase().replace(" ", "_");
          let cityId = cityArr[cityName];

          if (!cityId) {
            console.error(`City ID not found for city name: ${item.cityId}`);
            continue;
          }

          const highestIdeaId = await getHighestIdeaIdForCity(cityId);
          const personId = await getMember(item.personsID, cityId);
          if (personId) {
            item.personsID = personId[0];
          } else {
            delete item.personsID;
          }
          const newIdeaID = `${cityAbbreviations[cityId]}-${(
            highestIdeaId +
            1 +
            i
          )
            .toString()
            .padStart(5, "0")}`;
          item.ideaID = newIdeaID;
          item.cityId = cityId;
        }

        let ideaItemsSegments = R.splitEvery(25, ideaItems);
        let createIdea;
        let failDataCount = 0;
        for (const element of ideaItemsSegments) {
          let ideaData = [];

          for (let ideaRec of element) {
            if (ideaRec.personsID) {
              let ideaId = uuid.v4();
              let cityName = ideaRec.cityId.toLowerCase().replace(" ", "_");

              ideaData.push({
                PutRequest: {
                  Item: {
                    id: { S: ideaId },
                    __typename: { S: "Ideas" },
                    cityId: { S: cityName ?? cityArr["jacksonville"] },
                    name: { S: ideaRec?.name ?? "" },
                    shortDescription: { S: ideaRec?.shortDescription ?? "" },
                    longDescription: { S: ideaRec?.longDescription ?? "" },
                    personsID: { S: ideaRec?.personsID },
                    status: { S: ideaRec?.status },
                    createdBy: { S: ideaRec?.createdBy ?? "" },
                    category: { S: ideaRec?.category ?? "" },
                    ideaID: { S: ideaRec?.ideaID ?? "" },
                    isDeleted: { S: "false" },
                    createdAt: { S: date.toISOString() },
                    updatedAt: { S: date.toISOString() },
                    _version: { N: "1" },
                    _lastChangedAt: { N: date.getTime().toString() },
                  },
                },
              });
            }
          }

          createIdea = await ddbBatchWrite(
            "Ideas-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            ideaData
          );

          console.log("createIdea", createIdea);
          failDataCount =
            ideaData.length - createIdea?.ideasByDate?.CapacityUnits || 0;
        }
        failDataCount +=
          ideaItems.length - createIdea?.ideasByDate?.CapacityUnits || 0;
        console.log("failDataCount", failDataCount);
        let failArray = [];
        if (failDataCount >= 1) {
          if (failDataCount === 1) {
            failArray.push(`${failDataCount} idea is not uploaded`);
          } else {
            failArray.push(`${failDataCount} ideas are not uploaded`);
          }
        }
        callback(null, {
          message: "Idea import successfully.",
          statusCode: 200,
          data: {
            success: createIdea?.ideasByDate?.CapacityUnits
              ? createIdea?.ideasByDate?.CapacityUnits
              : 0,
            fail: failArray.length > 0 ? failArray : [],
          },
        });
        break;
      }

      default:
        callback("Unknown field, unable to resolve" + event.fieldName, null);
        break;
    }
  } catch (error) {
    const err = {
      isSuccess: false,
      statusCode: error.statusCode ? error.statusCode : 400,
      errorMessage: error.message,
    };
    console.log("error", err);
    callback(err);
  }
};

async function createHomeworkUser(homeworkUserArray) {
  const homeworkUsersTableName = `HomeworkUsers-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
  let homeworkUserSegments = R.splitEvery(25, homeworkUserArray);
  for (const element of homeworkUserSegments) {
    console.log("homeworkUserSegments[i]", element);
    let homeworkUserData = [];

    for (let homeworkUserRec of element) {
      let date = new Date();

      if (homeworkUserRec) {
        homeworkUserData.push({
          PutRequest: {
            Item: {
              id: { S: uuid.v4() },
              __typename: { S: "HomeworkUsers" },
              homeworkId: { S: homeworkUserRec?.homeworkId ?? "" },
              homeworkStatus: { S: "pending" },
              homeworkStudentsStakeholdersId: {
                S: homeworkUserRec?.homeworkId ?? "",
              },
              programsHomeworkUsersId: {
                S: homeworkUserRec?.microcredentialId ?? "",
              },
              studentStakeholderId: {
                S: homeworkUserRec?.studentStakeholderId ?? "",
              },
              userHomeworksId: {
                S: homeworkUserRec?.studentStakeholderId ?? "",
              },
              homeworkAssignDate: { S: date.toISOString() },
              createdAt: { S: date.toISOString() },
              updatedAt: { S: date.toISOString() },
              _version: { N: "1" },
              _lastChangedAt: { N: date.getTime().toString() },
            },
          },
        });
      }
    }
    await ddbBatchWrite(homeworkUsersTableName, homeworkUserData);
  }
  return homeworkUserArray;
}

async function createHomeworkOrganization(homeworkOrganizationArray) {
  const homeworkOrganizationsTableName = `HomeworkOrganizations-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
  let homeworkOrganizationSegments = R.splitEvery(
    25,
    homeworkOrganizationArray
  );
  for (const element of homeworkOrganizationSegments) {
    console.log("homeworkOrganizationSegments[i]", element);
    let homeworkOrganizationData = [];

    for (let homeworkOrganizationRec of element) {
      let date = new Date();

      if (homeworkOrganizationRec) {
        homeworkOrganizationData.push({
          PutRequest: {
            Item: {
              id: { S: uuid.v4() },
              __typename: { S: "HomeworkOrganizations" },
              homeworkId: { S: homeworkOrganizationRec?.homeworkId ?? "" },
              homeworkStatus: { S: "pending" },
              homeworkMembersId: {
                S: homeworkOrganizationRec?.homeworkId ?? "",
              },
              programsHomeworkOrganizationsId: {
                S: homeworkOrganizationRec?.microcredentialId ?? "",
              },
              memberId: { S: homeworkOrganizationRec?.memberId ?? "" },
              organizationsHomeworksId: {
                S: homeworkOrganizationRec?.memberId ?? "",
              },
              homeworkAssignDate: { S: date.toISOString() },
              createdAt: { S: date.toISOString() },
              updatedAt: { S: date.toISOString() },
              _version: { N: "1" },
              _lastChangedAt: { N: date.getTime().toString() },
            },
          },
        });
      }
    }
    await ddbBatchWrite(
      homeworkOrganizationsTableName,
      homeworkOrganizationData
    );
  }
  return homeworkOrganizationArray;
}

const extractIds = (data, key) => {
  const ids = data?.[key] ? R.map(R.trim, R.split(",", data?.[key])) : [];
  return ids.map((id) => Number(id));
};

const findMemberData = (memberCodes, memberCode) => {
  return (
    memberCodes.find((ex) => Number(ex.memberCode) === Number(memberCode)) || {}
  );
};

const findMicroCredentialData = (microCredentialCodes, microCredentialCode) => {
  return (
    microCredentialCodes.find(
      (ex) => ex.microCredentialCode === microCredentialCode
    ) || {}
  );
};

const processIds = (
  ids,
  homeworkId,
  microcredentialId,
  memberCodes,
  targetArray,
  idKey
) => {
  for (const id of ids) {
    const memberData = findMemberData(memberCodes, id);
    targetArray.push({
      homeworkId,
      microcredentialId,
      [idKey]:
        memberData?.personsID ||
        (idKey === "memberId" ? memberData?.organizationID : ""),
    });
  }
};

async function getMemberCode() {
  const scanParams = {
    ExpressionAttributeValues: { ":d": { S: "false" }, ":t": { S: "school" } },
    FilterExpression: "#D = :d and #T <> :t",
    ExpressionAttributeNames: { "#D": "isDeleted", "#T": "type", "#N": "name" },
    ProjectionExpression:
      "memberCode, organizationID, personsID,cityId, #T, #N",
    TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
  };
  const scanResponse = await ddb.scan(scanParams).promise();
  const newResponse = scanResponse.Items.map((records) =>
    AWS.DynamoDB.Converter.unmarshall(records)
  );
  return newResponse;
}

async function getMicroCredentialCode() {
  const scanParams = {
    ExpressionAttributeValues: { ":d": { S: "false" } },
    FilterExpression: "#D = :d",
    ExpressionAttributeNames: { "#D": "isDeleted", "#N": "name" },
    ProjectionExpression: "id, microCredentialCode, #N",
    TableName: `Programs-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
  };
  const scanResponse = await ddb.scan(scanParams).promise();
  const newResponse = scanResponse.Items.map((records) =>
    AWS.DynamoDB.Converter.unmarshall(records)
  );
  return newResponse;
}

const uploadImage = async (imageUrl, homeworkId, date) => {
  const bucketName =
    process.env.STORAGE_S3MYVILLAGEPROJECTADMINPORTALORGPROFILELOGO_BUCKETNAME;

  const response = await axios.get(imageUrl, { responseType: "arraybuffer" });

  // Extract filename from Content-Disposition header
  let contentType = response?.headers["content-type"] ?? "image/jpeg";
  let filename = `${date.getTime().toString()}-${homeworkId}.${
    contentType?.split("/")[1] || "jpeg"
  }`; // Set a default filename if not found
  const contentDisposition = response.headers["content-disposition"];
  if (contentDisposition) {
    const match = contentDisposition.match(/filename="(.+?)"/);
    if (match) {
      filename = match[1];
    }
  }
  const imageBuffer = Buffer.from(response.data, "binary");
  console.log("filename", filename);
  filename = `${date.getTime().toString()}-${filename}`;
  const objectKey = `public/homeworks/${homeworkId}/${filename}`;
  const imageKey = `homeworks/${homeworkId}/${filename}`;

  const params = {
    Bucket: bucketName,
    Key: objectKey,
    Body: imageBuffer,
    ContentType: contentType,
  };

  let imageUploadRes = await s3.upload(params).promise();
  imageUploadRes["imageKey"] = imageKey;
  console.log("imageUploadRes", imageUploadRes);
  return imageUploadRes;
};

const sendNotifications = async (notificationData, homework) => {
  for (const homeworkId in notificationData) {
    let homeworkItem = homework[homeworkId];
    const notificationFunctionName = `notificationFunction-${process.env.ENV}`;
    const payload = {
      arguments: {
        input: {
          title: homeworkItem?.name || "",
          body: homeworkItem?.shortDescription || "",
          homeworkId: homeworkId,
          homeworkDueDate: new Date(homeworkItem?.dueDate).toISOString() ?? "",
          taskNotificationsId: "null",
          notificationType: "homework",
          MVPTokens: homeworkItem?.assignmentPoints ?? 0,
          points: homeworkItem?.assignmentPoints ?? 0,
          isAnswerable: false,
          notificationIcon: "notificationIcons/feedbackNotificationicon.png",
          userList: notificationData[homeworkId],
        },
      },
    };
    console.log("payload", payload);
    const notificationParams = {
      FunctionName: notificationFunctionName,
      InvocationType: "RequestResponse",
      LogType: "None",
      Payload: JSON.stringify(payload),
    };

    await lambda.invoke(notificationParams).promise();
  }
};

const getUserDataForPersonsIds = async (personsIds) => {
  console.log("personsIds function", personsIds);
  const notificationData = {};

  for (const key in personsIds) {
    const personData = [];

    for (const personId of personsIds[key]) {
      const params = {
        ExpressionAttributeValues: {
          ":p": { S: personId.personsID },
          ":e": { S: "arn:aws:sns" },
        },
        FilterExpression: "#P = :p and contains(#E, :e) ",
        ExpressionAttributeNames: {
          "#P": "id",
          "#E": "endpointArn",
        },
        ProjectionExpression: "id, endpointArn, isLogin",
        TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
      };

      const userResponse = await ddb.scan(params).promise();
      const userNewResponse = userResponse.Items.map((record) =>
        AWS.DynamoDB.Converter.unmarshall(record)
      );
      personData.push(...userNewResponse);
    }

    notificationData[key] = personData;
  }

  return notificationData;
};

const getAssociatedUserIds = async (homeworkOrganizationIds) => {
  let personsIds = {};

  for (let homeworkOrganizationData of homeworkOrganizationIds) {
    const homeworkId = homeworkOrganizationData?.homeworkId;

    if (!personsIds[homeworkId]) {
      personsIds[homeworkId] = []; // Initialize as an empty array
    }
    let params = {
      ExpressionAttributeValues: {
        ":o": { S: homeworkOrganizationData?.memberId },
        ":t": { S: "Organization" },
      },
      FilterExpression: "#O = :o and #T = :t",
      ExpressionAttributeNames: {
        "#O": "organizationID",
        "#T": "type",
      },
      ProjectionExpression: "personsID",
      TableName: `Association-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };

    let assocResponse = await ddb.scan(params).promise();
    let assocNewResponse = assocResponse.Items.map((record) =>
      AWS.DynamoDB.Converter.unmarshall(record)
    );
    // console.log('assocNewResponse', assocNewResponse)
    personsIds[homeworkId] = personsIds[homeworkId].concat(assocNewResponse);
  }
  // console.log('personsIds', personsIds)

  return personsIds;
};
