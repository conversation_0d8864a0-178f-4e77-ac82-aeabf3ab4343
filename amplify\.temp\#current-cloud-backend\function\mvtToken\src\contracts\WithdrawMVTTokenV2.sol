// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "./WithdrawMVTToken.sol";

contract WithdrawMVTTokenV2 is WithdrawMVTToken {
    // Struct to hold user details (firstName, lastName, email)
    struct UserDetails {
        string firstName;
        string lastName;
        string email;
    }

    // Mapping of user addresses to their user details
    mapping(address => UserDetails) public userDetails;

    // New event for user details update
    event UserDetailsUpdated(
        address indexed user,
        string firstName,
        string lastName,
        string email
    );

    // Function to update the user details
    function updateUserDetails(
        string memory _firstName,
        string memory _lastName,
        string memory _email
    ) external {
        require(bytes(_firstName).length > 0, "First name is required");
        require(bytes(_lastName).length > 0, "Last name is required");
        require(bytes(_email).length > 0, "Email is required");

        userDetails[msg.sender] = UserDetails({
            firstName: _firstName,
            lastName: _lastName,
            email: _email
        });

        emit UserDetailsUpdated(msg.sender, _firstName, _lastName, _email);
    }

    // Function to batch update old users' details (can be called by an admin)
    function batchUpdateUserDetails(
        address[] calldata users,
        string[] calldata firstNames,
        string[] calldata lastNames,
        string[] calldata emails
    ) external onlyOwner {
        require(users.length == firstNames.length, "Mismatched lengths");
        require(users.length == lastNames.length, "Mismatched lengths");
        require(users.length == emails.length, "Mismatched lengths");

        for (uint256 i = 0; i < users.length; i++) {
            userDetails[users[i]] = UserDetails({
                firstName: firstNames[i],
                lastName: lastNames[i],
                email: emails[i]
            });

            emit UserDetailsUpdated(
                users[i],
                firstNames[i],
                lastNames[i],
                emails[i]
            );
        }
    }
}
