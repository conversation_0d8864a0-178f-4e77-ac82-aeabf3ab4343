/* Amplify Params - DO NOT EDIT
  API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT
  API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT
  AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID
  ENV
  REGION
Amplify Params - DO NOT EDIT */ /*
Copyright 2017 - 2017 Amazon.com, Inc. or its affiliates. All Rights Reserved.
Licensed under the Apache License, Version 2.0 (the "License"). You may not use this file except in compliance with the License. A copy of the License is located at
    http://aws.amazon.com/apache2.0/
or in the "license" file accompanying this file. This file is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and limitations under the License.
*/

const express = require("express");
const bodyParser = require("body-parser");
const awsServerlessExpressMiddleware = require("aws-serverless-express/middleware");
const uuid = require("uuid");

// declare a new express app
const app = express();
app.use(bodyParser.json());
app.use(awsServerlessExpressMiddleware.eventContext());

// Enable CORS for all methods
app.use(function (req, res, next) {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Headers", "*");
  next();
});

const AWS = require("aws-sdk");
AWS.config.update({
  maxRetries: 3,
  httpOptions: { timeout: 30000, connectTimeout: 5000 },
  // region: process.env.REGION,
  region: "us-east-1",
  // accessKeyId: process.env.ACCESS_KEY_ID,
  accessKeyId: "********************",
  // secretAccessKey: process.env.SECRET_ACCESS_KEY,
  secretAccessKey: "Ye2ihRW5qrV9YljN2pmbcT5jFWzVob3Y9dXDAWAq",
});
const lambda = new AWS.Lambda();
var ddb = new AWS.DynamoDB();

app.post("/checkMemberCodeExist", async function (req, res) {
  try {
    const memberCode = req.body.memberCode ?? "";
    const birthday = req.body?.birthday ?? "";
    const userParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":s": { BOOL: false },
      },
      FilterExpression: "#D = :d and #S = :s",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#S": "isStakeholder",
        "#N": "name",
      },
      ProjectionExpression: "id, memberCode, #N, birthday",
      TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };

    const userDetail = await ddb.scan(userParams).promise();
    const userDetailRes = userDetail.Items.map((records) =>
      AWS.DynamoDB.Converter.unmarshall(records)
    );

    const existingMemberCode = userDetailRes.find(
      (inner) =>
        inner.memberCode === memberCode &&
        new Date(new Date(inner.birthday).setHours(0, 0, 0)).setMilliseconds(
          0
        ) === new Date(new Date(birthday).setHours(0, 0, 0)).setMilliseconds(0)
    );
    console.log("existingMemberCode", existingMemberCode);

    const response = {
      status: 200,
      isExist: !!existingMemberCode,
      userId: existingMemberCode?.id ?? "",
      message: existingMemberCode
        ? "Birthday and Member code is match."
        : "Birthday and Member code does not matched.",
    };

    res.json(response);
  } catch (error) {
    res.status(error.statusCode || 500).json({ error: error.message });
  }
});

app.post("/getUserDetail", async function (req, res) {
  try {
    const memberCode = req.body.memberCode ?? "";
    const userId = req.body?.userId ?? "";
    const userParams = {
      TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
      ExpressionAttributeNames: { "#N": "name", "#S": "status" },
      ProjectionExpression:
        "id, memberCode, #N, birthday, membershipId, imageUrl,studentEnrollmentDate,gender,#S,createdAt",
      Key: { id: { S: userId } },
    };
    const userDetail = await ddb.getItem(userParams).promise();
    let getUserDetail = userDetail["Item"]
      ? AWS.DynamoDB.Converter.unmarshall(userDetail["Item"])
      : null;
    console.log("getUserDetail", getUserDetail);

    if (getUserDetail && getUserDetail?.id) {
      const membershipParams = {
        TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        Key: { id: { S: getUserDetail.membershipId } },
      };
      const getMembershipRes = await ddb.getItem(membershipParams).promise();
      const membershipItem = getMembershipRes["Item"]
        ? AWS.DynamoDB.Converter.unmarshall(getMembershipRes["Item"])
        : null;
      console.log("membershipItem", membershipItem);
      getUserDetail.totalCoin =
        parseFloat(membershipItem?.currentImpactScore) ?? 0;

      getUserDetail["name"] = getUserDetail?.name;
      getUserDetail["imageUrl"] =
        getUserDetail?.imageUrl || "public/game-images/Main MVP logo.PNG";
      delete getUserDetail?.membershipId;
      delete getUserDetail.givenName;
      console.log("getUserDetail", getUserDetail);
    } else {
      getUserDetail = {
        message: "User not found.",
      };
    }

    const response = {
      status: 200,
      data: getUserDetail,
    };

    res.json(response);
  } catch (error) {
    res.status(error.statusCode || 500).json({ error: error.message });
  }
});

app.post("/gameLogout", async function (req, res) {
  try {
    const memberCode = req.body.memberCode;
    const userId = req.body.userId;
    const logoutTime = req.body.logoutTime;

    var params = {
      TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
      Key: {
        id: { S: userId },
      },
      ExpressionAttributeNames: {
        "#L": "logoutTime",
      },
      ExpressionAttributeValues: {
        ":l": {
          S: logoutTime,
        },
      },
      ReturnValues: "ALL_NEW",
      UpdateExpression: "SET #L = :l",
    };
    console.log("params", params);
    let updatePostData = await ddb.updateItem(params).promise();
    console.log("updatePostData", updatePostData);
    res.status(200).json({ data: "Logged out successfully." });
  } catch (error) {
    res.status(error.statusCode).json({ error: error });
  }
});

app.post("/createGameActivity", async function (req, res) {
  try {
    const date = new Date();
    const params = {
      Item: {
        id: { S: uuid.v4() },
        __typename: { S: "Activity" },
        type: { S: req.body?.type },
        moduleName: { S: req.body?.moduleName || "" },
        moduleType: { S: req.body?.moduleType || "" },
        gameData: {
          M: {
            gameLoginTime: {
              S: req.body?.gameData?.gameLoginTime || "",
            },
            gameName: {
              S: req.body?.gameData?.gameName || "",
            },
            appName: {
              S: req.body?.gameData?.appName || "",
            },
          },
        },
        requestStatus: { S: req.body?.requestStatus },
        activityType: { S: req.body?.activityType },
        moduleImageUrl: { S: req.body?.moduleImageUrl || "" },
        cityId: { S: req.body?.cityId },
        createdUserId: { S: req.body?.createdUserId || "" },
        moduleId: { S: req.body?.moduleId },
        isDeleted: { S: "false" },
        createdUserName: { S: req.body?.createdUserName || "" },
        createdAt: { S: date.toISOString() },
        updatedAt: { S: date.toISOString() },
        _version: { N: "1" },
        _lastChangedAt: { N: date.getTime().toString() },
      },
      ReturnConsumedCapacity: "TOTAL",
      ReturnValues: "ALL_OLD",
      TableName: `Activity-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };

    await ddb.putItem(params).promise();
    res.status(200).json({ data: "Game activity created successfully" });
  } catch (error) {
    const statusCode = error.statusCode || 500;
    res.status(statusCode).json({ error: error });
  }
});

app.post("/recordMissionTime", async function (req, res) {
  try {
    const date = new Date();
    const params = {
      Item: {
        id: { S: uuid.v4() },
        __typename: { S: "Activity" },
        type: { S: req.body?.type },
        moduleName: { S: req.body?.moduleName || "" },
        moduleType: { S: req.body?.moduleType || "" },
        gameData: {
          M: {
            gameLoginTime: {
              S: req.body?.gameData?.gameLoginTime || "",
            },
            gameName: {
              S: req.body?.gameData?.gameName || "",
            },
            duration: {
              S: req.body?.gameData?.duration || "",
            },
            appName: {
              S: req.body?.gameData?.appName || "",
            },
          },
        },
        requestStatus: { S: req.body?.requestStatus },
        activityType: { S: req.body?.activityType },
        moduleImageUrl: { S: req.body?.moduleImageUrl || "" },
        cityId: { S: req.body?.cityId },
        createdUserId: { S: req.body?.createdUserId || "" },
        moduleId: { S: req.body?.moduleId },
        isDeleted: { S: "false" },
        createdUserName: { S: req.body?.createdUserName || "" },
        createdAt: { S: date.toISOString() },
        updatedAt: { S: date.toISOString() },
        _version: { N: "1" },
        _lastChangedAt: { N: date.getTime().toString() },
      },
      ReturnConsumedCapacity: "TOTAL",
      ReturnValues: "ALL_OLD",
      TableName: `Activity-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };
    console.log("params", params);
    await ddb.putItem(params).promise();
    res.status(200).json({ data: "Game activity created successfully" });
  } catch (error) {
    const statusCode = error.statusCode || 500;
    res.status(statusCode).json({ error: error });
  }
});

app.post("/getStudentListWithRank", async function (req, res) {
  try {
    console.log("req.body.userId", req.body.userId);
    const ranking = await getRankingUser();
    console.log("ranking: ", ranking);
    const top4student = await top4studentFunction(req.body.userId);
    console.log("top4student: ", top4student);
    const top4family = await top4familyFunction(req.body.userId);
    console.log("top4family: ", top4family);
    res.status(200).json({ data: { ranking, top4student, top4family } });
  } catch (error) {
    console.error("Error fetching student list with rank:", error);
    res
      .status(500)
      .json({ message: "Internal server error", error: error.message });
  }
});

async function getRankingUser() {
  try {
    const scanParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":user": { S: "User" },
        ":member": { S: "member" },
      },
      FilterExpression: "#D = :d and #T IN (:user, :member)",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#T": "type",
      },
      ProjectionExpression: "memberCode, personsID, organizationID, isActive, cityId, MVPTokens, #T",
      TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };

    const scanResponse = await ddb.scan(scanParams).promise();

    const members = scanResponse.Items.map((record) =>
      AWS.DynamoDB.Converter.unmarshall(record)
    );

    const sortedMembers = members
      .map((member) => ({
        ...member,
        MVPTokens: parseFloat(member.MVPTokens),
      }))
      .sort((a, b) => b.MVPTokens - a.MVPTokens)
      .slice(0, 10);

    const usersWithDetails = await Promise.all(
      sortedMembers.map(async (member) => {
        const userTable = member.type === "User" ? "User" : "Organizations";
        const userKey = member.type === "User" ? member.personsID : member.organizationID;

        const userParams = {
          TableName: `${userTable}-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          Key: { id: { S: userKey } },
          ExpressionAttributeNames: {
            "#N": "name", // Alias for reserved keyword
          },
          ProjectionExpression: "id, isStakeholder, #N, email, membershipId, givenName, familyName, stackholderCities, cityId, imageUrl",
        };

        const userResponse = await ddb.getItem(userParams).promise();
        const userItem = userResponse.Item
          ? AWS.DynamoDB.Converter.unmarshall(userResponse.Item)
          : {};

        const cityParams = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
          },
          FilterExpression: "#D = :d",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#N": "name", // Alias for reserved keyword
          },
          ProjectionExpression: "id, #N",
          TableName: `City-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };

        const cityResponse = await ddb.scan(cityParams).promise();
        const cities = cityResponse.Items.map((record) =>
          AWS.DynamoDB.Converter.unmarshall(record)
        );

        if (userItem.stackholderCities?.length && userItem.isStakeholder) {
          const cityMap = cities.reduce((acc, city) => {
            acc[city.id] = city.name;
            return acc;
          }, {});

          userItem.cities = userItem.stackholderCities
            .map((cityId) => cityMap[cityId])
            .filter(Boolean)
            .join(", ");
        } else {
          const city = cities.find((city) => city.id === userItem.cityId);
          userItem.cities = city ? city.name : null;
        }

        return {
          ...member,
          userItem,
        };
      })
    );

    return usersWithDetails.sort((a, b) => b.MVPTokens - a.MVPTokens);
  } catch (error) {
    console.error("Error fetching ranked users:", error);
    throw new Error("Failed to fetch ranking.");
  }
}

async function top4studentFunction(userId) {
  try {
    const envSuffix = `${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
    const userTable = `User-${envSuffix}`;
    const cityTable = `City-${envSuffix}`;
    const membershipTable = `Membership-${envSuffix}`;

    // Helper function for DynamoDB `getItem`
    async function getItem(params) {
      const response = await ddb.getItem(params).promise();
      return response.Item ? AWS.DynamoDB.Converter.unmarshall(response.Item) : null;
    }

    // Helper function for scanning with pagination
    async function scanDynamoDB(params) {
      let items = [];
      let lastEvaluatedKey;
      do {
        const response = await ddb.scan({ ...params, ExclusiveStartKey: lastEvaluatedKey }).promise();
        items = items.concat(response.Items);
        lastEvaluatedKey = response.LastEvaluatedKey;
      } while (lastEvaluatedKey);
      return items.map(AWS.DynamoDB.Converter.unmarshall);
    }

    // Fetch user details
    const userParams = {
      TableName: userTable,
      Key: { id: { S: userId } },
      ExpressionAttributeNames: { "#N": "name" },
      ProjectionExpression:
        "id, isStakeholder, #N, email, membershipId, givenName, familyName, stackholderCities, cityId, imageUrl",
    };

    const userItem = await getItem(userParams);

    if (!userItem || !userItem.cityId) {
      throw new Error("User not found or missing cityId");
    }

    // Scan for users in the same city
    const scanParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":c": { S: userItem.cityId },
        ":is": { BOOL: false },
      },
      FilterExpression: "#D = :d AND #C = :c AND #IS = :is",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#C": "cityId",
        "#IS": "isStakeholder",
        "#N": "name",
      },
      ProjectionExpression:
        "id, #IS, #N, email, membershipId, givenName, familyName, stackholderCities, cityId, imageUrl, MVPTokens",
      TableName: userTable,
    };

    const users = await scanDynamoDB(scanParams);

    if (users.length === 0) {
      return [];
    }

    // Fetch all city details once
    const cityParams = {
      ExpressionAttributeValues: { ":d": { S: "false" } },
      FilterExpression: "#D = :d",
      ExpressionAttributeNames: { "#D": "isDeleted", "#N": "name" },
      ProjectionExpression: "id, #N",
      TableName: cityTable,
    };

    const cities = await scanDynamoDB(cityParams);
    const cityMap = cities.reduce((acc, city) => {
      acc[city.id] = city.name;
      return acc;
    }, {});

    // Fetch membership details for each user
    async function fetchMembershipDetails(userId) {
      const membershipParams = {
        ExpressionAttributeValues: {
          ":d": { S: "false" },
          ":c": { S: userId },
        },
        FilterExpression: "#D = :d AND #C = :c",
        ExpressionAttributeNames: {
          "#D": "isDeleted",
          "#C": "personsID",
          "#T": "type",
        },
        ProjectionExpression: "memberCode, personsID, organizationID, isActive, cityId, MVPTokens, #T",
        TableName: membershipTable,
      };
      const memberships = await scanDynamoDB(membershipParams);
      return memberships.reduce((acc, membership) => ({ ...acc, ...membership }), {});
    }

    const membershipPromises = users.map((user) => fetchMembershipDetails(user.id));
    const memberships = await Promise.all(membershipPromises);

    // Enrich users with city and membership details
    const enrichedUsers = users.map((user, index) => {
      const membership = memberships[index];
      const cityName = cityMap[user.cityId] || "Unknown";
      return {
        MVPTokens: Number(membership.MVPTokens || 0),
        cityId: user.cityId,
        memberCode: membership.memberCode,
        personsID: membership.personsID,
        type: membership.type || "User",
        userItem: {
          ...user,
          cities: cityName,
        },
      };
    });

    // Sort and return the top 4 students
    const sortedMembers = enrichedUsers.sort((a, b) => b.MVPTokens - a.MVPTokens);
    return sortedMembers.slice(0, 4);
  } catch (error) {
    console.error("Error fetching top 4 students:", error.message);
    throw new Error("Failed to retrieve top 4 students. Please try again later.");
  }
}


// Placeholder for fetching membership details (implement based on actual API)
async function fetchMembershipDetails(userId) {
  // Example implementation: Replace with actual API/database logic
  return { membershipId: `M-${userId}`, membershipType: "Basic" };
}


async function fetchMembershipDetails(userId) {
  const params = {
    ExpressionAttributeValues: {
      ":d": { S: "false" },
      ":c": { S: userId },
    },
    FilterExpression: "#D = :d AND #C = :c",
    ExpressionAttributeNames: {
      "#D": "isDeleted",
      "#C": "personsID",
      "#T": "type",
    },
    ProjectionExpression:
      "memberCode, personsID, organizationID, isActive, cityId, MVPTokens, #T",
    // TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
  };

  const response = await ddb.scan(params).promise();
  return response.Items.map(AWS.DynamoDB.Converter.unmarshall).reduce(
    (acc, res) => {
      return { ...acc, ...res };
    },
    {}
  );
}

async function top4familyFunction(userId) {
  try {
    // Centralize environment variables for reuse
    const ENV = process.env.ENV;
    const GRAPHQL_API_ID = process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT;
    const ASSOCIATION_TABLE = `Association-${GRAPHQL_API_ID}-${ENV}`;
    const USER_TABLE = `User-${GRAPHQL_API_ID}-${ENV}`;
    const ORGANIZATION_TABLE = `Organizations-${GRAPHQL_API_ID}-${ENV}`;
    const CITY_TABLE = `City-${GRAPHQL_API_ID}-${ENV}`;
    const MEMBERSHIP_TABLE = `Membership-${GRAPHQL_API_ID}-${ENV}`;

    // Fetch associations for the user
    const associationParams = {
      ExpressionAttributeValues: {
        ":u": { S: userId },
        ":tp": { S: "Person" },
        ":to": { S: "Organization" },
      },
      FilterExpression: "(#P = :u or #OP = :u) and (#T = :tp or #T = :to)",
      ExpressionAttributeNames: {
        "#P": "personsID",
        "#OP": "otherPersonsID",
        "#T": "type",
      },
      ProjectionExpression:
        "personsID, otherPersonsID, organizationID, businessID, #T",
      TableName: ASSOCIATION_TABLE,
    };

    const associationResponse = await ddb.scan(associationParams).promise();
    const associations = associationResponse.Items.map((item) =>
      AWS.DynamoDB.Converter.unmarshall(item)
    );

    // Fetch city data once for reuse
    const cityParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
      },
      FilterExpression: "#D = :d",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#N": "name",
      },
      ProjectionExpression: "id, #N",
      TableName: CITY_TABLE,
    };

    const cityResponse = await ddb.scan(cityParams).promise();
    const cities = cityResponse.Items.map((item) =>
      AWS.DynamoDB.Converter.unmarshall(item)
    );
    const cityMap = cities.reduce((acc, city) => {
      acc[city.id] = city.name;
      return acc;
    }, {});

    // Fetch user and organization details for all associations
    const usersWithDetails = await Promise.all(
      associations.map(async (member) => {
        const isPerson = member.type === "Person";
        const tableName = isPerson ? USER_TABLE : ORGANIZATION_TABLE;
        const userKey = isPerson
          ? member.personsID ?? member.otherPersonsID
          : member.organizationID;

        const userParams = {
          TableName: tableName,
          Key: { id: { S: userKey } },
          ExpressionAttributeNames: {
            "#N": "name",
          },
          ProjectionExpression:
            "id, isStakeholder, #N, email, membershipId, givenName, familyName, stackholderCities, cityId, imageUrl",
        };

        const userResponse = await ddb.getItem(userParams).promise();
        const userItem = userResponse.Item
          ? AWS.DynamoDB.Converter.unmarshall(userResponse.Item)
          : null;

        if (!userItem) return null;

        // Determine cities for the user
        if (userItem.isStakeholder && isPerson && userItem.stackholderCities) {
          userItem.cities = userItem.stackholderCities
            .map((id) => cityMap[id])
            .filter(Boolean)
            .join(", ");
        } else {
          const cityName = cityMap[userItem.cityId] || null;
          userItem.cities = cityName;
        }

        return userItem;
      })
    );

    // Remove null entries from user details
    const filteredUsers = usersWithDetails.filter(Boolean);

    // Fetch memberships for the users
    const memberships = await Promise.all(
      filteredUsers.map(async (userItem) => {
        const membershipParams = {
          TableName: MEMBERSHIP_TABLE,
          Key: { id: { S: userItem.membershipId } },
          ExpressionAttributeNames: {
            "#T": "type",
          },
          ProjectionExpression:
            "memberCode, personsID, organizationID, isActive, cityId, MVPTokens, #T",
        };

        const membershipResponse = await ddb.getItem(membershipParams).promise();
        const membershipData = membershipResponse.Item
          ? AWS.DynamoDB.Converter.unmarshall(membershipResponse.Item)
          : null;

        return {
          ...membershipData,
          ...userItem,
        };
      })
    );

    // Sort and pick the top 4 members
    const sortedMembers = memberships
      .map((member) => ({
        ...member,
        MVPTokens: parseFloat(member.MVPTokens || 0), // Ensure MVPTokens is numeric
      }))
      .sort((a, b) => b.MVPTokens - a.MVPTokens);

    // Fetch top 4 and include the userItem as in getRankingUser response format
    const top4Members = sortedMembers.slice(0, 4).map((member) => ({
      ...member,
      userItem: {
        imageUrl: member.imageUrl,
        cityId: member.cityId,
        isStakeholder: member.isStakeholder,
        email: member.email,
        id: member.id,
        stackholderCities: member.stackholderCities,
        familyName: member.familyName,
        name: member.name,
        givenName: member.givenName,
        membershipId: member.membershipId,
        cities: member.cities,
      },
    }));

    return top4Members;
  } catch (error) {
    console.error("Error fetching top family function:", error);
    throw new Error(error.message);
  }
}



app.listen(3000, function () {
  console.log("App started");
});

// Export the app object. When executing the application local this does nothing. However,
// to port it to AWS Lambda we will create a wrapper around that will load the app from
// this file
module.exports = app;
