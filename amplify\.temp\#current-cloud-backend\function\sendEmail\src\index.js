
const AWS = require('aws-sdk');

AWS.config.update({
    maxRetries: 3,
    httpOptions: { timeout: 30000, connectTimeout: 5000 },
    region: process.env.REGION,
    accessKeyId: process.env.ACCESS_KEY_ID,
    secretAccessKey: process.env.SECRET_ACCESS_KEY,
});

const ses = new AWS.SES({ region: 'us-east-1' });
const s3 = new AWS.S3();


const handler = async (event, context, callback) => {

    switch (event.fieldName) {
        case "welcomeMail":
            console.log("welcome")
            let fileContent;
            let { firstName, memberCode, cityLocation, to, templateType, phoneNumber } = event.arguments.input
            console.log(event.arguments.input)
            const params = {
                Bucket: "myvillageproject-admin-portal-org-profile-logo141-amplifydev",
                Key: "public/emailTemplates/newStakeEmailTemplate.html",
            };

            const data = await s3.getObject(params).promise();
            fileContent = data.Body.toString('utf-8');

            const htmlBody = fileContent.replace('${firstName}', firstName ? firstName : "")
                .replace('${memberCode}', memberCode ? memberCode : "")
                .replace('${cityLocation}', cityLocation ? cityLocation : "")
                .replace('${email}', to ? to : "")
                .replace('${phoneNumber}', phoneNumber ? phoneNumber : "");
            // const textData = `Welcome ${firstName ? firstName : ""} to the MyVillage Project!\nYour Member Code is ${memberCode ? memberCode : ""}.\nWe look forward to partnering with you to help empower the ${cityLocation ? cityLocation : ""} community with an environment that fosters unity around lifelong learning and creates a future where opportunity and wealth are the norm for all families and organizations.`
            const textData = `Welcome ${firstName ? firstName : ""} to the MyVillage Project!\n\nMember Code: ${memberCode ? memberCode : ""}\nEmail: ${to ? to : ""}\nPhone: ${phoneNumber ? phoneNumber : ""}\n\nWe look forward to partnering with you to help empower the ${cityLocation ? cityLocation : ""} community with an environment that fosters unity around lifelong learning and creates a future where opportunity and wealth are the norm for all families and organizations.`;

            const sendMailRes = await sendMail(firstName, memberCode, cityLocation, to, htmlBody, textData)
            callback(null, sendMailRes);
            break;

        default:
            console.log("default")
            break;
    }
};

const sendMail = async (firstName, memberCode, cityLocation, to, s3Template, textData) => {

    const params = {
        Destination: {
            ToAddresses: [to], // Use the email from the event
        },
        Message: {
            Body: {
                Html: {
                    Charset: 'UTF-8',
                    Data: s3Template,
                },
                Text: {
                    Charset: 'UTF-8',
                    Data: textData,
                },
            },
            Subject: {
                Charset: 'UTF-8',
                Data: 'Welcome to Our Platform!',
            },
        },
        Source: '<EMAIL>', // Replace with your verified sender email address
    };

    try {
        await ses.sendEmail(params).promise();
        return {
            statusCode: 200,
            message: "Email sent successfully!",
        };
    } catch (error) {
        console.error(error);
        return {
            statusCode: 500,
            body: `Error sending email: ${error.message}`,
        };
    }
}

module.exports = { handler }