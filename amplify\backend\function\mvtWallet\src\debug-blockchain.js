#!/usr/bin/env node

/**
 * Debug script to test blockchain connectivity and contract functionality
 * Run this script to diagnose USDC transfer issues
 * 
 * Usage: node debug-blockchain.js
 */

const contractService = require('./shared/blockchain/contractService');

async function runDiagnostics() {
  console.log('='.repeat(60));
  console.log('BLOCKCHAIN CONNECTIVITY DIAGNOSTICS');
  console.log('='.repeat(60));
  
  try {
    // Test 1: Environment Variables
    console.log('\n1. ENVIRONMENT VARIABLES CHECK');
    console.log('-'.repeat(40));
    const envValidation = contractService.validateEnvironmentVariables();
    console.log('Environment validation result:', JSON.stringify(envValidation, null, 2));
    
    if (!envValidation.isValid) {
      console.error('❌ Environment variables are missing!');
      console.error('Missing variables:', envValidation.missing);
      return;
    }
    console.log('✅ All required environment variables are present');
    
    // Test 2: Basic Connection
    console.log('\n2. BASIC CONNECTION CHECK');
    console.log('-'.repeat(40));
    const isConnected = contractService.isBlockchainConnected();
    console.log('Basic connection status:', isConnected);
    
    if (!isConnected) {
      console.error('❌ Basic blockchain connection failed');
      return;
    }
    console.log('✅ Basic blockchain connection successful');
    
    // Test 3: Comprehensive Connection Test
    console.log('\n3. COMPREHENSIVE CONNECTION TEST');
    console.log('-'.repeat(40));
    const connectionTest = await contractService.testContractConnection();
    console.log('Connection test result:', JSON.stringify(connectionTest, null, 2));
    
    // Test 4: Contract Balance
    console.log('\n4. CONTRACT BALANCE TEST');
    console.log('-'.repeat(40));
    try {
      const balance = await contractService.getContractUSDCBalance();
      console.log('✅ Contract USDC balance:', balance);
    } catch (balanceError) {
      console.error('❌ Failed to get contract balance:', balanceError.message);
    }
    
    // Test 5: Wallet Address
    console.log('\n5. WALLET ADDRESS TEST');
    console.log('-'.repeat(40));
    const walletAddress = contractService.getWalletAddress();
    console.log('Wallet address:', walletAddress);
    
    if (!walletAddress) {
      console.error('❌ Wallet address not available');
      return;
    }
    console.log('✅ Wallet address available');
    
    // Test 6: Test Transfer (Dry Run)
    console.log('\n6. TRANSFER VALIDATION TEST');
    console.log('-'.repeat(40));
    console.log('Note: This would test a small transfer to validate the process');
    console.log('For safety, actual transfer testing should be done in a controlled environment');
    
    console.log('\n' + '='.repeat(60));
    console.log('DIAGNOSTICS COMPLETE');
    console.log('='.repeat(60));
    
    // Summary
    if (isConnected && envValidation.isValid) {
      console.log('✅ OVERALL STATUS: Blockchain connection appears to be working');
      console.log('If USDC transfers are still failing, the issue may be:');
      console.log('- Network connectivity issues during transaction execution');
      console.log('- Insufficient gas or USDC balance in the contract');
      console.log('- Contract method execution failures');
      console.log('- RPC endpoint reliability issues');
    } else {
      console.log('❌ OVERALL STATUS: Blockchain connection has issues');
      console.log('Please fix the identified issues before attempting USDC transfers');
    }
    
  } catch (error) {
    console.error('\n❌ DIAGNOSTICS FAILED');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run diagnostics if this script is executed directly
if (require.main === module) {
  runDiagnostics().catch(console.error);
}

module.exports = { runDiagnostics };
