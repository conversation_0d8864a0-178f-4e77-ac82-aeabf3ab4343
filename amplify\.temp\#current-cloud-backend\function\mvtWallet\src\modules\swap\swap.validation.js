const { 
  validateMVTAmount, 
  isNonEmptyString,
  validateWalletAddress 
} = require('../../shared/utils/validationUtils');

/**
 * Validate swap request input
 * @param {object} input - Swap request input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateSwapRequestInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { mvtAmount, description } = input;

  // Validate MVT amount (must be integer)
  const amountValidation = validateMVTAmount(mvtAmount);
  if (!amountValidation.isValid) {
    return amountValidation;
  }

  // Description is optional but if provided must be non-empty string
  if (description && !isNonEmptyString(description)) {
    return {
      isValid: false,
      error: "Description must be a non-empty string if provided"
    };
  }

  return { isValid: true };
}

/**
 * Validate swap approval input
 * @param {object} input - Swap approval input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateSwapApprovalInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { swapRequestId } = input;

  if (!swapRequestId || !isNonEmptyString(swapRequestId)) {
    return {
      isValid: false,
      error: "Valid swap request ID is required"
    };
  }

  return { isValid: true };
}

/**
 * Validate swap rejection input
 * @param {object} input - Swap rejection input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateSwapRejectionInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { swapRequestId, rejectionReason } = input;

  if (!swapRequestId || !isNonEmptyString(swapRequestId)) {
    return {
      isValid: false,
      error: "Valid swap request ID is required"
    };
  }

  if (!rejectionReason || !isNonEmptyString(rejectionReason)) {
    return {
      isValid: false,
      error: "Rejection reason is required and must be a non-empty string"
    };
  }

  return { isValid: true };
}

/**
 * Validate swap request ID
 * @param {string} swapRequestId - Swap request ID to validate
 * @returns {object} - Validation result
 */
function validateSwapRequestId(swapRequestId) {
  if (!swapRequestId || !isNonEmptyString(swapRequestId)) {
    return { isValid: false, error: 'Valid swap request ID is required' };
  }
  
  // Check if it follows the expected format (swap-req-timestamp-random)
  const swapIdPattern = /^swap-req-\d+-[a-z0-9]+$/;
  if (!swapIdPattern.test(swapRequestId)) {
    return { isValid: false, error: 'Invalid swap request ID format' };
  }
  
  return { isValid: true };
}

/**
 * Validate swap list request parameters
 * @param {object} params - Request parameters
 * @returns {object} - Validation result
 */
function validateSwapListRequest(params) {
  if (!params) {
    return { isValid: true }; // No parameters is valid for list requests
  }
  
  const { limit, userId } = params;
  
  // Validate limit if provided
  if (limit !== undefined) {
    if (typeof limit !== 'number' || limit <= 0 || limit > 100) {
      return { isValid: false, error: 'Limit must be a number between 1 and 100' };
    }
  }
  
  // Validate userId if provided
  if (userId !== undefined && (!userId || !isNonEmptyString(userId))) {
    return { isValid: false, error: 'User ID must be a non-empty string if provided' };
  }
  
  return { isValid: true };
}

/**
 * Validate swap feasibility parameters
 * @param {object} params - Swap feasibility parameters
 * @returns {object} - Validation result
 */
function validateSwapFeasibilityParams(params) {
  if (!params) {
    return { isValid: false, error: 'Swap feasibility parameters are required' };
  }
  
  const { mvtAmount, userId } = params;
  
  // Validate MVT amount
  const mvtValidation = validateMVTAmount(mvtAmount);
  if (!mvtValidation.isValid) {
    return mvtValidation;
  }
  
  // Validate user ID
  if (!userId || !isNonEmptyString(userId)) {
    return { isValid: false, error: 'Valid user ID is required' };
  }
  
  return { isValid: true };
}

/**
 * Validate user wallet address for swap
 * @param {string} walletAddress - Wallet address to validate
 * @returns {object} - Validation result
 */
function validateSwapWalletAddress(walletAddress) {
  if (!walletAddress) {
    return { 
      isValid: false, 
      error: 'Please add a valid blockchain wallet address to your profile before requesting withdrawals' 
    };
  }
  
  return validateWalletAddress(walletAddress);
}

/**
 * Validate swap status
 * @param {string} status - Status to validate
 * @returns {object} - Validation result
 */
function validateSwapStatus(status) {
  const validStatuses = ['PENDING', 'APPROVED', 'REJECTED', 'COMPLETED', 'EXPIRED'];
  
  if (!status || !validStatuses.includes(status)) {
    return { 
      isValid: false, 
      error: `Invalid swap status. Must be one of: ${validStatuses.join(', ')}` 
    };
  }
  
  return { isValid: true };
}

module.exports = {
  validateSwapRequestInput,
  validateSwapApprovalInput,
  validateSwapRejectionInput,
  validateSwapRequestId,
  validateSwapListRequest,
  validateSwapFeasibilityParams,
  validateSwapWalletAddress,
  validateSwapStatus
};
