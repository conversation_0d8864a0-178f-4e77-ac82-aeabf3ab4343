{"lambdaLayers": [], "environmentVariableList": [{"cloudFormationParameterName": "region", "environmentVariableName": "REGION"}, {"cloudFormationParameterName": "accessKeyId", "environmentVariableName": "ACCESS_KEY_ID"}, {"cloudFormationParameterName": "secretAccessKey", "environmentVariableName": "SECRET_ACCESS_KEY"}, {"cloudFormationParameterName": "mvtWithdrawContractAddress", "environmentVariableName": "MVT_WITHDRAW_CONTRACT_ADDRESS"}, {"cloudFormationParameterName": "privateKey", "environmentVariableName": "PRIVATE_KEY"}, {"cloudFormationParameterName": "ethereumRpcUrl", "environmentVariableName": "ETHEREUM_RPC_URL"}, {"cloudFormationParameterName": "mvtUsdcContractAddress", "environmentVariableName": "MVT_USDC_CONTRACT_ADDRESS"}, {"cloudFormationParameterName": "stripeSecretKey", "environmentVariableName": "STRIPE_SECRET_KEY"}], "permissions": {"api": {"myvillageprojectadmi": ["Query", "Mutation", "Subscription"]}, "auth": {"myvillageprojectadmifeb4ea87": ["read"]}}}