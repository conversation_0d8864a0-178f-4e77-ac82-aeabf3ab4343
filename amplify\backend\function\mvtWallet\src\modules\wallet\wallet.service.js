const { AWS, ddb } = require('../../config/aws');
const { getTableName, tableExists } = require('../../shared/database/dynamoUtils');
const { CENTRAL_WALLET_ID } = require('../../shared/constants');
const { createDatabaseLogger, logError } = require('../../shared/utils/logger');

/**
 * Get or create central wallet balance with eventual consistency handling
 * @param {boolean} consistentRead - Whether to use strongly consistent read (default: false)
 * @returns {Promise<object>} - Central wallet data
 */
async function getCentralWalletBalance(consistentRead = false) {
  try {
    const params = {
      TableName: getTableName("MVTTokenWallet"),
      Key: {
        id: { S: CENTRAL_WALLET_ID },
      },
      ConsistentRead: consistentRead // ✅ ADDED: Support for strongly consistent reads
    };

    const result = await ddb.getItem(params).promise();

    if (result.Item) {
      const wallet = AWS.DynamoDB.Converter.unmarshall(result.Item);
      return {
        id: wallet.id,
        balance: wallet.balance || 0,
        lastMintedAt: wallet.lastMintedAt,
        totalMinted: wallet.totalMinted || 0,
        totalTransferred: wallet.totalTransferred || 0,
        totalReceived: wallet.totalReceived || 0, // ✅ FIXED: Added missing totalReceived field
        createdAt: wallet.createdAt,
      };
    } else {
      // Create central wallet if it doesn't exist
      const now = new Date().toISOString();
      const newWallet = {
        id: CENTRAL_WALLET_ID,
        balance: 0,
        totalMinted: 0,
        totalTransferred: 0,
        totalReceived: 0, // ✅ FIXED: Added missing totalReceived field
        isDeleted: "false",
        createdAt: now,
        updatedAt: now,
        __typename: "MVTTokenWallet",
      };

      const createParams = {
        TableName: getTableName("MVTTokenWallet"),
        Item: AWS.DynamoDB.Converter.marshall(newWallet),
      };

      await ddb.putItem(createParams).promise();

      return {
        id: newWallet.id,
        balance: newWallet.balance,
        lastMintedAt: null,
        totalMinted: newWallet.totalMinted,
        totalTransferred: newWallet.totalTransferred,
        totalReceived: newWallet.totalReceived, // ✅ FIXED: Added missing totalReceived field
        createdAt: newWallet.createdAt,
      };
    }
  } catch (error) {
    const logger = createDatabaseLogger({}, 'getCentralWalletBalance', 'MVTTokenWallet');
    logError(logger, error, 'getCentralWalletBalance');
    throw new Error("Failed to retrieve central wallet balance");
  }
}

/**
 * Get user balance with recent transactions
 * @param {string} userId - User ID
 * @returns {Promise<object>} - User balance data with recent transactions
 */
async function getUserBalance(userId) {
  try {
    const userBalanceTableName = getTableName("UserMVTBalance");

    const balanceParams = {
      TableName: userBalanceTableName,
      Key: {
        id: { S: userId },
      },
    };

    const balanceResult = await ddb.getItem(balanceParams).promise();

    let userBalance;
    if (balanceResult.Item) {
      userBalance = AWS.DynamoDB.Converter.unmarshall(balanceResult.Item);
    } else {
      // Create user balance record if it doesn't exist
      const now = new Date().toISOString();
      userBalance = {
        id: userId,
        userId: userId,
        balance: 0,
        pendingBalance: 0,
        lockedBalance: 0,
        totalReceived: 0,
        totalSent: 0,
        lastUpdated: now,
        isDeleted: "false",
        createdAt: now,
        updatedAt: now,
        __typename: "UserMVTBalance",
      };

      const createParams = {
        TableName: userBalanceTableName,
        Item: AWS.DynamoDB.Converter.marshall(userBalance),
      };

      await ddb.putItem(createParams).promise();
    }

    // Get recent transactions for this user
    const recentTransactions = await getUserRecentTransactions(userId);

    return {
      userId: userBalance.userId,
      balance: userBalance.balance || 0,
      pendingBalance: userBalance.pendingBalance || 0,
      lockedBalance: userBalance.lockedBalance || 0,
      availableBalance: (userBalance.balance || 0) - (userBalance.lockedBalance || 0),
      totalReceived: userBalance.totalReceived || 0,
      totalSent: userBalance.totalSent || 0,
      lastUpdated: userBalance.lastUpdated,
      recentTransactions: recentTransactions,
    };
  } catch (error) {
    const logger = createDatabaseLogger({}, 'getUserBalance', 'UserMVTBalance', { userId });
    logError(logger, error, 'getUserBalance', { userId });

    // If it's a table not found error, return default data
    if (error.code === 'ResourceNotFoundException') {
      return {
        userId: userId,
        balance: 0,
        pendingBalance: 0,
        lockedBalance: 0,
        availableBalance: 0,
        totalReceived: 0,
        totalSent: 0,
        lastUpdated: new Date().toISOString(),
        recentTransactions: []
      };
    }

    throw new Error("Failed to retrieve user balance");
  }
}

/**
 * Get recent transactions for a user from MVTWalletTransaction table
 * @param {string} userId - User ID
 * @param {number} limit - Maximum number of transactions to return
 * @returns {Promise<Array>} - Array of recent transactions
 */
async function getUserRecentTransactions(userId, limit = 10) {
  try {
    const transactionTableName = getTableName("MVTWalletTransaction");

    // Check if table exists first
    const exists = await tableExists(ddb, transactionTableName);
    if (!exists) {
      return [];
    }

    const params = {
      TableName: transactionTableName,
      FilterExpression:
        "(fromUserId = :userId OR toUserId = :userId) AND isDeleted = :notDeleted",
      ExpressionAttributeValues: {
        ":userId": { S: userId },
        ":notDeleted": { S: "false" },
      },
      Limit: limit,
    };

    const result = await ddb.scan(params).promise();

    if (result.Items && result.Items.length > 0) {
      const transactions = result.Items.map((item) => {
        const transaction = AWS.DynamoDB.Converter.unmarshall(item);
        return {
          id: transaction.id,
          transactionType: transaction.transactionType,
          amount: transaction.amount,
          status: transaction.status,
          description: transaction.description,
          createdAt: transaction.createdAt,
          transactionHash: transaction.transactionHash,
        };
      });

      // Sort by creation date (most recent first)
      transactions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      return transactions;
    }

    return [];
  } catch (error) {
    const logger = createDatabaseLogger({}, 'getUserRecentTransactions', 'MVTWalletTransaction', { userId });
    logError(logger, error, 'getUserRecentTransactions', { userId });
    return [];
  }
}

/**
 * Update user balance
 * @param {string} userId - User ID
 * @param {number} newBalance - New balance amount
 * @param {number} newTotalReceived - New total received amount
 * @param {number} newTotalSent - New total sent amount
 * @returns {Promise<void>}
 */
async function updateUserBalance(userId, newBalance, newTotalReceived, newTotalSent) {
  const userBalanceTableName = getTableName("UserMVTBalance");
  const now = new Date().toISOString();

  const updateUserBalanceParams = {
    TableName: userBalanceTableName,
    Key: {
      id: { S: userId },
    },
    UpdateExpression: "SET balance = :balance, totalReceived = :totalReceived, totalSent = :totalSent, lastUpdated = :updated",
    ExpressionAttributeValues: {
      ":balance": { N: newBalance.toString() },
      ":totalReceived": { N: newTotalReceived.toString() },
      ":totalSent": { N: newTotalSent.toString() },
      ":updated": { S: now },
    },
  };

  await ddb.updateItem(updateUserBalanceParams).promise();
}

/**
 * Update central wallet balance
 * @param {number} newBalance - New balance amount
 * @param {number} newTotalMinted - New total minted amount
 * @param {number} newTotalTransferred - New total transferred amount
 * @param {number} newTotalReceived - New total received amount (optional)
 * @param {string} lastMintedAt - Last minted timestamp (optional)
 * @returns {Promise<void>}
 */
async function updateCentralWalletBalance(newBalance, newTotalMinted, newTotalTransferred, newTotalReceived = null, lastMintedAt = null) {
  const now = new Date().toISOString();

  // Build update expression dynamically based on provided parameters
  let updateExpression = "SET balance = :balance, totalMinted = :totalMinted, totalTransferred = :totalTransferred, updatedAt = :updatedAt";

  const expressionAttributeValues = {
    ":balance": { N: newBalance.toString() },
    ":totalMinted": { N: newTotalMinted.toString() },
    ":totalTransferred": { N: newTotalTransferred.toString() },
    ":updatedAt": { S: now },
  };

  // Add totalReceived if provided
  if (newTotalReceived !== null) {
    updateExpression += ", totalReceived = :totalReceived";
    expressionAttributeValues[":totalReceived"] = { N: newTotalReceived.toString() };
  }

  // Add lastMintedAt if provided
  if (lastMintedAt) {
    updateExpression += ", lastMintedAt = :lastMintedAt";
    expressionAttributeValues[":lastMintedAt"] = { S: lastMintedAt };
  }

  const updateWalletParams = {
    TableName: getTableName("MVTTokenWallet"),
    Key: {
      id: { S: CENTRAL_WALLET_ID },
    },
    UpdateExpression: updateExpression,
    ExpressionAttributeValues: expressionAttributeValues,
  };

  await ddb.updateItem(updateWalletParams).promise();
}

/**
 * Lock MVT tokens for a user (move from available to locked)
 * @param {string} userId - User ID
 * @param {number} amount - Amount to lock
 * @returns {Promise<boolean>} - Success status
 */
async function lockUserMVTTokens(userId, amount) {
  try {
    const userBalanceTableName = getTableName("UserMVTBalance");
    const now = new Date().toISOString();

    // First get current balance to validate
    const userBalance = await getUserBalance(userId);

    if (userBalance.availableBalance < amount) {
      throw new Error(`Insufficient available balance. Available: ${userBalance.availableBalance}, Requested: ${amount}`);
    }

    // Update user balance: increase lockedBalance (handle missing lockedBalance field)
    const updateExpression = "SET #lockedBalance = if_not_exists(#lockedBalance, :zero) + :amount, #lastUpdated = :updated";
    const expressionAttributeNames = {
      "#lockedBalance": "lockedBalance",
      "#lastUpdated": "lastUpdated"
    };
    const expressionAttributeValues = {
      ":amount": { N: amount.toString() },
      ":zero": { N: "0" },
      ":updated": { S: now }
    };

    const updateUserBalanceParams = {
      TableName: userBalanceTableName,
      Key: {
        id: { S: userId },
      },
      UpdateExpression: updateExpression,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
    };

    await ddb.updateItem(updateUserBalanceParams).promise();

    return true;
  } catch (error) {
    const logger = createDatabaseLogger({}, 'lockUserMVTTokens', 'UserMVTBalance', { userId, amount });
    logError(logger, error, 'lockUserMVTTokens', { userId, amount });
    throw new Error(`Failed to lock MVT tokens: ${error.message}`);
  }
}

/**
 * Unlock MVT tokens for a user (move from locked back to available)
 * @param {string} userId - User ID
 * @param {number} amount - Amount to unlock
 * @returns {Promise<boolean>} - Success status
 */
async function unlockUserMVTTokens(userId, amount) {
  try {
    const userBalanceTableName = getTableName("UserMVTBalance");
    const now = new Date().toISOString();

    // Update user balance: decrease lockedBalance (handle missing lockedBalance field)
    const updateExpression = "SET #lockedBalance = if_not_exists(#lockedBalance, :zero) - :amount, #lastUpdated = :updated";
    const expressionAttributeNames = {
      "#lockedBalance": "lockedBalance",
      "#lastUpdated": "lastUpdated"
    };
    const expressionAttributeValues = {
      ":amount": { N: amount.toString() },
      ":zero": { N: "0" },
      ":updated": { S: now }
    };

    const updateUserBalanceParams = {
      TableName: userBalanceTableName,
      Key: {
        id: { S: userId },
      },
      UpdateExpression: updateExpression,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
    };

    await ddb.updateItem(updateUserBalanceParams).promise();

    return true;
  } catch (error) {
    const logger = createDatabaseLogger({}, 'unlockUserMVTTokens', 'UserMVTBalance', { userId, amount });
    logError(logger, error, 'unlockUserMVTTokens', { userId, amount });
    throw new Error(`Failed to unlock MVT tokens: ${error.message}`);
  }
}

/**
 * Transfer locked MVT tokens to central wallet (for completed swaps)
 * Uses atomic DynamoDB transaction to ensure data consistency
 * @param {string} userId - User ID
 * @param {number} amount - Amount to transfer from locked balance
 * @returns {Promise<object>} - Transfer result with balance details
 */
async function transferLockedMVTToCentral(userId, amount) {
  const logger = createDatabaseLogger({}, 'transferLockedMVTToCentral', 'UserMVTBalance', { userId, amount });

  try {
    const userBalanceTableName = getTableName("UserMVTBalance");
    const centralWalletTableName = getTableName("MVTTokenWallet");
    const now = new Date().toISOString();

    // Get current balances for validation and calculation
    const userBalance = await getUserBalance(userId);
    const centralBalance = await getCentralWalletBalance();

    logger.info({
      userBalance: userBalance.balance,
      userLockedBalance: userBalance.lockedBalance,
      centralBalance: centralBalance.balance,
      centralTotalReceived: centralBalance.totalReceived, // ✅ ADDED: Log current totalReceived
      transferAmount: amount
    }, 'Starting atomic MVT transfer to central wallet');

    // Validate sufficient locked balance
    if (userBalance.lockedBalance < amount) {
      throw new Error(`Insufficient locked balance. Locked: ${userBalance.lockedBalance}, Requested: ${amount}`);
    }

    // Calculate new balances
    const newCentralBalance = centralBalance.balance + amount;
    const newCentralTotalReceived = (centralBalance.totalReceived || 0) + amount;

    // Prepare atomic transaction items
    const transactItems = [
      {
        // Update user balance: decrease lockedBalance and balance, increase totalSent
        Update: {
          TableName: userBalanceTableName,
          Key: { id: { S: userId } },
          UpdateExpression: "SET #balance = #balance - :amount, #lockedBalance = #lockedBalance - :amount, #totalSent = if_not_exists(#totalSent, :zero) + :amount, #lastUpdated = :updated",
          ConditionExpression: "#lockedBalance >= :amount AND #balance >= :amount",
          ExpressionAttributeNames: {
            "#balance": "balance",
            "#lockedBalance": "lockedBalance",
            "#totalSent": "totalSent",
            "#lastUpdated": "lastUpdated"
          },
          ExpressionAttributeValues: {
            ":amount": { N: amount.toString() },
            ":zero": { N: "0" },
            ":updated": { S: now }
          }
        }
      },
      {
        // Update central wallet: increase balance and totalReceived
        Update: {
          TableName: centralWalletTableName,
          Key: { id: { S: "central-wallet" } },
          UpdateExpression: "SET #balance = :newBalance, #totalReceived = :newTotalReceived, #updatedAt = :updated",
          ExpressionAttributeNames: {
            "#balance": "balance",
            "#totalReceived": "totalReceived",
            "#updatedAt": "updatedAt"
          },
          ExpressionAttributeValues: {
            ":newBalance": { N: newCentralBalance.toString() },
            ":newTotalReceived": { N: newCentralTotalReceived.toString() },
            ":updated": { S: now }
          }
        }
      }
    ];

    // Execute atomic transaction
    const transactParams = { TransactItems: transactItems };
    await ddb.transactWriteItems(transactParams).promise();

    logger.info({
      previousCentralBalance: centralBalance.balance,
      newCentralBalance: newCentralBalance,
      previousCentralTotalReceived: centralBalance.totalReceived, // ✅ ADDED: Log previous totalReceived
      newCentralTotalReceived: newCentralTotalReceived, // ✅ ADDED: Log new totalReceived
      transferAmount: amount,
      userId: userId
    }, 'Successfully completed atomic MVT transfer to central wallet');

    return {
      success: true,
      transferAmount: amount,
      previousCentralBalance: centralBalance.balance,
      newCentralBalance: newCentralBalance,
      previousUserBalance: userBalance.balance,
      newUserBalance: userBalance.balance - amount,
      previousUserLockedBalance: userBalance.lockedBalance,
      newUserLockedBalance: userBalance.lockedBalance - amount
    };
  } catch (error) {
    logError(logger, error, 'transferLockedMVTToCentral', { userId, amount });

    // Provide specific error messages for different failure scenarios
    if (error.code === 'TransactionCanceledException') {
      throw new Error(`Transfer failed: Insufficient locked balance or concurrent modification detected. User: ${userId}, Amount: ${amount}`);
    } else if (error.code === 'ConditionalCheckFailedException') {
      throw new Error(`Transfer failed: Balance validation failed. User may have insufficient locked tokens. User: ${userId}, Amount: ${amount}`);
    } else {
      throw new Error(`Failed to transfer locked MVT tokens: ${error.message}`);
    }
  }
}

module.exports = {
  getCentralWalletBalance,
  getUserBalance,
  getUserRecentTransactions,
  updateCentralWalletBalance,
  updateUserBalance,
  lockUserMVTTokens,
  unlockUserMVTTokens,
  transferLockedMVTToCentral
};
