/* Amplify Params - DO NOT EDIT
	API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT
	API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT
	AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID
	ENV
	REGION
Amplify Params - DO NOT EDIT */

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

const AWS = require("aws-sdk");
const uuid = require("uuid");
const R = require("ramda");

AWS.config.update({
  maxRetries: 3,
  httpOptions: { timeout: 30000, connectTimeout: 5000 },
  region: process.env.REGION,
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY,
});

const ddb = new AWS.DynamoDB();
const sns = new AWS.SNS();

exports.handler = async (event, context, callback) => {
  try {
    console.log("event", event);
    const {
      title = "",
      body = "",
      taskNotificationsId = "null",
      feedbackId = "",
      notificationType = "",
      notificationIcon = "",
      isAnswerable = false,
      userList = [],
      MVPTokens = "0",
      points = "0",
      feedbackDate = "",
      homeworkId = "",
      homeworkDueDate = "",
    } = event.arguments?.input || {};

    const TableName = `Notifications-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
    const date = new Date();
    let payload = {};

    if (notificationType && notificationType === "forceLogOut") {
      payload = {
        GCM: {
          notification: {
            content_available: true,
          },
          data: {
            body: body,
            title: title,
          },
        },
      };
    } else {
      payload = {
        GCM: {
          notification: {
            body: body,
            title: title,
          },
        },
      };
    }

    payload.GCM = JSON.stringify(payload.GCM);
    payload = JSON.stringify(payload);
    console.log("payload", payload);

    const sendPushPromises = userList.map(async (rec) => {
      if (rec.id && rec.endpointArn && rec.isLogin && rec.isLogin === true) {
        const updateARNParams = {
          Attributes: {
            Enabled: "true",
          },
          EndpointArn: rec.endpointArn,
        };
        await sns.setEndpointAttributes(updateARNParams).promise();

        const publishData = {
          Message: payload,
          MessageStructure: "json",
          TargetArn: rec.endpointArn,
        };
        await sns.publish(publishData).promise();
      }
    });

    await Promise.all(sendPushPromises);

    const batchWritePromises = R.splitEvery(24, userList).map(
      async (segment) => {
        const notificationData = [];
        for (let i = 0; i < segment.length; i++) {
          const rec = segment[i];
          if (rec.id && notificationType !== "forceLogOut") {
            const notificationId = uuid.v4();
            const item = {
              id: { S: notificationId },
              __typename: { S: "Notifications" },
              title: { S: title ?? "" },
              description: { S: body ?? "" },
              userId: { S: rec.id ?? "" },
              imageUrl: { S: "" },
              taskNotificationsId: { S: taskNotificationsId ?? "null" },
              feedbackId: { S: feedbackId ?? "" },
              notificationType: { S: notificationType ?? "" },
              notificationIcon: { S: notificationIcon ?? "" },
              MVPTokens: { N: MVPTokens.toString() },
              points: { N: points.toString() },
              homeworkId: { S: homeworkId ?? "" },
              homeworkDueDate: { S: homeworkDueDate ?? "" },
              isAnswerable: { BOOL: isAnswerable ?? false },
              isRead: { BOOL: false },
              isDeleted: { S: "false" },
              createdAt: { S: date.toISOString() },
              updatedAt: { S: date.toISOString() },
              _version: { N: "1" },
              _lastChangedAt: { N: date.getTime().toString() },
            };

            if (feedbackDate && Object.hasOwnProperty.call(feedbackDate, i)) {
              item.feedbackDate = { S: feedbackDate };
            }

            notificationData.push({
              PutRequest: {
                Item: item,
              },
            });
          }
        }

        const notificationParams = {
          RequestItems: {
            [TableName]: notificationData,
          },
        };

        try {
          let response = await ddb.batchWriteItem(notificationParams).promise();
          while (!R.isEmpty(response.UnprocessedItems)) {
            const count = response.UnprocessedItems[TableName].length;
            console.log(`${count} unprocessed item(s) left, retrying...`);
            const unprocessedParams = {
              RequestItems: response.UnprocessedItems,
            };
            response = await ddb.batchWriteItem(unprocessedParams).promise();
          }
        } catch (error) {
          console.log(error, error.stack);
        }
      }
    );

    await Promise.all(batchWritePromises);

    callback(null, { message: "Message sent successfully", statusCode: 200 });
  } catch (error) {
    const errorRes = {
      isSuccess: false,
      statusCode: error.statusCode ? error.statusCode : 400,
      message: error.message,
    };
    callback(errorRes);
  }
};
