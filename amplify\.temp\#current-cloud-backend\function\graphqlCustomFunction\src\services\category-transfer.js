const AWS = require('aws-sdk');

const dynamoDb = new AWS.DynamoDB();
const EnvironmentName = `${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
const CategoryTable = `Categories-${EnvironmentName}`;
const KnowledgeRepositoryStoreTable = `KnowledgeRepositoryStore-${EnvironmentName}`;
const KnowledgeRepositoryStoreSummaryTable = `KnowledgeRepositoryStoreSummary-${EnvironmentName}`;
const ProgramsTable = `Programs-${EnvironmentName}`;
const PointsTable = `Points-${EnvironmentName}`;

async function transferCategoryData(fromCategoryId, toCategoryId) {
  console.log(`Transferring data from category ${fromCategoryId} to ${toCategoryId}`);

  const fromCategoryParentId = await getCategoryParentId(fromCategoryId);
  const toCategoryParentId = await getCategoryParentId(toCategoryId);

  if (fromCategoryParentId && toCategoryParentId) {
    console.log(`Both ${fromCategoryId} and ${toCategoryId} are subcategories. Transferring subcategory data...`);
    
    // Transfer subcategory data
    await transferEntityDataForSubCategory(KnowledgeRepositoryStoreTable, fromCategoryId, toCategoryId, updateKnowledgeRepositoryStoreCategoryId);
    await transferEntityDataForSubCategory(KnowledgeRepositoryStoreSummaryTable, fromCategoryId, toCategoryId, updateKnowledgeRepositoryStoreSummaryCategoryId);
  } else {
    console.log(`Transferring data as regular category...`);
    
    // Transfer regular category data
    await transferEntityData(KnowledgeRepositoryStoreTable, fromCategoryId, toCategoryId, updateKnowledgeRepositoryStoreCategoryId);
    await transferEntityData(KnowledgeRepositoryStoreSummaryTable, fromCategoryId, toCategoryId, updateKnowledgeRepositoryStoreSummaryCategoryId);
    await transferEntityDataTable(ProgramsTable, fromCategoryId, toCategoryId, updateProgramsCategoryId);
    await transferEntityDataTable(PointsTable, fromCategoryId, toCategoryId, updatePointsCategoryId);
    
    // Transfer subcategories linked to the parentId
    await transferSubCategories(fromCategoryId, toCategoryId);
  }

  await deleteCategory(fromCategoryId);
}


async function transferSubCategories(fromCategoryId, toCategoryId) {
  console.log(`Starting transfer of sub categories from category ${fromCategoryId} to ${toCategoryId}`);
  const params = {
    TableName: CategoryTable,
    FilterExpression: 'parentId = :parentId',
    ExpressionAttributeValues: {
      ':parentId': { S: fromCategoryId },
    },
  };
  try {
    const result = await dynamoDb.scan(params).promise();
    const items = result.Items;
    console.log('Found sub category length: ', items.length);
    if (items.length === 0) {
      console.log(`No sub categories found in category ${fromCategoryId}`);
    } else {
      const promises = items.map(async (item) => {
        const updateParams = {
          TableName: CategoryTable,
          Key: { id: { S: item.id.S } },
          UpdateExpression: 'SET parentId = :toCategoryId',
          ExpressionAttributeValues: {
            ':toCategoryId': { S: toCategoryId },
            ':fromCategoryId': { S: fromCategoryId },
          },
          ReturnValues: 'UPDATED_NEW',
          ConditionExpression: 'attribute_exists(id) AND parentId = :fromCategoryId',
        };
        try {
          await dynamoDb.updateItem(updateParams).promise();
          console.log(`Successfully transferred sub category ${item.id.S} to category ${toCategoryId}`);
        } catch (error) {
          console.error(`Error transferring sub category ${item.id.S} to category ${toCategoryId}: ${error.message}`);
        }
      });
      await Promise.all(promises);
    }
  } catch (error) {
    if (error.code === 'ResourceNotFoundException') {
      console.log(`Category ${fromCategoryId} does not exist`);
    } else {
      console.error(`Error transferring sub categories: ${error.message}`);
    }
  }
}

async function transferEntityData(tableName, fromCategoryId, toCategoryId, updateFunction) {
  console.log(`Transferring entities in table ${tableName} from category ${fromCategoryId} to ${toCategoryId}`);
  const params = {
    TableName: tableName,
    FilterExpression: 'categoryId = :categoryId',
    ExpressionAttributeValues: {
      ':categoryId': { S: fromCategoryId },
    },
  };

  const data = await dynamoDb.scan(params).promise();
  const items = data.Items;

  console.log(`Found ${items.length} items to transfer`);

  await Promise.all(items.map(async (item) => {
    await updateFunction(item.id.S, toCategoryId);
  }));
}

async function transferEntityDataTable(tableName, fromCategoryId, toCategoryId, updateFunction) {
  console.log(`Transferring entities in table ${tableName} from category ${fromCategoryId} to ${toCategoryId}`);
  const params = {
    TableName: tableName,
    FilterExpression: 'categoryID = :categoryID',
    ExpressionAttributeValues: {
      ':categoryID': { S: fromCategoryId },
    },
  };

  const data = await dynamoDb.scan(params).promise();
  const items = data.Items;

  console.log(`Found ${items.length} items to transfer`);

  await Promise.all(items.map(async (item) => {
    await updateFunction(item.id.S, toCategoryId);
  }));
}

async function transferEntityDataForSubCategory(tableName, fromCategoryId, toCategoryId) {
  console.log(`Transferring subcategory data in table ${tableName} from ${fromCategoryId} to ${toCategoryId}`);

  const params = {
    TableName: tableName,
    FilterExpression: 'contains(subCategoryIds, :fromCategoryId)',
    ExpressionAttributeValues: {
      ':fromCategoryId': { S: fromCategoryId }
    }
  };

  const data = await dynamoDb.scan(params).promise();
  const items = data.Items;

  console.log(`Found ${items.length} items to transfer`);

  await Promise.all(items.map(async (item) => {
    // Remove fromCategoryId and add toCategoryId in subCategoryIds
    await updateSubCategoryIds(item.id.S, fromCategoryId, toCategoryId);
  }));
}

async function updateSubCategoryIds(knowledgeRepositoryStoreId, fromCategoryId, toCategoryId) {
  console.log(`Updating subCategoryIds for ${knowledgeRepositoryStoreId} - Removing: ${fromCategoryId}, Adding: ${toCategoryId}`);
  
  const getParams = {
    TableName: KnowledgeRepositoryStoreTable,
    Key: { id: { S: knowledgeRepositoryStoreId } }
  };

  try {
    const result = await dynamoDb.getItem(getParams).promise();
    if (!result.Item) {
      console.warn(`Item with id ${knowledgeRepositoryStoreId} not found`);
      return;
    }

    const existingSubCategoryIds = result.Item.subCategoryIds ? result.Item.subCategoryIds.L.map(item => item.S) : [];
    const updatedSubCategoryIds = existingSubCategoryIds.filter(id => id !== fromCategoryId);

    // Add toCategoryId only if it's not already present
    if (!updatedSubCategoryIds.includes(toCategoryId)) {
      updatedSubCategoryIds.push(toCategoryId);
    }

    const updateParams = {
      TableName: KnowledgeRepositoryStoreTable,
      Key: { id: { S: knowledgeRepositoryStoreId } },
      UpdateExpression: 'SET subCategoryIds = :subCategoryIds',
      ExpressionAttributeValues: {
        ':subCategoryIds': { L: updatedSubCategoryIds.map(id => ({ S: id })) }
      },
      ReturnValues: 'UPDATED_NEW'
    };

    await dynamoDb.updateItem(updateParams).promise();
    console.log(`Updated subCategoryIds for item ${knowledgeRepositoryStoreId}`);
  } catch (error) {
    console.error(`Error updating subCategoryIds for item ${knowledgeRepositoryStoreId}: ${error.message}`);
  }
}

async function getCategoryParentId(categoryId) {
  const params = {
    TableName: CategoryTable,
    Key: { id: { S: categoryId } },
  };

  try {
    const result = await dynamoDb.getItem(params).promise();
    return result.Item.parentId?.S;
  } catch (error) {
    console.error(`Error getting category ${categoryId} parent id: ${error.message}`);
    return null;
  }
}

async function updateKnowledgeRepositoryStoreCategoryId(knowledgeRepositoryStoreId, categoryId) {
  console.log(`Updating KnowledgeRepositoryStore item ${knowledgeRepositoryStoreId} to category ${categoryId}`);
  const updateParams = {
    TableName: KnowledgeRepositoryStoreTable,
    Key: { id: { S: knowledgeRepositoryStoreId } },
    UpdateExpression: 'SET categoryId = :categoryId',
    ExpressionAttributeValues: {
      ':categoryId': { S: categoryId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updateKnowledgeRepositoryStoreSummaryCategoryId(knowledgeRepositoryStoreSummaryId, categoryId) {
  console.log(`Updating KnowledgeRepositoryStoreSummary item ${knowledgeRepositoryStoreSummaryId} to category ${categoryId}`);
  const updateParams = {
    TableName: KnowledgeRepositoryStoreSummaryTable,
    Key: { id: { S: knowledgeRepositoryStoreSummaryId } },
    UpdateExpression: 'SET categoryId = :categoryId',
    ExpressionAttributeValues: {
      ':categoryId': { S: categoryId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updateProgramsCategoryId(knowledgeRepositoryStoreSummaryId, categoryId) {
  console.log(`Updating Programs item ${knowledgeRepositoryStoreSummaryId} to category ${categoryId}`);
  const updateParams = {
    TableName: ProgramsTable,
    Key: { id: { S: knowledgeRepositoryStoreSummaryId } },
    UpdateExpression: 'SET categoryID = :categoryID',
    ExpressionAttributeValues: {
      ':categoryID': { S: categoryId },
    },
    ReturnValues: 'UPDATED_NEW',
  };
  
  console.log('updateProgramsCategoryId params: ', updateParams);
  await dynamoDb.updateItem(updateParams).promise();
}

async function updatePointsCategoryId(knowledgeRepositoryStoreSummaryId, categoryId) {
  console.log(`Updating Points item ${knowledgeRepositoryStoreSummaryId} to category ${categoryId}`);
  const updateParams = {
    TableName: PointsTable,
    Key: { id: { S: knowledgeRepositoryStoreSummaryId } },
    UpdateExpression: 'SET categoryID = :categoryID',
    ExpressionAttributeValues: {
      ':categoryID': { S: categoryId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function deleteCategory(categoryId) {
  console.log(`Attempting to delete category with ID: ${categoryId}`);

  const params = {
    TableName: CategoryTable,
    Key: { id: { S: categoryId } },
  };

  try {
    await dynamoDb.deleteItem(params).promise();
    console.log(`Successfully deleted category with ID: ${categoryId}`);
  } catch (error) {
    console.error(`Failed to delete category with ID: ${categoryId}`, error);
  }
}

module.exports = {
  transferCategoryData
}
