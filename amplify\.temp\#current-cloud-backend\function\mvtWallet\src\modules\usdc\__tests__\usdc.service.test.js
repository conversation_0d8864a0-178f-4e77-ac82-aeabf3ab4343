/**
 * USDC Service Test Suite
 * Tests for USDC business logic, blockchain contract interactions, and liquidity pool operations
 */

const usdcService = require('../usdc.service');
const contractService = require('../../../shared/blockchain/contractService');
const validationUtils = require('../../../shared/utils/validationUtils');
const { mockDynamoDB } = require('../../../__tests__/setup');

// Mock dependencies
jest.mock('../../../config/aws', () => ({
  AWS: {
    DynamoDB: {
      Converter: {
        marshall: jest.fn((item) => item),
        unmarshall: jest.fn((item) => item)
      }
    }
  },
  ddb: mockDynamoDB
}));

jest.mock('../../../shared/blockchain/contractService');
jest.mock('../../../shared/utils/validationUtils');
jest.mock('../../../shared/database/dynamoUtils', () => ({
  getTableName: jest.fn((tableName) => `test-${tableName}`),
  tableExists: jest.fn().mockResolvedValue(true)
}));

describe('USDC Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getUSDCLiquidityPool', () => {
    test('should retrieve liquidity pool data successfully', async () => {
      // Arrange
      contractService.getContractUSDCBalance.mockResolvedValue('10000500000'); // 10000.50 USDC (6 decimals)
      contractService.getWalletAddress.mockReturnValue('******************************************');

      // Act
      const result = await usdcService.getUSDCLiquidityPool();

      // Assert
      expect(contractService.getContractUSDCBalance).toHaveBeenCalled();
      expect(result).toMatchObject({
        totalReserves: 10000.50,
        availableBalance: 10000.50,
        adminWalletAddress: '******************************************',
        status: 'AVAILABLE',
        note: 'Connected to blockchain contract'
      });
      expect(result.lastUpdated).toBeDefined();
    });

    test('should handle zero balance scenario', async () => {
      // Arrange
      contractService.getContractUSDCBalance.mockResolvedValue('0');
      contractService.getWalletAddress.mockReturnValue('******************************************');

      // Act
      const result = await usdcService.getUSDCLiquidityPool();

      // Assert
      expect(result).toMatchObject({
        totalReserves: 0,
        availableBalance: 0,
        status: 'LOW_LIQUIDITY',
        note: 'Contract balance is 0 or unavailable'
      });
    });

    test('should handle blockchain connection errors', async () => {
      // Arrange
      contractService.getContractUSDCBalance.mockRejectedValue(new Error('Network error'));

      // Act & Assert
      await expect(usdcService.getUSDCLiquidityPool()).rejects.toThrow('Failed to retrieve USDC liquidity pool data');
    });

    test('should handle contract call failures', async () => {
      // Arrange
      contractService.getContractUSDCBalance.mockRejectedValue(new Error('Contract call failed'));

      // Act & Assert
      await expect(usdcService.getUSDCLiquidityPool()).rejects.toThrow('Failed to retrieve USDC liquidity pool data');
    });

    test('should handle invalid balance responses', async () => {
      // Arrange
      contractService.getContractUSDCBalance.mockResolvedValue('invalid');
      contractService.getWalletAddress.mockReturnValue('******************************************');

      // Act
      const result = await usdcService.getUSDCLiquidityPool();

      // Assert
      expect(result.totalReserves).toBe(0);
      expect(result.status).toBe('LOW_LIQUIDITY');
    });
  });

  describe('depositUSDCToPool', () => {
    const amount = 1000.50;
    const adminUserId = 'admin-123';
    const description = 'Test deposit';

    test('should deposit USDC successfully', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
      contractService.isBlockchainConnected.mockReturnValue(true);
      
      const mockCurrentPool = {
        totalReserves: 5000.00,
        availableBalance: 5000.00,
        status: 'AVAILABLE'
      };
      
      const mockDepositResult = {
        hash: '0xabcdef123456',
        blockNumber: 12345,
        gasUsed: '21000',
        status: 1
      };
      
      // Mock the service's own method call
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue(mockCurrentPool);
      contractService.depositUSDCToContract.mockResolvedValue(mockDepositResult);
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const result = await usdcService.depositUSDCToPool(amount, adminUserId, description);

      // Assert
      expect(validationUtils.validateUSDCAmount).toHaveBeenCalledWith(amount);
      expect(contractService.isBlockchainConnected).toHaveBeenCalled();
      expect(contractService.depositUSDCToContract).toHaveBeenCalledWith(amount);
      expect(result).toMatchObject({
        transactionType: 'USDC_DEPOSIT',
        amount: amount,
        status: 'COMPLETED',
        transactionHash: '0xabcdef123456',
        blockNumber: 12345,
        gasUsed: '21000',
        adminUserId: adminUserId
      });
    });

    test('should validate USDC amount', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({
        isValid: false,
        error: 'Amount must be a positive number'
      });

      // Act & Assert
      await expect(
        usdcService.depositUSDCToPool(amount, adminUserId, description)
      ).rejects.toThrow('Amount must be a positive number');
    });

    test('should check blockchain connection', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
      contractService.isBlockchainConnected.mockReturnValue(false);

      // Act & Assert
      await expect(
        usdcService.depositUSDCToPool(amount, adminUserId, description)
      ).rejects.toThrow('Blockchain connection not available');
    });

    test('should handle contract deposit failures', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
      contractService.isBlockchainConnected.mockReturnValue(true);
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue({ status: 'AVAILABLE' });
      contractService.depositUSDCToContract.mockRejectedValue(new Error('Transaction failed'));

      // Act & Assert
      await expect(
        usdcService.depositUSDCToPool(amount, adminUserId, description)
      ).rejects.toThrow('Failed to deposit USDC to liquidity pool');
    });

    test('should handle gas estimation failures', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
      contractService.isBlockchainConnected.mockReturnValue(true);
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue({ status: 'AVAILABLE' });
      contractService.depositUSDCToContract.mockRejectedValue(new Error('Gas estimation failed'));

      // Act & Assert
      await expect(
        usdcService.depositUSDCToPool(amount, adminUserId, description)
      ).rejects.toThrow('Failed to deposit USDC to liquidity pool');
    });

    test('should create transaction record even if logging fails', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
      contractService.isBlockchainConnected.mockReturnValue(true);
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue({ status: 'AVAILABLE' });
      
      const mockDepositResult = {
        hash: '0xabcdef123456',
        blockNumber: 12345,
        gasUsed: '21000',
        status: 1
      };
      
      contractService.depositUSDCToContract.mockResolvedValue(mockDepositResult);
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBError(new Error('DynamoDB error')));

      // Act
      const result = await usdcService.depositUSDCToPool(amount, adminUserId, description);

      // Assert
      expect(result.status).toBe('COMPLETED');
      expect(result.transactionHash).toBe('0xabcdef123456');
    });
  });

  describe('withdrawUSDCFromPool', () => {
    const amount = 500.25;
    const adminUserId = 'admin-123';
    const description = 'Test withdrawal';

    test('should withdraw USDC successfully', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
      contractService.isBlockchainConnected.mockReturnValue(true);
      
      const mockCurrentPool = {
        totalReserves: 5000.00,
        availableBalance: 5000.00,
        status: 'AVAILABLE'
      };
      
      const mockWithdrawResult = {
        hash: '0xfedcba654321',
        blockNumber: 12346,
        gasUsed: '25000',
        status: 1
      };
      
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue(mockCurrentPool);
      contractService.withdrawUSDCFromContract.mockResolvedValue(mockWithdrawResult);
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const result = await usdcService.withdrawUSDCFromPool(amount, adminUserId, description);

      // Assert
      expect(contractService.withdrawUSDCFromContract).toHaveBeenCalledWith(amount);
      expect(result).toMatchObject({
        transactionType: 'USDC_WITHDRAWAL',
        amount: amount,
        status: 'COMPLETED',
        transactionHash: '0xfedcba654321',
        adminUserId: adminUserId
      });
    });

    test('should validate withdrawal amount', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({
        isValid: false,
        error: 'Amount must be positive'
      });

      // Act & Assert
      await expect(
        usdcService.withdrawUSDCFromPool(amount, adminUserId, description)
      ).rejects.toThrow('Amount must be positive');
    });

    test('should check sufficient liquidity', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
      contractService.isBlockchainConnected.mockReturnValue(true);
      
      const mockCurrentPool = {
        totalReserves: 100.00,
        availableBalance: 100.00,
        status: 'LOW_LIQUIDITY'
      };
      
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue(mockCurrentPool);

      // Act & Assert
      await expect(
        usdcService.withdrawUSDCFromPool(500.25, adminUserId, description)
      ).rejects.toThrow('Insufficient liquidity pool balance');
    });

    test('should handle contract withdrawal failures', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
      contractService.isBlockchainConnected.mockReturnValue(true);
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue({
        availableBalance: 1000.00,
        status: 'AVAILABLE'
      });
      contractService.withdrawUSDCFromContract.mockRejectedValue(new Error('Withdrawal failed'));

      // Act & Assert
      await expect(
        usdcService.withdrawUSDCFromPool(amount, adminUserId, description)
      ).rejects.toThrow('Failed to withdraw USDC from liquidity pool');
    });

    test('should handle insufficient contract balance', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
      contractService.isBlockchainConnected.mockReturnValue(true);
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue({
        availableBalance: 1000.00,
        status: 'AVAILABLE'
      });
      contractService.withdrawUSDCFromContract.mockRejectedValue(new Error('ERC20: transfer amount exceeds balance'));

      // Act & Assert
      await expect(
        usdcService.withdrawUSDCFromPool(amount, adminUserId, description)
      ).rejects.toThrow('Failed to withdraw USDC from liquidity pool');
    });
  });

  describe('Blockchain Integration', () => {
    test('should handle network congestion', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
      contractService.isBlockchainConnected.mockReturnValue(true);
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue({ status: 'AVAILABLE' });
      contractService.depositUSDCToContract.mockRejectedValue(new Error('replacement transaction underpriced'));

      // Act & Assert
      await expect(
        usdcService.depositUSDCToPool(1000, 'admin-123', 'test')
      ).rejects.toThrow('Failed to deposit USDC to liquidity pool');
    });

    test('should handle contract revert errors', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
      contractService.isBlockchainConnected.mockReturnValue(true);
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue({
        availableBalance: 1000.00,
        status: 'AVAILABLE'
      });
      contractService.withdrawUSDCFromContract.mockRejectedValue(new Error('execution reverted'));

      // Act & Assert
      await expect(
        usdcService.withdrawUSDCFromPool(500, 'admin-123', 'test')
      ).rejects.toThrow('Failed to withdraw USDC from liquidity pool');
    });

    test('should handle timeout errors', async () => {
      // Arrange
      contractService.getContractUSDCBalance.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('timeout')), 100)
        )
      );

      // Act & Assert
      await expect(usdcService.getUSDCLiquidityPool()).rejects.toThrow('Failed to retrieve USDC liquidity pool data');
    });
  });

  describe('Transaction Logging', () => {
    test('should log successful deposits to DynamoDB', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
      contractService.isBlockchainConnected.mockReturnValue(true);
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue({ status: 'AVAILABLE' });
      
      const mockDepositResult = {
        hash: '0xabcdef123456',
        blockNumber: 12345,
        gasUsed: '21000',
        status: 1
      };
      
      contractService.depositUSDCToContract.mockResolvedValue(mockDepositResult);
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      await usdcService.depositUSDCToPool(1000, 'admin-123', 'test deposit');

      // Assert
      expect(mockDynamoDB.putItem).toHaveBeenCalledWith(
        expect.objectContaining({
          TableName: 'test-MVTWalletTransaction',
          Item: expect.objectContaining({
            transactionType: 'USDC_DEPOSIT',
            amount: 1000,
            transactionHash: '0xabcdef123456',
            blockNumber: 12345
          })
        })
      );
    });

    test('should continue operation even if transaction logging fails', async () => {
      // Arrange
      validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
      contractService.isBlockchainConnected.mockReturnValue(true);
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue({ status: 'AVAILABLE' });
      
      contractService.depositUSDCToContract.mockResolvedValue({
        hash: '0xabcdef123456',
        status: 1
      });
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBError(new Error('Logging failed')));

      // Act
      const result = await usdcService.depositUSDCToPool(1000, 'admin-123', 'test');

      // Assert
      expect(result.status).toBe('COMPLETED');
      expect(result.transactionHash).toBe('0xabcdef123456');
    });
  });

  describe('Error Recovery', () => {
    test('should provide meaningful error messages for common failures', async () => {
      // Test various error scenarios
      const testCases = [
        {
          error: new Error('insufficient funds'),
          expectedMessage: 'Failed to deposit USDC to liquidity pool'
        },
        {
          error: new Error('nonce too low'),
          expectedMessage: 'Failed to deposit USDC to liquidity pool'
        },
        {
          error: new Error('gas limit exceeded'),
          expectedMessage: 'Failed to deposit USDC to liquidity pool'
        }
      ];

      for (const testCase of testCases) {
        // Arrange
        validationUtils.validateUSDCAmount.mockReturnValue({ isValid: true });
        contractService.isBlockchainConnected.mockReturnValue(true);
        jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue({ status: 'AVAILABLE' });
        contractService.depositUSDCToContract.mockRejectedValue(testCase.error);

        // Act & Assert
        await expect(
          usdcService.depositUSDCToPool(1000, 'admin-123', 'test')
        ).rejects.toThrow(testCase.expectedMessage);
      }
    });
  });
});
