# MVT Wallet Lambda Function - Documentation

This document provides comprehensive documentation for the MVT Wallet Lambda function, including architecture, API reference, and testing guides.

## 📋 **Table of Contents**

1. [🏗️ Architecture Overview](#-architecture-overview)
2. [🔄 Routing Architecture](#-routing-architecture)
3. [🔢 Data Type Validation](#-data-type-validation)
4. [📁 File Structure](#-file-structure)
5. [📋 Complete API Reference](#-complete-api-reference)
6. [🧪 Testing Documentation](#-testing-documentation)
7. [🚨 Race Condition Prevention](#-race-condition-prevention)
8. [🚀 Deployment](#-deployment)

## 🏗️ Architecture Overview

### Hybrid Architecture Summary

The MVT Wallet system implements a **hybrid architecture** combining local operations with blockchain functionality:

- **MVT Operations**: Handled locally (not on blockchain) for speed and cost efficiency
- **USDC Operations**: Use blockchain contracts for security and transparency
- **Exchange Rates**: Calculated locally using dynamic algorithms based on local MVT supply and blockchain USDC reserves
- **Swap Requests**: Stored locally in DynamoDB following mvtToken patterns

### Architecture Principles

#### 1. Clean Code Architecture
- **Single Responsibility**: Each file has one clear purpose
- **Separation of Concerns**: Business logic separated from handlers
- **DRY Principle**: Common patterns extracted into utilities
- **KISS Principle**: Simple, maintainable code structure

#### 2. Error Handling Strategy
- **Centralized Error Processing**: Consistent error responses
- **User-Friendly Messages**: Clear explanations instead of technical jargon
- **Automatic Recovery**: Re-locking mechanisms for common issues
- **Comprehensive Logging**: Detailed diagnostics for debugging

#### 3. Race Condition Prevention
- **Atomic Transactions**: DynamoDB TransactWrite for multi-table operations
- **Optimistic Locking**: Version control with retry mechanisms
- **State Machine Pattern**: Formal state transitions with validation
- **Idempotency**: Duplicate operation prevention

### Data Flow Architecture

#### Swap Request Lifecycle
```
1. User Request → 2. Validation → 3. Atomic Creation → 4. Admin Review → 5. Processing → 6. Completion
     ↓               ↓               ↓                  ↓               ↓              ↓
   Handler      Validation      Lock Tokens +      State Machine   Transfer +     Update Status +
   Routing      Utils           Create Request     Validation      Blockchain     Unlock/Transfer
```

#### Balance Management Flow
```
User Balance = Available Balance + Locked Balance
                      ↓                    ↓
                 Normal Operations    Swap Requests
                      ↓                    ↓
                 Transfers, Mints    Pending Swaps
```

#### Authorization Flow
```
Request → Extract User ID → Check Role → Validate Access → Execute Operation
   ↓           ↓              ↓            ↓               ↓
GraphQL    Cognito JWT    Admin/User   Target Resource  Business Logic
Event      Parsing        Check        Authorization    Execution
```

### Security Architecture

#### Authentication Layers
1. **Cognito JWT Validation**: AWS Cognito token verification
2. **Role-Based Access**: Admin vs User permissions
3. **Resource-Level Authorization**: User can only access own data
4. **Operation-Level Validation**: Business rule enforcement

#### Data Protection
- **Input Validation**: All inputs validated before processing
- **SQL Injection Prevention**: Parameterized DynamoDB queries
- **Rate Limiting**: Built-in AWS Lambda concurrency limits
- **Audit Trail**: Complete transaction logging

### Database Architecture

#### Table Relationships
```
User ←→ UserMVTBalance ←→ MVTWalletTransaction
  ↓           ↓                    ↓
  └→ MVTSwapRequest ←→ Transaction Records
            ↓
    USDCLiquidityPool
```

#### Consistency Model
- **Eventual Consistency**: DynamoDB default for reads
- **Strong Consistency**: Atomic transactions for critical operations
- **Version Control**: Optimistic locking for concurrent updates
- **State Validation**: Business rule enforcement at database level

### Performance Architecture

#### Optimization Strategies
1. **Modular Loading**: Only load required handler modules
2. **Connection Pooling**: Reuse DynamoDB connections
3. **Caching**: User balance caching where appropriate
4. **Batch Operations**: Group related database operations

#### Scalability Features
- **Stateless Design**: No server-side session storage
- **Auto-scaling**: AWS Lambda automatic scaling
- **Database Partitioning**: DynamoDB partition key optimization
- **Async Processing**: Non-blocking operations where possible

## 🔄 Routing Architecture

### Request Flow

The MVT Wallet Lambda function uses a modular resolver architecture:

```
GraphQL Request
       ↓
┌─────────────────┐
│   index.js      │ ← Main Lambda Handler
└─────────────────┘
       ↓
┌─────────────────┐
│   resolver.js   │ ← Modular Resolver (Main Router)
└─────────────────┘
       ↓
┌─────────────────┐
│   Field Router  │
└─────────────────┘
       ↓
┌─────────────────┐
│ Module Handlers │
└─────────────────┘
```

### Field Routing

**Wallet Module:**
- `getAdminMVTWalletBalance` → `modules/wallet/wallet.handlers.js`
- `getUserMVTWalletBalance` → `modules/wallet/wallet.handlers.js`

**Transaction Module:**
- `adminMintMVT` → `modules/transaction/transaction.handlers.js`
- `adminTransferMVT` → `modules/transaction/transaction.handlers.js`
- `userTransferMVT` → `modules/transaction/transaction.handlers.js`
- `getMVTWalletTransactionList` → `modules/transaction/transaction.handlers.js`
- `getMVTWalletTransactionById` → `modules/transaction/transaction.handlers.js`

**USDC Operations:**
- `getUSDCLiquidityPool` → `handlers/usdcHandlers.js`
- `adminDepositUSDC` → `handlers/usdcHandlers.js`
- `adminWithdrawUSDC` → `handlers/usdcHandlers.js`

**Exchange Rate Operations:**
- `getMVTWalletExchangeRate` → `handlers/exchangeRateHandlers.js`

**Swap Operations:**
- `getMVTWalletSwapRequests` → `handlers/swapHandlers.js`
- `requestMVTWalletSwap` → `handlers/swapHandlers.js`
- `approveMVTWalletSwap` → `handlers/swapHandlers.js`
- `rejectMVTWalletSwap` → `handlers/swapHandlers.js`

## 🔢 Data Type Validation

### Strict Data Type Requirements

#### MVT Token Amounts - Integer Only
- ✅ **Requirement**: All MVT token amounts must be integers (whole numbers)
- ❌ **Rejected**: Decimal amounts (1.5, 2.7, 100.25)
- ✅ **Accepted**: Integer amounts (1, 2, 100, 500)
- 🚨 **Error Message**: "MVT amount must be a whole number (integer). Decimal amounts are not allowed."

#### USDC Amounts - Float Allowed
- ✅ **Requirement**: USDC amounts can be floats for fractional values
- ✅ **Accepted**: Decimal amounts (0.5, 1.25, 100.75)
- ✅ **Accepted**: Integer amounts (1, 2, 100)

#### Validation Coverage
- ✅ Input validation in `validateSwapRequestInput()`
- ✅ Swap service validation in `createSwapRequest()`
- ✅ Exchange rate calculations
- ✅ Transaction service validation
- ✅ Mint and transfer operations
- ✅ GraphQL response handling

#### Validation Functions
- `validateMVTAmount(amount)` - Ensures MVT amounts are positive integers
- `validateUSDCAmount(amount)` - Allows USDC amounts as positive floats
- `validateSwapRequestInput(input)` - Validates swap request with integer MVT amounts
- `validateMintInput(input)` - Validates mint operations with integer MVT amounts
- `validateTransferInput(input)` - Validates transfer operations with integer MVT amounts

#### Error Messages
- **MVT Decimal Error**: "MVT amount must be a whole number (integer). Decimal amounts are not allowed."
- **MVT Negative Error**: "MVT amount must be greater than 0"
- **USDC Error**: "USDC amount must be a positive number greater than 0"

## 📁 File Structure

### Current Architecture

```
amplify/backend/function/mvtWallet/src/
├── index.js                              # Main handler (28 lines - 95% reduction)
├── resolver.js                           # New modular resolver (MAIN ROUTER)
├── modules/                              # Domain-specific modules
│   ├── wallet/                          # Wallet balance operations
│   │   ├── wallet.handlers.js
│   │   ├── wallet.service.js
│   │   ├── wallet.validation.js
│   │   ├── wallet.resolvers.js
│   │   └── __tests__/
│   └── transaction/                     # Transaction operations
│       ├── transaction.handlers.js
│       ├── transaction.service.js
│       ├── transaction.validation.js
│       ├── transaction.resolvers.js
│       └── __tests__/
├── handlers/                            # Legacy handlers (still functional)
│   ├── index.js                        # Handler router (bypassed)
│   ├── usdcHandlers.js                 # USDC liquidity operations
│   ├── exchangeRateHandlers.js         # Exchange rate operations
│   └── swapHandlers.js                 # Swap request operations
├── shared/                              # Shared utilities and services
│   ├── services/
│   │   ├── authService.js              # Authentication & authorization
│   │   └── blockchain/
│   │       └── contractService.js      # Smart contract interactions
│   └── utils/
│       ├── handlerUtils.js             # Common handler patterns
│       ├── responseUtils.js            # Response formatting
│       ├── validationUtils.js          # Input validation
│       ├── dynamoUtils.js              # DynamoDB utilities
│       ├── atomicTransactions.js       # Atomic transaction utilities
│       ├── stateMachine.js             # State machine implementation
│       └── migrationUtils.js           # Schema migration utilities
├── constants/                           # System constants
│   └── index.js                        # Status codes, roles, etc.
├── config/                             # Configuration
│   └── aws.js                          # AWS SDK configuration
├── __tests__/                          # Test files
│   ├── setup.js                        # Global test setup and mocks
│   ├── shared/
│   │   └── mockData.js                 # Reusable mock data for all tests
│   ├── integration/                    # Module interaction tests
│   │   ├── wallet-transaction.test.js  # Wallet ↔ Transaction interactions
│   │   ├── exchange-rate-usdc.test.js  # Exchange Rate ↔ USDC interactions
│   │   ├── swap-wallet.test.js         # Swap ↔ Wallet interactions
│   │   ├── auth-validation.test.js     # Auth ↔ Validation interactions
│   │   └── module-interactions.test.js # Legacy redirect file
│   └── scenarios/                      # End-to-end workflow tests
│       ├── user-journey.test.js        # Complete user journeys
│       ├── admin-workflow.test.js      # Admin workflow scenarios
│       ├── error-recovery.test.js      # Error handling scenarios
│       ├── data-integrity.test.js      # Data consistency scenarios
│       ├── performance-load.test.js    # Performance & load scenarios
│       └── locked-balance.test.js      # Token locking scenarios
├── package.json                        # Updated with Jest configuration
└── run-tests.js                       # Comprehensive test runner
```

## 📋 **Complete API Reference**

### **🔄 MVT to USDC Swap Operations**

#### **1. Request MVT Wallet Swap (User)**
```graphql
mutation RequestMVTWalletSwap($input: SwapRequestInput!) {
  requestMVTWalletSwap(input: $input) {
    statusCode
    message
    data {
      id
      userId
      userWalletAddress
      mvtAmount        # Integer only
      usdcAmount       # Float allowed
      exchangeRate
      status
      description
      requestedAt
      expiresAt
    }
  }
}
```

**Variables**:
```json
{
  "input": {
    "mvtAmount": 100,    // Must be integer
    "description": "Swap 100 MVT tokens for USDC"
  }
}
```

#### **2. Admin Approve Swap**
```graphql
mutation ApproveMVTWalletSwap($input: SwapApprovalInput!) {
  approveMVTWalletSwap(input: $input) {
    statusCode
    message
    data {
      success
      swapRequestId
      mvtAmount        # Integer
      usdcAmount       # Float
      transactionHash
      newUserBalance
    }
  }
}
```

#### **3. Admin Reject Swap**
```graphql
mutation RejectMVTWalletSwap($input: SwapRejectionInput!) {
  rejectMVTWalletSwap(input: $input) {
    statusCode
    message
    data {
      success
      swapRequestId
      status
      rejectionReason
    }
  }
}
```

### **💰 USDC Liquidity Pool Operations (Admin Only)**

#### **4. Admin Deposit USDC**
```graphql
mutation AdminDepositUSDC($input: USDCDepositInput!) {
  adminDepositUSDC(input: $input) {
    statusCode
    message
    data {
      id
      transactionType
      amount           # Float allowed
      status
      transactionHash
      blockNumber
      gasUsed
    }
  }
}
```

#### **5. Admin Withdraw USDC**
```graphql
mutation AdminWithdrawUSDC($input: USDCWithdrawalInput!) {
  adminWithdrawUSDC(input: $input) {
    statusCode
    message
    data {
      id
      transactionType
      amount           # Float allowed
      status
      transactionHash
    }
  }
}
```

### **📊 Supporting Operations**

#### **6. Get Exchange Rate**
```graphql
query GetMVTWalletExchangeRate {
  getMVTWalletExchangeRate {
    statusCode
    message
    data {
      currentRate
      rateDisplay
      liquidityStatus {
        usdcReserves
        mvtSupply
        status
      }
    }
  }
}
```

#### **7. Get User Balance**
```graphql
query GetUserMVTWalletBalance($input: UserBalanceQueryInput) {
  getUserMVTWalletBalance(input: $input) {
    statusCode
    message
    data {
      userId
      balance          # Integer MVT amount
      totalReceived    # Integer MVT amount
      totalSent        # Integer MVT amount
    }
  }
}
```

## 🔧 Hybrid Architecture Implementation

### 1. **Local MVT Operations**
- **Wallet Management**: User balances stored in DynamoDB (`UserMVTBalance` table)
- **Transaction Processing**: All MVT transfers handled locally without blockchain calls
- **Minting**: Admin can mint MVT tokens directly to local balances
- **Speed & Cost**: Instant transactions with no gas fees
- **Integer Validation**: All MVT amounts strictly validated as integers

### 2. **Blockchain USDC Operations**
- **Liquidity Pool**: USDC reserves managed via smart contract
- **User Transfers**: USDC sent directly to user wallet addresses on blockchain
- **Security**: Leverages blockchain immutability for USDC operations
- **Transparency**: All USDC movements recorded on-chain
- **Float Support**: USDC amounts support decimal precision

### 3. **Local Exchange Rate Calculation**
- **Dynamic Pricing**: Rates calculated based on local MVT supply and blockchain USDC reserves
- **Real-time Updates**: Exchange rates updated on each request
- **Liquidity Management**: Automatic rate adjustments based on available liquidity
- **No Contract Dependency**: Exchange rates calculated locally, not from smart contract
- **Integer MVT Handling**: MVT amounts in calculations treated as integers

### 4. **Swap Request Pattern (Following mvtToken)**
- **Local Storage**: All swap requests stored in `MVTSwapRequest` DynamoDB table
- **API Compatibility**: Same response patterns as mvtToken for consistency
- **Transaction Logging**: Complete audit trail in `MVTWalletTransaction` table
- **Admin Approval**: Manual approval process for all swap requests
- **Data Type Consistency**: MVT amounts as integers, USDC amounts as floats

### 3. **Production Ready**
- Clean, descriptive function and variable names
- Proper error handling with standardized responses
- Comprehensive input validation
- Structured logging with Pino

### 4. **Hybrid Blockchain Integration**
- **MVT Operations**: Internal wallet system with DynamoDB storage
- **USDC Operations**: Blockchain contract integration with real USDC transfers
- **Unified Transaction Tracking**: Both MVT and USDC transactions with `tokenType` field
- **Separate GraphQL Types**: mvtWallet-specific response types to avoid conflicts

## 📋 Available Operations

### Admin Operations
- `getAdminMVTWalletBalance` - Get central wallet balance
- `adminMintMVT` - Mint MVT tokens to central wallet
- `adminTransferMVT` - Transfer MVT tokens from central wallet to user
- `getUSDCLiquidityPool` - Get USDC liquidity pool status
- `adminDepositUSDC` - Deposit USDC to liquidity pool for swaps
- `adminWithdrawUSDC` - Withdraw USDC from liquidity pool
- `getMVTSwapRequests` - Get all swap requests (admin view)
- `approveMVTSwap` - Approve user swap requests
- `rejectMVTSwap` - Reject user swap requests with reason

### User Operations
- `getUserMVTWalletBalance` - Get user wallet balance and recent transactions
- `userTransferMVT` - Transfer MVT tokens to another user (user-to-user transfer)
- `getMVTWalletTransactionList` - Get transaction list (user's own or admin view)
- `getMVTWalletTransactionById` - Get specific transaction details
- `getExchangeRate` - Get current MVT to USDC exchange rate
- `requestMVTSwap` - Request to swap MVT tokens for USDC
- `getMVTSwapRequests` - Get user's own swap requests
- `getMVTSwapRequestById` - Get specific swap request details

## 🔐 Authorization

### Admin Access
- Required for: minting, transferring, viewing all transactions
- Checks user role: `SUPER_ADMIN` or `STAFF_MEMBER`
- Validates against User table in DynamoDB

### User Access
- Users can only access their own data
- Automatic user ID extraction from Cognito context
- Admin users can access any user's data

## 🗃️ Database Tables

### Primary Tables
- **MVTTokenWallet**: Central wallet balance and statistics
- **UserMVTBalance**: Individual user balances (optional)
- **MVTWalletTransaction**: Complete transaction audit trail
- **User**: User authentication and role information
- **USDCLiquidityPool**: USDC reserves and liquidity management
- **MVTSwapRequest**: User swap requests and processing status

### Table Existence Handling
- Graceful handling of missing tables during schema deployment
- Default values returned when tables don't exist yet
- Non-blocking operations for optional tables

## 🧪 Test Cases

### 🔐 Authentication & Authorization
| Test ID | Description | Expected Outcome |
|---------|-------------|------------------|
| TC001 | Admin user accesses admin wallet balance | 200 OK + Central wallet balance returned |
| TC002 | Non-admin user attempts admin operations | 403 Forbidden + "Admin access required" |
| TC003 | User accesses own wallet balance | 200 OK + User balance returned |
| TC004 | User tries to access other user's balance | 403 Forbidden + Authorization error |
| TC005 | Unauthenticated request without token | 401 Unauthorized + Authentication required |
| TC006 | Admin checks other user's balance | 200 OK + Target user balance returned |

### 👤 User Operations (Happy Path)
| Test ID | Description | Expected Outcome |
|---------|-------------|------------------|
| TC007 | User receives MVT tokens from admin | 200 OK + User balance increases + Transaction logged |
| TC008 | User transfers MVT to another user | 200 OK + Sender decreases, recipient increases + Audit trail |
| TC009 | User creates MVT to USDC swap request | 200 OK + Tokens locked + Swap request created + Rate calculated |
| TC010 | User views own transaction history | 200 OK + Complete transaction list returned |
| TC011 | User checks specific transaction details | 200 OK + Transaction details returned |
| TC012 | User views current exchange rate | 200 OK + Current rate + Liquidity status |

### 👨‍💼 Admin Operations (Happy Path)
| Test ID | Description | Expected Outcome |
|---------|-------------|------------------|
| TC013 | Admin mints MVT tokens to central wallet | 200 OK + Central balance increases + Mint transaction logged |
| TC014 | Admin transfers MVT from central to user | 200 OK + Central decreases, user increases + Transaction logged |
| TC015 | Admin deposits USDC to liquidity pool | 200 OK + Blockchain transaction + Pool balance updated |
| TC016 | Admin withdraws USDC from liquidity pool | 200 OK + USDC transferred + Pool balance decreased |
| TC017 | Admin approves user swap request | 200 OK + USDC transferred + MVT unlocked + Status updated |
| TC018 | Admin rejects user swap request | 200 OK + Tokens unlocked + Status updated + Reason logged |
| TC019 | Admin views all swap requests | 200 OK + Complete swap request list returned |
| TC020 | Admin views all user transactions | 200 OK + System-wide transaction history |

### ⚠️ User Error Scenarios
| Test ID | Description | Expected Outcome |
|---------|-------------|------------------|
| TC021 | User transfers with decimal MVT amount | 400 Bad Request + "MVT amount must be whole number" |
| TC022 | User transfers with insufficient balance | 400 Bad Request + "Insufficient balance" error |
| TC023 | User transfers zero or negative amount | 400 Bad Request + "Amount must be greater than 0" |
| TC024 | User transfers to invalid recipient ID | 400 Bad Request + "Invalid user ID" error |
| TC025 | User creates swap without wallet address | 400 Bad Request + "Valid wallet address required" |
| TC026 | User accesses other user's data | 403 Forbidden + Authorization error message |

### ⚠️ Admin Error Scenarios
| Test ID | Description | Expected Outcome |
|---------|-------------|------------------|
| TC027 | Admin mints with decimal MVT amount | 400 Bad Request + "MVT amount must be integer" |
| TC028 | Admin transfers to non-existent user | 400 Bad Request + "User not found" error |
| TC029 | Admin deposits USDC with blockchain failure | 500 Internal Server Error + Blockchain error handling |
| TC030 | Admin approves already processed swap | 400 Bad Request + "Swap already processed" |
| TC031 | Non-admin attempts admin operations | 403 Forbidden + "Admin access required" |

### 🔧 System Error Handling
| Test ID | Description | Expected Outcome |
|---------|-------------|------------------|
| TC032 | Database connection failure | 500 Internal Server Error + Graceful error handling |
| TC033 | Blockchain connectivity issues | 500 Internal Server Error + Fallback behavior |
| TC034 | Concurrent balance operations | All operations complete + No race conditions |
| TC035 | Partial failure with rollback | Failed operation + No partial state changes |
| TC036 | High-volume concurrent operations | All operations complete + Performance maintained |

### 🧪 Edge Cases & Boundary Tests
| Test ID | Description | Expected Outcome |
|---------|-------------|------------------|
| TC037 | Transfer maximum safe integer amount | 200 OK + Successful processing |
| TC038 | Transfer minimum valid amount (1 MVT) | 200 OK + Successful processing |
| TC039 | Handle special number values (NaN, Infinity) | 400 Bad Request + Validation errors |
| TC040 | Process very long user ID (255 chars) | 200 OK + Successful processing |
| TC041 | Reject oversized user ID (256+ chars) | 400 Bad Request + "ID too long" error |
| TC042 | Handle malformed event objects | Error thrown + Graceful failure |
| TC043 | Process timeout scenarios | 500 Internal Server Error + Timeout handling |

### 🔄 Integration & Workflow Tests
| Test ID | Description | Expected Outcome |
|---------|-------------|------------------|
| TC044 | Complete user journey: Receive → Transfer → Swap | All operations succeed + Complete audit trail |
| TC045 | Admin workflow: Setup → Monitor → Approve | All admin operations succeed + System ready |
| TC046 | Mixed user and admin concurrent operations | All operations complete + No conflicts |

### 🧾 Test Framework Details

**Test Framework**: Jest

**Test Commands**:
```bash
# Run all tests
npm test

# Run specific module tests
npm run test:wallet
npm run test:transaction

# Run integration tests
npm run test:integration

# Run scenario tests
npm run test:scenarios

# Generate coverage report
npm run test:coverage

# Watch mode for development
npm run test:watch
```

**Test Structure**: For detailed test logic and implementation, check the `__tests__/` and `modules/*/tests/` folders.


## 📖 Usage Examples

### Admin Mint Tokens
```javascript
// GraphQL Mutation
mutation AdminMintMVT($input: AdminMintMVTInput!) {
  adminMintMVT(input: $input) {
    statusCode
    message
    data {
      id
      amount
      transactionType
      status
      createdAt
    }
  }
}

// Variables
{
  "input": {
    "amount": 1000,
    "description": "Monthly token mint"
  }
}
```

### User Transfer Tokens
```javascript
// GraphQL Mutation
mutation UserTransferMVT($input: UserTransferMVTInput!) {
  userTransferMVT(input: $input) {
    statusCode
    message
    data {
      id
      amount
      fromUserId
      toUserId
      transactionType
      status
      createdAt
    }
  }
}

// Variables
{
  "input": {
    "recipientUserId": "target-user-id",
    "amount": 50,
    "description": "Payment for services"
  }
}
```

### Request MVT Swap
```javascript
// GraphQL Mutation
mutation RequestMVTWalletSwap($input: SwapRequestInput!) {
  requestMVTWalletSwap(input: $input) {
    statusCode
    message
    data {
      id
      userId
      userWalletAddress
      mvtAmount
      usdcAmount
      exchangeRate
      status
      description
      requestedAt
      expiresAt
    }
  }
}

// Variables
{
  "input": {
    "mvtAmount": 100,
    "description": "Swap MVT for USDC"
  }
}
```

### Get Exchange Rate
```javascript
// GraphQL Query
query GetMVTWalletExchangeRate {
  getMVTWalletExchangeRate {
    statusCode
    message
    data {
      currentRate
      rateDisplay
      liquidityStatus {
        usdcReserves
        mvtSupply
        liquidityRatio
        status
      }
      lastUpdated
    }
  }
}
```

### Get User Balance
```javascript
// GraphQL Query
query GetUserMVTWalletBalance($input: GetUserMVTWalletBalanceInput) {
  getUserMVTWalletBalance(input: $input) {
    statusCode
    message
    data {
      userId
      balance
      totalReceived
      recentTransactions {
        id
        amount
        transactionType
        createdAt
      }
    }
  }
}
```

## 🚀 Deployment

The refactored code maintains full backward compatibility. No changes are required to:
- GraphQL schema
- Frontend applications
- Existing tests
- Database configurations

Simply deploy the updated Lambda function code and it will work with existing infrastructure.





## 🧪 Testing Documentation

### Overview

The MVT Wallet Lambda function includes a comprehensive Jest test suite covering all modules with unit tests, integration tests, and scenario-based testing following established codebase patterns.

### Test Structure

#### Directory Organization

```
src/
├── __tests__/
│   ├── setup.js                         # Global test setup and mocks
│   ├── shared/
│   │   └── mockData.js                  # Reusable mock data for all tests
│   ├── integration/                     # Module interaction tests
│   │   ├── wallet-transaction.test.js   # Wallet ↔ Transaction interactions
│   │   ├── exchange-rate-usdc.test.js   # Exchange Rate ↔ USDC interactions
│   │   ├── swap-wallet.test.js          # Swap ↔ Wallet interactions
│   │   ├── auth-validation.test.js      # Auth ↔ Validation interactions
│   │   └── module-interactions.test.js  # Legacy redirect file
│   └── scenarios/                       # End-to-end workflow tests
│       ├── user-journey.test.js         # Complete user journeys
│       ├── admin-workflow.test.js       # Admin workflow scenarios
│       ├── error-recovery.test.js       # Error handling scenarios
│       ├── data-integrity.test.js       # Data consistency scenarios
│       ├── performance-load.test.js     # Performance & load scenarios
│       └── locked-balance.test.js       # Token locking scenarios
├── modules/
│   ├── wallet/__tests__/
│   │   ├── wallet.handlers.test.js      # Handler unit tests
│   │   ├── wallet.service.test.js       # Service unit tests
│   │   ├── wallet.validation.test.js    # Validation unit tests
│   │   └── wallet.resolvers.test.js     # Resolver unit tests
│   └── transaction/__tests__/
│       ├── transaction.handlers.test.js
│       ├── transaction.service.test.js
│       ├── transaction.validation.test.js
│       └── transaction.resolvers.test.js
├── package.json                         # Jest configuration
└── run-tests.js                        # Comprehensive test runner
```

### Test Categories

#### 1. Unit Tests

**Handler Tests**
- GraphQL event processing and argument extraction
- Authentication and authorization controls
- Input validation and error handling
- Response formatting consistency
- Graceful error handling with user-friendly messages

**Service Tests**
- Core business logic and calculations
- DynamoDB operations with proper mocking
- Blockchain contract interactions
- Atomic operations and race condition prevention
- Transaction consistency and audit trails

**Validation Tests**
- Input validation with valid/invalid data
- Edge cases: boundary values, null/undefined inputs
- Type checking and data sanitization
- Domain-specific business rules

**Resolver Tests**
- GraphQL field mapping and routing
- Function signature compatibility
- Error propagation through resolver chain

#### 2. Integration Tests

**Wallet ↔ Transaction Integration**
- Token minting with atomic wallet balance updates
- Transfer operations with proper balance validation
- Transaction logging with wallet state consistency
- Error handling with proper rollback mechanisms

**Exchange Rate ↔ USDC Integration**
- Exchange rate calculations based on USDC liquidity pools
- Swap feasibility validation using real-time rates
- Blockchain connectivity handling for rate calculations
- Liquidity pool status monitoring

**Swap ↔ Wallet Integration**
- Token locking during swap request creation
- Token unlocking on swap rejection
- Token transfer to central wallet on swap approval
- Locked balance management for concurrent swaps

**Authentication ↔ Validation Integration**
- Admin permission validation for restricted operations
- User authorization for wallet operations
- Input validation with authentication context
- Cross-module security enforcement

#### 3. Scenario Tests

**User Journey Scenarios**
- Complete user workflow: Receive tokens → Transfer to friend → Create swap request
- Insufficient balance handling during transfers and swaps
- User authentication and authorization throughout journey

**Admin Workflow Scenarios**
- Complete admin workflow: Setup liquidity → Monitor system → Approve swaps
- Admin permission validation for all administrative operations
- System monitoring and swap request management

**Error Recovery Scenarios**
- System failure handling with graceful degradation
- Blockchain connectivity issues and fallback behavior
- Concurrent operation safety and race condition prevention

**Data Integrity Scenarios**
- Consistency maintenance during complex multi-step operations
- Partial failure handling with proper rollback mechanisms
- Atomic transaction processing across modules

**Performance & Load Scenarios**
- High-volume operation handling under load
- Mixed user and admin concurrent operations
- System performance under stress conditions

**Locked Balance Scenarios**
- Token locking mechanisms during swap creation
- Token unlocking on swap rejection
- Locked token transfer on swap approval
- Available balance calculations with locked tokens
- Concurrent swap request handling

### Running Tests

#### Test Commands
```bash
# Run all tests
npm test

# Run specific module tests
npm run test:wallet
npm run test:transaction
npm run test:usdc
npm run test:exchangeRate
npm run test:swap

# Run integration tests (all)
npm run test:integration

# Run specific integration tests
npm run test:wallet-transaction
npm run test:exchange-rate-usdc
npm run test:swap-wallet
npm run test:auth-validation

# Run scenario tests (all)
npm run test:scenarios

# Run specific scenario tests
npm run test:user-journey
npm run test:admin-workflow
npm run test:error-recovery
npm run test:data-integrity
npm run test:performance-load
npm run test:locked-balance

# Generate coverage report
npm run test:coverage

# Watch mode for development
npm run test:watch

# Use advanced test runner
npm run test:runner
npm run test:runner userJourney
npm run test:runner walletTransaction
npm run test:runner coverage
```

#### Coverage Requirements
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%
- **Statements**: 80%

## 🚨 Race Condition Prevention

### Overview
The MVT Wallet system implements comprehensive race condition prevention strategies to ensure data consistency and system reliability.

### Prevention Strategies

#### 1. Atomic Multi-Table Operations
The system uses DynamoDB TransactWrite operations for multi-table updates:

```javascript
// Atomic transaction example:
const transactParams = {
  TransactItems: [
    { Update: updateUserBalanceParams },
    { Put: createSwapRequestParams },
    { Put: createTransactionParams }
  ]
};
await ddb.transactWriteItems(transactParams).promise();
```

#### 2. Optimistic Locking
Version control prevents concurrent modification conflicts:

```javascript
// Optimistic locking with version control:
const updateParams = {
  ConditionExpression: 'version = :currentVersion',
  UpdateExpression: 'SET balance = :newBalance, version = version + :inc',
  ExpressionAttributeValues: {
    ':currentVersion': currentVersion,
    ':newBalance': newBalance,
    ':inc': 1
  }
};
```

#### 3. State Machine Pattern
Formal state transitions with validation ensure consistent state changes:

```javascript
// State machine validation:
const validTransitions = {
  'PENDING': ['APPROVED', 'REJECTED'],
  'APPROVED': ['COMPLETED'],
  'REJECTED': []
};
```





## 🚀 Deployment

The MVT Wallet Lambda function maintains full backward compatibility:
- GraphQL schema compatibility preserved
- All existing API endpoints unchanged
- Database configurations remain the same
- Frontend applications require no modifications

Deploy using standard Amplify commands:
```bash
amplify push
```
### **Key Features Summary**
- ✅ **Strict MVT Integer Validation**: All MVT amounts must be whole numbers
- ✅ **USDC Float Support**: USDC amounts can have decimal places
- ✅ **Hybrid Architecture**: Local MVT operations + Blockchain USDC operations
- ✅ **Robust Error Handling**: Comprehensive validation and fallback mechanisms
- ✅ **Complete API Coverage**: All operations support proper data type validation
- ✅ **Production Ready**: Clean code, proper error handling, comprehensive testing
