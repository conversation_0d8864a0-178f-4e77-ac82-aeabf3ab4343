const AWS = require("aws-sdk");

const dynamoDb = new AWS.DynamoDB();
const EnvironmentName = `${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
const CityTable = `City-${EnvironmentName}`;
const UserTable = `User-${EnvironmentName}`;
const TaskTable = `Task-${EnvironmentName}`;
const MembershipTable = `Membership-${EnvironmentName}`;
const AssociationTable = `Association-${EnvironmentName}`;
const OrganizationsTable = `Organizations-${EnvironmentName}`;
const EventsTable = `Events-${EnvironmentName}`;
const ProgramsTable = `Programs-${EnvironmentName}`;
const BusinessTable = `Business-${EnvironmentName}`;
const PointsTable = `Points-${EnvironmentName}`;
const IdeasTable = `Ideas-${EnvironmentName}`;
const HomeworkTable = `Homework-${EnvironmentName}`;
const CityFundTransactionsTable = `CityFundTransactions-${EnvironmentName}`;
const SubmissionTable = `Submission-${EnvironmentName}`;
const ActivityTable = `Activity-${EnvironmentName}`;

async function deleteCity(cityId) {
  console.log(`Attempting to delete city with ID: ${cityId}`);

  const params = {
    TableName: CityTable,
    Key: { id: { S: cityId } },
  };

  try {
    await dynamoDb.deleteItem(params).promise();
    console.log(`Successfully deleted city with ID: ${cityId}`);
  } catch (error) {
    console.error(`Failed to delete city with ID: ${cityId}`, error);
  }
}

async function removeCityData(cityId) {
  const params = {
    TableName: CityTable,
    Key: { id: { S: cityId } },
  };

  const data = await dynamoDb.getItem(params).promise();
  if (!data.Item) {
    console.log(`City with ID: ${cityId} does not exist`);
    throw new Error(`City with ID: ${cityId} does not exist`);
  }
  await deleteAssociatedData(cityId);
  await deleteCity(cityId);
}

async function deleteUserStackholderCities(cityId) {
  console.log(`Starting to delete stackholder cities for cityId: ${cityId}`);
  
  const userParams = {
    TableName: UserTable,
    FilterExpression: "cityId = :cityId",
    ExpressionAttributeValues: {
      ":cityId": { S: cityId },
    },
    ProjectionExpression: "id, stackholderCities",
  };

  try {
    const data = await dynamoDb.scan(userParams).promise();
    const items = data.Items;
    console.log(`Found ${items.length} users with cityid: ${cityId}`);

    if (items.length > 0) {
      await Promise.all(
        items.map(async (item) => {
          console.log(`Processing user with id: ${item.id.S}`);
          
          if (item.stackholderCities?.L) {
            const updatedStakeholderCities = item.stackholderCities.L.filter(
              (city) => city.S !== cityId
            ).map((city) => ({ S: city.S }));

            console.log(`Updated stakeholder cities for user id ${item.id.S}:`, updatedStakeholderCities);

            if (updatedStakeholderCities.length > 0) {
              const updateParams = {
                TableName: UserTable,
                Key: { id: { S: item.id.S } },
                UpdateExpression: "SET stackholderCities = :stackholderCities",
                ExpressionAttributeValues: {
                  ":stackholderCities": {
                    L: updatedStakeholderCities,
                  },
                },
              };

              try {
                await dynamoDb.updateItem(updateParams).promise();
                console.log(`Successfully updated user with id ${item.id.S}`);
              } catch (error) {
                console.error(`Error updating user with id ${item.id.S}`, error);
              }
            } else {
              await deleteUserFromStackholderCity(item.id.S);
            }
          }
        })
      );
    }
  } catch (error) {
    console.error(`Error fetching users for cityId: ${cityId}`, error);
  }
  
  console.log(`Completed deleting stackholder cities for cityId: ${cityId}`);
}

async function removeUserActivities(userId) {
  console.log(`Starting to delete user activities for user with id: ${userId}`);
  
  const params = {
    TableName: ActivityTable,
    FilterExpression: "moduleId = :userId",
    ExpressionAttributeValues: {
      ":userId": { S: userId },
    },
  };

  try {
    const data = await dynamoDb.scan(params).promise();
    const items = data.Items;

    console.log(`Found ${items.length} activities for user with id: ${userId}`);

    if (items.length > 0) {
      await Promise.all(
        items.map(async (item) => {
          const deleteParams = {
            TableName: ActivityTable,
            Key: { id: {S: item.id.S }},
          };

          try {
            await dynamoDb.deleteItem(deleteParams).promise();
            console.log(`Successfully deleted activity with id ${item.id.S}`);
          } catch (error) {
            console.error(`Error deleting activity with id ${item.id.S}`, error);
          }
        })
      );
    }
  } catch (error) {
    console.error(`Error fetching activities for user with id: ${userId}`, error);
  }
  
  console.log(`Completed deleting user activities for user with id: ${userId}`);
}

async function deleteUserFromStackholderCity(userId) {
  await removeUserActivities(userId);
  console.log(`Starting to delete user with id: ${userId}`);
  
  const params = {
    TableName: UserTable,
    Key: { id: { S: userId } },
  };

  try {
    await dynamoDb.deleteItem(params).promise();
    console.log(`Successfully deleted user with id ${userId}`);
  } catch (error) {
    console.error(`Error deleting user with id ${userId}`, error);
  }
  
  console.log(`Completed deleting user with id: ${userId}`);
}

async function deleteAssociatedData(cityId) {
  await deleteItems(TaskTable, cityId);
  await deleteItems(MembershipTable, cityId);
  await deleteItems(AssociationTable, cityId);
  await deleteItems(OrganizationsTable, cityId);
  await deleteItems(EventsTable, cityId);
  await deleteItems(ProgramsTable, cityId);
  await deleteItems(BusinessTable, cityId);
  await deleteItems(PointsTable, cityId);
  await deleteItems(IdeasTable, cityId);
  await deleteItems(HomeworkTable, cityId);
  await deleteItems(CityFundTransactionsTable, cityId);
  await deleteItems(SubmissionTable, cityId);
  await deleteUserStackholderCities(cityId);
}

async function deleteItems(tableName, cityId) {
  const params = {
    TableName: tableName,
    FilterExpression: "cityId = :cityId",
    ExpressionAttributeValues: {
      ":cityId": { S: cityId },
    },
  };

  console.log(`Fetching items from ${tableName} for cityId: ${cityId}`);

  const data = await dynamoDb.scan(params).promise();
  const items = data.Items;

  console.log(`Found ${items.length} items with cityId: ${cityId}`);

  if (items.length > 0) {
    await Promise.all(
      items.map(async (item) => {
        console.log(`Processing item with id: ${item.id.S}`);

        const deleteParams = {
          TableName: tableName,
          Key: { id: { S: item.id.S } },
        };

        console.log(`Deleting item with id: ${item.id.S}`);

        await dynamoDb.deleteItem(deleteParams).promise();
      })
    );
  } else {
    console.log(`No items found with cityId: ${cityId}`);
  }
}

module.exports = {
  removeCityData,
  deleteCity,
};
