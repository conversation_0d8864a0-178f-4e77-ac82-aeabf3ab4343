{"version": 1, "paths": {"/user": {"name": "/user", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/userv2": {"name": "/userv2", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/userConfirm": {"name": "/userConfirm", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/membership": {"name": "/membership", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/userCreate": {"name": "/userCreate", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/ideaCreate": {"name": "/ideaCreate", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/userLoginDetail": {"name": "/userLoginDetail", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/deleteRecord": {"name": "/deleteRecord", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/publishNotification": {"name": "/publishNotification", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/checkPhoneNumberExist": {"name": "/checkPhoneNumberExist", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/webhook": {"name": "/webhook", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/webhookNew": {"name": "/webhookNew", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/sendVerificationCode": {"name": "/sendVerificationCode", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/verifyCode": {"name": "/verifyCode", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/verifyUserDefault": {"name": "/verifyUserDefault", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/resendVerificationCode": {"name": "/resendVerificationCode", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/setAuthToken": {"name": "/setAuthToken", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/chatMember": {"name": "/chatMember", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/checkUserStatus": {"name": "/checkUserStatus", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/userCreatev2": {"name": "/userCreatev2", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}, "/verifyUserDefaultV2": {"name": "/verifyUserDefaultV2", "lambdaFunction": "getUserDetailFun", "permissions": {"setting": "open"}}}}