const AWS = require('aws-sdk');
const { deleteCity } = require('./city-remove');

const dynamoDb = new AWS.DynamoDB();
const EnvironmentName = `${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
const UserTable = `User-${EnvironmentName}`;
const TaskTable = `Task-${EnvironmentName}`;
const MembershipTable = `Membership-${EnvironmentName}`;
const AssociationTable = `Association-${EnvironmentName}`;
const OrganizationsTable = `Organizations-${EnvironmentName}`;
const EventsTable = `Events-${EnvironmentName}`;
const ProgramsTable = `Programs-${EnvironmentName}`;
const BusinessTable = `Business-${EnvironmentName}`;
const PointsTable = `Points-${EnvironmentName}`;
const IdeasTable = `Ideas-${EnvironmentName}`;
const HomeworkTable = `Homework-${EnvironmentName}`;
const CityFundTransactionsTable = `CityFundTransactions-${EnvironmentName}`;
const SubmissionTable = `Submission-${EnvironmentName}`;


async function transferData(fromCityId, toCityId) {

  await transferEntityData(UserTable, fromCityId, toCityId, updateUserCityId);
  await transferEntityData(TaskTable, fromCityId, toCityId, updateTaskCityId);
  await transferEntityData(MembershipTable, fromCityId, toCityId, updateMembershipCityId);
  await transferEntityData(AssociationTable, fromCityId, toCityId, updateAssociationCityId);
  await transferEntityData(OrganizationsTable, fromCityId, toCityId, updateOrganizationCityId);
  await transferEntityData(EventsTable, fromCityId, toCityId, updateEventsCityId);
  await transferEntityData(ProgramsTable, fromCityId, toCityId, updateProgramsCityId);
  await transferEntityData(BusinessTable, fromCityId, toCityId, updateBusinessCityId);
  await transferEntityData(PointsTable, fromCityId, toCityId, updatePointsCityId);
  await transferEntityData(IdeasTable, fromCityId, toCityId, updateIdeasCityId);
  await transferEntityData(HomeworkTable, fromCityId, toCityId, updateHomeworkCityId);
  await transferEntityData(CityFundTransactionsTable, fromCityId, toCityId, updateCityFundTransactionsCityId);
  await transferEntityData(SubmissionTable, fromCityId, toCityId, updateSubmissionCityId);
  await deleteCity(fromCityId);
}

async function transferEntityData(tableName, fromCityId, toCityId, updateFunction) {
  const params = {
    TableName: tableName,
    FilterExpression: 'cityId = :cityId',
    ExpressionAttributeValues: {
      ':cityId': { S: fromCityId },
    },
  };

  const data = await dynamoDb.scan(params).promise();
  const items = data.Items;

  if (tableName === UserTable) {
    await Promise.all(items.map(async (item) => {
      await updateFunction(item.id.S, toCityId, fromCityId);
    }));
  } else {
    await Promise.all(items.map(async (item) => {
      await updateFunction(item.id.S, toCityId);
    }));
  }
}

async function updateTaskCityId(taskId, cityId) {
  const updateParams = {
    TableName: TaskTable,
    Key: { id: { S: taskId } },
    UpdateExpression: 'SET cityId = :cityId',
    ExpressionAttributeValues: {
      ':cityId': { S: cityId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updateUserCityId(userId, toCityId, fromCityId) {
  console.log('updateUserCityId: ', userId, toCityId, fromCityId);
  const params = {
    TableName: UserTable,
    Key: { id: { S: userId } },
  };

  const data = await dynamoDb.getItem(params).promise();
  const user = data.Item;
  console.log('user: ', user);

  const updatedStakeholderCities = (user?.stackholderCities?.L || [])
  .map(city => city.S === fromCityId ? { S: toCityId } : city);
  
  console.log('updatedStakeholderCities: ', updatedStakeholderCities);
  const updateParams = {
    TableName: UserTable,
    Key: { id: { S: userId } },
    UpdateExpression: 'SET cityId = :toCityId, stackholderCities = :stackholderCities',
    ExpressionAttributeValues: {
      ':toCityId': { S: toCityId },
      ':stackholderCities': {
        L: updatedStakeholderCities,
      },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updateMembershipCityId(membershipId, cityId) {
  const updateParams = {
    TableName: MembershipTable,
    Key: { id: { S: membershipId } },
    UpdateExpression: 'SET cityId = :cityId',
    ExpressionAttributeValues: {
      ':cityId': { S: cityId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updateAssociationCityId(associationId, cityId) {
  const updateParams = {
    TableName: AssociationTable,
    Key: { id: { S: associationId } },
    UpdateExpression: 'SET cityId = :cityId',
    ExpressionAttributeValues: {
      ':cityId': { S: cityId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updateOrganizationCityId(organizationId, cityId) {
  const updateParams = {
    TableName: OrganizationsTable,
    Key: { id: { S: organizationId } },
    UpdateExpression: 'SET cityId = :cityId',
    ExpressionAttributeValues: {
      ':cityId': { S: cityId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updateEventsCityId(eventId, cityId) {
  const updateParams = {
    TableName: EventsTable,
    Key: { id: { S: eventId } },
    UpdateExpression: 'SET cityId = :cityId',
    ExpressionAttributeValues: {
      ':cityId': { S: cityId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updateProgramsCityId(programId, cityId) {
  const updateParams = {
    TableName: ProgramsTable,
    Key: { id: { S: programId } },
    UpdateExpression: 'SET cityId = :cityId',
    ExpressionAttributeValues: {
      ':cityId': { S: cityId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updateBusinessCityId(businessId, cityId) {
  const updateParams = {
    TableName: BusinessTable,
    Key: { id: { S: businessId } },
    UpdateExpression: 'SET cityId = :cityId',
    ExpressionAttributeValues: {
      ':cityId': { S: cityId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updatePointsCityId(pointsId, cityId) {
  const updateParams = {
    TableName: PointsTable,
    Key: { id: { S: pointsId } },
    UpdateExpression: 'SET cityId = :cityId',
    ExpressionAttributeValues: {
      ':cityId': { S: cityId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updateIdeasCityId(ideaId, cityId) {
  const updateParams = {
    TableName: IdeasTable,
    Key: { id: { S: ideaId } },
    UpdateExpression: 'SET cityId = :cityId',
    ExpressionAttributeValues: {
      ':cityId': { S: cityId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updateHomeworkCityId(homeworkId, cityId) {
  const updateParams = {
    TableName: HomeworkTable,
    Key: { id: { S: homeworkId } },
    UpdateExpression: 'SET cityId = :cityId',
    ExpressionAttributeValues: {
      ':cityId': { S: cityId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updateCityFundTransactionsCityId(cityFundTransactionsId, cityId) {
  const updateParams = {
    TableName: CityFundTransactionsTable,
    Key: { id: { S: cityFundTransactionsId } },
    UpdateExpression: 'SET cityId = :cityId',
    ExpressionAttributeValues: {
      ':cityId': { S: cityId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

async function updateSubmissionCityId(submissionId, cityId) {
  const updateParams = {
    TableName: SubmissionTable,
    Key: { id: { S: submissionId } },
    UpdateExpression: 'SET cityId = :cityId',
    ExpressionAttributeValues: {
      ':cityId': { S: cityId },
    },
    ReturnValues: 'UPDATED_NEW',
  };

  await dynamoDb.updateItem(updateParams).promise();
}

module.exports = {
  transferData
}