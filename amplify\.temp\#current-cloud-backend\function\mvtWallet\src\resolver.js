// Import all module resolvers
const walletResolvers = require('./modules/wallet/wallet.resolvers');
const transactionResolvers = require('./modules/transaction/transaction.resolvers');
const usdcResolvers = require('./modules/usdc/usdc.resolvers');
const exchangeRateResolvers = require('./modules/exchangeRate/exchangeRate.resolvers');
const swapResolvers = require('./modules/swap/swap.resolvers');
const stripeResolvers = require('./modules/stripe/stripe.resolvers');
const onrampResolvers = require('./modules/onramp/onramp.resolvers');
const responseUtils = require('./shared/utils/responseUtils');
const standardizedErrorUtils = require('./shared/utils/standardizedErrorUtils');

/**
 * Combined resolver mapping for all GraphQL fields
 * This maintains backward compatibility while introducing the new modular structure
 */
const resolvers = {
  // Wallet operations (new modular structure)
  ...walletResolvers,

  // Transaction operations (new modular structure)
  ...transactionResolvers,

  // USDC operations (new modular structure)
  ...usdcResolvers,

  // Exchange rate operations (new modular structure)
  ...exchangeRateResolvers,

  // Swap operations (new modular structure)
  ...swapResolvers,

  // Stripe operations (new modular structure)
  ...stripeResolvers,

  // Onramp operations (new modular structure)
  ...onrampResolvers
};

/**
 * Route GraphQL field to appropriate resolver
 * @param {string} fieldName - GraphQL field name
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function routeToResolver(fieldName, event, args) {
  try {
    const resolver = resolvers[fieldName];
    
    if (!resolver) {
      console.error(`Unknown GraphQL field: ${fieldName}`);
      return standardizedErrorUtils.createValidationError(
        fieldName,
        `Unknown GraphQL field: ${fieldName}`,
        'fieldName'
      );
    }
    
    console.log(`Routing ${fieldName} to resolver`);
    return await resolver(event, args);
  } catch (error) {
    console.error(`Error in resolver for ${fieldName}:`, error);
    return standardizedErrorUtils.createAutoDetectedError(
      fieldName,
      error,
      'GraphQL resolver execution'
    );
  }
}

/**
 * Get list of available GraphQL fields
 * @returns {Array<string>} - Array of available field names
 */
function getAvailableFields() {
  return Object.keys(resolvers).sort();
}

/**
 * Check if a field is available
 * @param {string} fieldName - Field name to check
 * @returns {boolean} - True if field is available
 */
function isFieldAvailable(fieldName) {
  return fieldName in resolvers;
}

/**
 * Get resolver statistics
 * @returns {object} - Statistics about resolvers
 */
function getResolverStats() {
  const totalResolvers = Object.keys(resolvers).length;
  const walletResolvers = Object.keys(require('./modules/wallet/wallet.resolvers')).length;
  const transactionResolvers = Object.keys(require('./modules/transaction/transaction.resolvers')).length;
  const usdcResolvers = Object.keys(require('./modules/usdc/usdc.resolvers')).length;
  const exchangeRateResolvers = Object.keys(require('./modules/exchangeRate/exchangeRate.resolvers')).length;
  const swapResolvers = Object.keys(require('./modules/swap/swap.resolvers')).length;
  const stripeResolvers = Object.keys(require('./modules/stripe/stripe.resolvers')).length;

  const modularTotal = walletResolvers + transactionResolvers + usdcResolvers + exchangeRateResolvers + swapResolvers + stripeResolvers;

  return {
    total: totalResolvers,
    modular: {
      wallet: walletResolvers,
      transaction: transactionResolvers,
      usdc: usdcResolvers,
      exchangeRate: exchangeRateResolvers,
      swap: swapResolvers,
      stripe: stripeResolvers
    },
    legacy: totalResolvers - modularTotal,
    migrationProgress: Math.round((modularTotal / totalResolvers) * 100)
  };
}

module.exports = {
  routeToResolver,
  resolvers,
  getAvailableFields,
  isFieldAvailable,
  getResolverStats
};
