{"providers": {"awscloudformation": {"AuthRoleName": "amplify-myvillageprojectadmi-amplifydev-141648-authRole", "UnauthRoleArn": "arn:aws:iam::************:role/amplify-myvillageprojectadmi-amplifydev-141648-unauthRole", "AuthRoleArn": "arn:aws:iam::************:role/amplify-myvillageprojectadmi-amplifydev-141648-authRole", "Region": "us-east-1", "DeploymentBucketName": "amplify-myvillageprojectadmi-amplifydev-141648-deployment", "UnauthRoleName": "amplify-myvillageprojectadmi-amplifydev-141648-unauthRole", "StackName": "amplify-myvillageprojectadmi-amplifydev-141648", "StackId": "arn:aws:cloudformation:us-east-1:************:stack/amplify-myvillageprojectadmi-amplifydev-141648/07e77b50-39b8-11ed-a31e-0a27fa9d2b4b", "AmplifyAppId": "dcithl8w2kxhu"}}, "api": {"getUserDetail": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "getUserDetailFun"}], "providerPlugin": "awscloudformation", "service": "API Gateway", "output": {"ApiName": "getUserDetail", "RootUrl": "https://6s0bso8sri.execute-api.us-east-1.amazonaws.com/amplifydev", "ApiId": "6s0bso8sri"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/api/getUserDetail-cloudformation-template.json", "logicalId": "apigetUserDetail"}, "lastPushDirHash": "E1Y9uthYEypHjC6TBcRLKkX3fzE="}, "mobileGameAPI": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "mobileGameAPIFunction"}], "providerPlugin": "awscloudformation", "service": "API Gateway", "output": {"ApiName": "mobileGameAPI", "RootUrl": "https://obghkaxdmb.execute-api.us-east-1.amazonaws.com/amplifydev", "ApiId": "obghkaxdmb"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/api/mobileGameAPI-cloudformation-template.json", "logicalId": "apimobileGameAPI"}, "lastPushDirHash": "HNx4VIynKvBBDLYd1c4Ti/ipm/c="}, "myvillageprojectadmi": {"dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}], "output": {"authConfig": {"additionalAuthenticationProviders": [{"authenticationType": "AWS_IAM"}], "defaultAuthentication": {"authenticationType": "AMAZON_COGNITO_USER_POOLS", "userPoolConfig": {"userPoolId": "authmyvillageprojectadmifeb4ea87"}}}, "securityType": "AMAZON_COGNITO_USER_POOLS", "GraphQLAPIIdOutput": "rlumv7mljje5bb65hsiqpi77hu", "GraphQLAPIEndpointOutput": "https://ixj5li7ny5aqxbimp2tvkv4mcu.appsync-api.us-east-1.amazonaws.com/graphql"}, "providerPlugin": "awscloudformation", "service": "AppSync", "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/api/cloudformation-template.json", "logicalId": "apimyvillageprojectadmi"}, "lastPushDirHash": "jUIdlMngxdlFKd0RP8+mIeWyaPg="}}, "auth": {"myvillageprojectadmifeb4ea87": {"customAuth": false, "dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": [], "passwordPolicyMinLength": 8}, "signupAttributes": ["EMAIL", "FAMILY_NAME", "GIVEN_NAME", "NAME", "PHONE_NUMBER"], "socialProviders": [], "usernameAttributes": ["EMAIL"], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito", "output": {"CreatedSNSRole": "arn:aws:iam::************:role/snsfeb4ea87141648-amplifydev", "UserPoolId": "us-east-1_55otBQ6qB", "AppClientIDWeb": "2p3viuv1dv714a6jufiskquk51", "AppClientID": "4fbpvrlkf63aveojpgc7ngkodd", "IdentityPoolId": "us-east-1:5757ebf7-ce2f-4353-b18f-7d3d5a0ef12f", "UserPoolArn": "arn:aws:cognito-idp:us-east-1:************:userpool/us-east-1_55otBQ6qB", "IdentityPoolName": "myvillageprojectadmifeb4ea87_identitypool_feb4ea87__amplifydev", "UserPoolName": "myvillageprojectadmifeb4ea87_userpool_feb4ea87"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/auth/myvillageprojectadmifeb4ea87-cloudformation-template.json", "logicalId": "authmyvillageprojectadmifeb4ea87"}, "lastPushDirHash": "EoBVuEEDHvv21fU8XpDQhmhNzWU="}}, "function": {"bedrockchatbot": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"LambdaExecutionRoleArn": "arn:aws:iam::************:role/myvillageprojectadmiLambdaRole6cbdec05-amplifydev", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:************:function:bedrockchatbot-amplifydev", "Name": "bedrockchatbot-amplifydev", "LambdaExecutionRole": "myvillageprojectadmiLambdaRole6cbdec05-amplifydev"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/function/bedrockchatbot-cloudformation-template.json", "logicalId": "functionbedrockchatbot"}, "lastBuildTimeStamp": "2025-06-12T14:29:21.005Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-06-12T14:29:54.548Z", "distZipFilename": "bedrockchatbot-6845524f712f73655531-build.zip", "s3Bucket": {"deploymentBucketName": "amplify-myvillageprojectadmi-amplifydev-141648-deployment", "s3Key": "amplify-builds/bedrockchatbot-6845524f712f73655531-build.zip"}, "lastPushDirHash": "lduQcCbRSoJjl/wvBDQXg6CKFJg="}, "chat": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}], "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"LambdaExecutionRoleArn": "arn:aws:iam::************:role/myvillageprojectadmiLambdaRoleba1898f9-amplifydev", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:************:function:chat-amplifydev", "Name": "chat-amplifydev", "LambdaExecutionRole": "myvillageprojectadmiLambdaRoleba1898f9-amplifydev"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/function/chat-cloudformation-template.json", "logicalId": "functionchat"}, "s3Bucket": {"deploymentBucketName": "amplify-myvillageprojectadmi-amplifydev-141648-deployment", "s3Key": "amplify-builds/chat-6c6d7275515869664573-build.zip"}, "lastBuildTimeStamp": "2025-06-12T14:29:21.007Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-06-12T14:29:42.721Z", "distZipFilename": "chat-6c6d7275515869664573-build.zip", "lastPushDirHash": "TvSY0Zjuc4eSo5uYAgwVpRcqyF0="}, "customQueryFunction": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}], "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"LambdaExecutionRoleArn": "arn:aws:iam::************:role/myvillageprojectadmiLambdaRolee6073fb1-amplifydev", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:************:function:customQueryFunction-amplifydev", "Name": "customQueryFunction-amplifydev", "LambdaExecutionRole": "myvillageprojectadmiLambdaRolee6073fb1-amplifydev"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/function/customQueryFunction-cloudformation-template.json", "logicalId": "functioncustomQueryFunction"}, "s3Bucket": {"deploymentBucketName": "amplify-myvillageprojectadmi-amplifydev-141648-deployment", "s3Key": "amplify-builds/customQueryFunction-45746948547a7563586c-build.zip"}, "lastBuildTimeStamp": "2025-06-12T14:29:21.009Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-06-12T14:29:47.258Z", "distZipFilename": "customQueryFunction-45746948547a7563586c-build.zip", "lastPushDirHash": "qf0CzhUCGFfIx+4NzyDVXGCilo0="}, "getUserDetailFun": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}], "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"LambdaExecutionRoleArn": "arn:aws:iam::************:role/myvillageprojectadmiLambdaRoled077da63-amplifydev", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:************:function:getUserDetailFun-amplifydev", "Name": "getUserDetailFun-amplifydev", "LambdaExecutionRole": "myvillageprojectadmiLambdaRoled077da63-amplifydev"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/function/getUserDetailFun-cloudformation-template.json", "logicalId": "functiongetUserDetailFun"}, "s3Bucket": {"deploymentBucketName": "amplify-myvillageprojectadmi-amplifydev-141648-deployment", "s3Key": "amplify-builds/getUserDetailFun-45494931786358567949-build.zip"}, "lastBuildTimeStamp": "2025-06-12T14:29:21.010Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-06-12T14:29:56.914Z", "distZipFilename": "getUserDetailFun-45494931786358567949-build.zip", "lastPushDirHash": "Jt62JvzSqLznxMMJvYZKn3pv0BY="}, "graphqlCustomFunction": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}, {"attributes": ["BucketName"], "category": "storage", "resourceName": "s3myvillageprojectadminportalorgprofilelogo"}], "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"LambdaExecutionRoleArn": "arn:aws:iam::************:role/myvillageprojectadmiLambdaRoleeadc6474-amplifydev", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:************:function:graphqlCustomFunction-amplifydev", "Name": "graphqlCustomFunction-amplifydev", "LambdaExecutionRole": "myvillageprojectadmiLambdaRoleeadc6474-amplifydev"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/function/graphqlCustomFunction-cloudformation-template.json", "logicalId": "functiongraphqlCustomFunction"}, "s3Bucket": {"deploymentBucketName": "amplify-myvillageprojectadmi-amplifydev-141648-deployment", "s3Key": "amplify-builds/graphqlCustomFunction-78725652684253754638-build.zip"}, "lastBuildTimeStamp": "2025-06-12T14:29:21.012Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-06-12T14:29:58.557Z", "distZipFilename": "graphqlCustomFunction-78725652684253754638-build.zip", "lastPushDirHash": "4UgNQHX5BABOYMgpBlnFmiGydbA="}, "importDataFunction": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}, {"attributes": ["BucketName"], "category": "storage", "resourceName": "s3myvillageprojectadminportalorgprofilelogo"}], "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"LambdaExecutionRoleArn": "arn:aws:iam::************:role/myvillageprojectadmiLambdaRolea0ee5c39-amplifydev", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:************:function:importDataFunction-amplifydev", "Name": "importDataFunction-amplifydev", "LambdaExecutionRole": "myvillageprojectadmiLambdaRolea0ee5c39-amplifydev"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/function/importDataFunction-cloudformation-template.json", "logicalId": "functionimportDataFunction"}, "s3Bucket": {"deploymentBucketName": "amplify-myvillageprojectadmi-amplifydev-141648-deployment", "s3Key": "amplify-builds/importDataFunction-624f4b54534a352f2b48-build.zip"}, "lastBuildTimeStamp": "2025-06-12T14:29:21.021Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-06-12T14:29:47.717Z", "distZipFilename": "importDataFunction-624f4b54534a352f2b48-build.zip", "lastPushDirHash": "zmGvbNKF1IwSeGAfT25Vdkie/o8="}, "mobileGameAPIFunction": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}], "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"LambdaExecutionRoleArn": "arn:aws:iam::************:role/myvillageprojectadmiLambdaRole935ef795-amplifydev", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:************:function:mobileGameAPIFunction-amplifydev", "Name": "mobileGameAPIFunction-amplifydev", "LambdaExecutionRole": "myvillageprojectadmiLambdaRole935ef795-amplifydev"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/function/mobileGameAPIFunction-cloudformation-template.json", "logicalId": "functionmobileGameAPIFunction"}, "s3Bucket": {"deploymentBucketName": "amplify-myvillageprojectadmi-amplifydev-141648-deployment", "s3Key": "amplify-builds/mobileGameAPIFunction-337a4938503736643945-build.zip"}, "lastBuildTimeStamp": "2025-06-12T14:29:21.022Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-06-12T14:29:54.969Z", "distZipFilename": "mobileGameAPIFunction-337a4938503736643945-build.zip", "lastPushDirHash": "poZ7BWU/MLBXDCsTC31USnowxXs="}, "mvtToken": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"LambdaExecutionRoleArn": "arn:aws:iam::************:role/myvillageprojectadmiLambdaRole8fde9ff0-amplifydev", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:************:function:mvtToken-amplifydev", "Name": "mvtToken-amplifydev", "LambdaExecutionRole": "myvillageprojectadmiLambdaRole8fde9ff0-amplifydev"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/function/mvtToken-cloudformation-template.json", "logicalId": "functionmvtToken"}, "s3Bucket": {"deploymentBucketName": "amplify-myvillageprojectadmi-amplifydev-141648-deployment", "s3Key": "amplify-builds/mvtToken-6374415178302b315563-build.zip"}, "lastBuildTimeStamp": "2025-06-12T14:29:21.024Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-06-12T14:29:52.586Z", "distZipFilename": "mvtToken-6374415178302b315563-build.zip", "lastPushDirHash": "WmpyN093yrYMNmlWuxvrLtacH+M="}, "mvtWallet": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}], "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"LambdaExecutionRoleArn": "arn:aws:iam::************:role/myvillageprojectadmiLambdaRole9c96549c-amplifydev", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:************:function:mvtWallet-amplifydev", "Name": "mvtWallet-amplifydev", "LambdaExecutionRole": "myvillageprojectadmiLambdaRole9c96549c-amplifydev"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/function/mvtWallet-cloudformation-template.json", "logicalId": "functionmvtWallet"}, "lastBuildTimeStamp": "2025-06-12T14:29:21.026Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-06-12T14:29:52.979Z", "distZipFilename": "mvtWallet-672b36306e5656424d77-build.zip", "s3Bucket": {"deploymentBucketName": "amplify-myvillageprojectadmi-amplifydev-141648-deployment", "s3Key": "amplify-builds/mvtWallet-672b36306e5656424d77-build.zip"}, "lastPushDirHash": "/Evlu35U6Ka78e1tio2rovcUcE4="}, "notificationFunction": {"build": true, "dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}, {"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}], "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"LambdaExecutionRoleArn": "arn:aws:iam::************:role/myvillageprojectadmiLambdaRole220184e3-amplifydev", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:************:function:notificationFunction-amplifydev", "Name": "notificationFunction-amplifydev", "LambdaExecutionRole": "myvillageprojectadmiLambdaRole220184e3-amplifydev"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/function/notificationFunction-cloudformation-template.json", "logicalId": "functionnotificationFunction"}, "s3Bucket": {"deploymentBucketName": "amplify-myvillageprojectadmi-amplifydev-141648-deployment", "s3Key": "amplify-builds/notificationFunction-47757430704d4e615942-build.zip"}, "lastBuildTimeStamp": "2025-06-12T14:29:21.027Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-06-12T14:29:45.532Z", "distZipFilename": "notificationFunction-47757430704d4e615942-build.zip", "lastPushDirHash": "WF2emF7U1rU25Oq9d7h2N1Mq93Q="}, "sendEmail": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"LambdaExecutionRoleArn": "arn:aws:iam::************:role/myvillageprojectadmiLambdaRoleba7f6108-amplifydev", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:************:function:sendEmail-amplifydev", "Name": "sendEmail-amplifydev", "LambdaExecutionRole": "myvillageprojectadmiLambdaRoleba7f6108-amplifydev"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/function/sendEmail-cloudformation-template.json", "logicalId": "functionsendEmail"}, "s3Bucket": {"deploymentBucketName": "amplify-myvillageprojectadmi-amplifydev-141648-deployment", "s3Key": "amplify-builds/sendEmail-3241732b79437076306e-build.zip"}, "lastBuildTimeStamp": "2025-06-12T14:29:21.032Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-06-12T14:29:41.788Z", "distZipFilename": "sendEmail-3241732b79437076306e-build.zip", "lastPushDirHash": "SVoMwa+6txM58BzSJ3Y8ed0nB9c="}, "usersManagementFunction": {"build": true, "dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}, {"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}], "providerPlugin": "awscloudformation", "service": "Lambda", "output": {"LambdaExecutionRoleArn": "arn:aws:iam::************:role/myvillageprojectadmiLambdaRolec1ef192b-amplifydev", "Region": "us-east-1", "Arn": "arn:aws:lambda:us-east-1:************:function:usersManagementFunction-amplifydev", "Name": "usersManagementFunction-amplifydev", "LambdaExecutionRole": "myvillageprojectadmiLambdaRolec1ef192b-amplifydev"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/function/usersManagementFunction-cloudformation-template.json", "logicalId": "functionusersManagementFunction"}, "s3Bucket": {"deploymentBucketName": "amplify-myvillageprojectadmi-amplifydev-141648-deployment", "s3Key": "amplify-builds/usersManagementFunction-595170746a7851757133-build.zip"}, "lastBuildTimeStamp": "2025-06-12T14:29:21.034Z", "lastBuildType": "PROD", "lastPackageTimeStamp": "2025-06-12T14:29:41.190Z", "distZipFilename": "usersManagementFunction-595170746a7851757133-build.zip", "lastPushDirHash": "ym/mJ3WYYtf/K09DFPttpOIcfsk="}}, "storage": {"s3myvillageprojectadminportalorgprofilelogo": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "S3", "output": {"BucketName": "myvillageproject-admin-portal-org-profile-logo141-amplifydev", "Region": "us-east-1"}, "lastPushTimeStamp": "2025-06-12T14:31:46.860Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-myvillageprojectadmi-amplifydev-141648-deployment/amplify-cfn-templates/storage/cloudformation-template.json", "logicalId": "storages3myvillageprojectadminportalorgprofilelogo"}, "lastPushDirHash": "POyHKow5PGi1ST4HiA9wM/BRErE="}}}