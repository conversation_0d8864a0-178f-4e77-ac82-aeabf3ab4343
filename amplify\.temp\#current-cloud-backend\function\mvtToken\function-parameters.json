{"lambdaLayers": [], "environmentVariableList": [{"cloudFormationParameterName": "rpcUrl", "environmentVariableName": "RPC_URL"}, {"cloudFormationParameterName": "privateKey", "environmentVariableName": "PRIVATE_KEY"}, {"cloudFormationParameterName": "mvtContractAddress", "environmentVariableName": "MVT_CONTRACT_ADDRESS"}, {"cloudFormationParameterName": "mvtWithdrawContractAddress", "environmentVariableName": "MVT_WITHDRAW_CONTRACT_ADDRESS"}, {"cloudFormationParameterName": "mvtUsdcContractAddress", "environmentVariableName": "MVT_USDC_CONTRACT_ADDRESS"}, {"cloudFormationParameterName": "etherscanApiKey", "environmentVariableName": "ETHERSCAN_API_KEY"}, {"cloudFormationParameterName": "stripeSecretKey", "environmentVariableName": "STRIPE_SECRET_KEY"}]}