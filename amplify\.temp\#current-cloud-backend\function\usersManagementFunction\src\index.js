/* Amplify Params - DO NOT EDIT
	API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT
	API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT
	AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID
	ENV
	REGION
Amplify Params - DO NOT EDIT */

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
const AWS = require("aws-sdk");
const uuid = require("uuid");

AWS.config.update({
  maxRetries: 3,
  httpOptions: { timeout: 30000, connectTimeout: 5000 },
  region: process.env.REGION,
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY,
});

const ddb = new AWS.DynamoDB();

exports.handler = async (event, context) => {
  try {
    const date = new Date();

    console.log("request.userAttributes", event.request.userAttributes);

    if (!event.request.userAttributes.sub) {
      console.log("Error: Nothing was written to DynamoDB, the user's email ID is unknown");
      return context.done(null, event);
    }

    const role = event.request.userAttributes['custom:role'] || '';
    const registeredFrom = event.request.userAttributes['custom:registeredFrom'] || '';
    const membershipId = uuid.v4();

    const scanParams = {
      ExpressionAttributeValues: { ":d": { S: "false" } },
      FilterExpression: "#D = :d",
      ExpressionAttributeNames: { "#D": "isDeleted" },
      ProjectionExpression: 'memberCode',
      TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };
    const scanResponse = await ddb.scan(scanParams).promise();
    const newRes = scanResponse.Items.map((records) => AWS.DynamoDB.Converter.unmarshall(records));
    const memberCodes = newRes.map(item => parseInt(item.memberCode) || 0);
    let highestMemberCode = Math.max(...memberCodes);

    const queryParams = {
      ExpressionAttributeValues: {
        ':n': { S: 'Jacksonville' },
      },
      FilterExpression: 'contains(#N, :n)',
      ExpressionAttributeNames: {
        '#N': 'name',
      },
      ProjectionExpression: "id",
      TableName: `City-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };
    const queryResponse = await ddb.scan(queryParams).promise();
    const records = queryResponse.Items;

    const newResponse = records.map((record) => AWS.DynamoDB.Converter.unmarshall(record));
    const cityId = newResponse[0]?.id || null;

    const userItem = {
      id: { S: event.request.userAttributes.sub },
      __typename: { S: "User" },
      email: { S: event.request.userAttributes.email },
      familyName: { S: event.request.userAttributes.family_name },
      givenName: { S: event.request.userAttributes.given_name },
      phoneNumber: { N: (event.request.userAttributes.phone_number).replace(/^\+[0-9]/, '') },
      isDeleted: { S: "false" },
      name: { S: event.request.userAttributes.name },
      cityId: { S: cityId },
      membershipId: { S: membershipId },
      isStakeholder: { BOOL: true },
      memberCode: { S: (highestMemberCode + 1).toString().padStart(4, '0') ?? "" },
      createdAt: { S: date.toISOString() },
      updatedAt: { S: date.toISOString() },
      _version: { N: "1" },
      _lastChangedAt: { N: date.getTime().toString() },
    };

    if (role) {
      userItem.role = { S: role };
    }
    if (registeredFrom) {
      userItem.registeredFrom = { S: registeredFrom };
    }
    console.log("params", userItem);

    const membershipItem = {
      id: { S: membershipId },
      __typename: { S: "Membership" },
      lastAddedImpactScore: { S: "0.00" },
      currentImpactScore: { S: "0.00" },
      MVPTokens: { N: "0.00" },
      isActive: { BOOL: true },
      personsID: { S: event.request.userAttributes.sub },
      isDeleted: { S: "false" },
      imageUrl: { S: "" },
      type: { S: "User" },
      cityId: { S: cityId },
      name: { S: event.request.userAttributes.name },
      memberCode: { S: (highestMemberCode + 1).toString().padStart(4, '0') ?? "" },
      createdAt: { S: date.toISOString() },
      updatedAt: { S: date.toISOString() },
      _version: { N: "1" },
      _lastChangedAt: { N: date.getTime().toString() },
    };

    // Call DynamoDB
    await ddb.putItem({ Item: userItem, TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}` }).promise();
    await ddb.putItem({ Item: membershipItem, TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}` }).promise();

    console.log("Success: Everything executed correctly");
    context.done(null, event);
  } catch (error) {
    console.log("Error", error);
    context.done(error);
  }
};
