/**
 * Exchange Rate Service Test Suite
 * Tests for exchange rate calculations, liquidity validation, and fallback mechanisms
 */

const exchangeRateService = require('../exchangeRate.service');
const walletService = require('../../wallet/wallet.service');
const usdcService = require('../../usdc/usdc.service');

// Mock dependencies
jest.mock('../../wallet/wallet.service');
jest.mock('../../usdc/usdc.service');

describe('Exchange Rate Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('calculateExchangeRate', () => {
    test('should calculate exchange rate correctly with valid liquidity', async () => {
      // Arrange
      const usdcReserves = 10000.0;
      const mvtReserves = 20000.0;
      
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: usdcReserves,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: mvtReserves
      });

      // Act
      const result = await exchangeRateService.calculateExchangeRate();

      // Assert
      expect(result.rate).toBe(0.49); // (10000 / 20000) * 0.98 safety buffer
      expect(result.baseRate).toBe(0.5); // 10000 / 20000
      expect(result.liquidityRatio).toBe(0.5);
      expect(result.usdcReserves).toBe(10000.0);
      expect(result.mvtReserves).toBe(20000.0);
      expect(result.safetyBuffer).toBe(0.02);
    });

    test('should handle zero MVT reserves', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 10000.0,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 0
      });

      // Act & Assert
      await expect(exchangeRateService.calculateExchangeRate()).rejects.toThrow('No MVT tokens in circulation');
    });

    test('should handle zero USDC reserves', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 0,
        status: 'LOW_LIQUIDITY'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 20000.0
      });

      // Act & Assert
      await expect(exchangeRateService.calculateExchangeRate()).rejects.toThrow('No USDC liquidity available');
    });

    test('should apply safety buffer correctly', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 1000.0,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 1000.0
      });

      // Act
      const result = await exchangeRateService.calculateExchangeRate();

      // Assert
      expect(result.baseRate).toBe(1.0); // 1000 / 1000
      expect(result.rate).toBe(0.98); // 1.0 * 0.98 safety buffer
      expect(result.safetyBuffer).toBe(0.02);
    });

    test('should handle very small amounts', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 0.01,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 100.0
      });

      // Act
      const result = await exchangeRateService.calculateExchangeRate();

      // Assert
      expect(result.baseRate).toBe(0.0001); // 0.01 / 100
      expect(result.rate).toBe(0.000098); // 0.0001 * 0.98
    });

    test('should handle very large amounts', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 1000000.0,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 1000.0
      });

      // Act
      const result = await exchangeRateService.calculateExchangeRate();

      // Assert
      expect(result.baseRate).toBe(1000.0); // 1000000 / 1000
      expect(result.rate).toBe(980.0); // 1000 * 0.98
    });

    test('should validate result before returning', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: NaN,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 1000.0
      });

      // Act & Assert
      await expect(exchangeRateService.calculateExchangeRate()).rejects.toThrow('Invalid exchange rate calculated');
    });

    test('should handle service errors gracefully', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockRejectedValue(new Error('USDC service error'));

      // Act & Assert
      await expect(exchangeRateService.calculateExchangeRate()).rejects.toThrow('Failed to calculate exchange rate');
    });
  });

  describe('getExchangeRateSummary', () => {
    test('should return complete exchange rate summary', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 10000.0,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 20000.0
      });

      // Act
      const result = await exchangeRateService.getExchangeRateSummary();

      // Assert
      expect(result).toMatchObject({
        currentRate: 0.49,
        rateDisplay: '1 MVT = 0.49 USDC',
        liquidityStatus: {
          usdcReserves: 10000.0,
          mvtSupply: 20000.0,
          liquidityRatio: 0.5,
          status: 'AVAILABLE'
        }
      });
      expect(result.lastUpdated).toBeDefined();
    });

    test('should handle low liquidity scenarios', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 100.0,
        status: 'LOW_LIQUIDITY'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 10000.0
      });

      // Act
      const result = await exchangeRateService.getExchangeRateSummary();

      // Assert
      expect(result.liquidityStatus.status).toBe('LOW_LIQUIDITY');
      expect(result.currentRate).toBe(0.0098); // (100 / 10000) * 0.98
    });

    test('should use fallback rates when calculation fails', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockRejectedValue(new Error('ResourceNotFoundException'));

      // Act
      const result = await exchangeRateService.getExchangeRateSummary();

      // Assert
      expect(result).toMatchObject({
        currentRate: 0.5,
        rateDisplay: '1 MVT = 0.50 USDC (Fallback Rate)',
        liquidityStatus: {
          usdcReserves: 500.0,
          mvtSupply: 1000.0,
          liquidityRatio: 0.5,
          status: 'FALLBACK_MODE'
        }
      });
    });

    test('should handle table not found errors with fallback', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockRejectedValue(new Error('table does not exist'));

      // Act
      const result = await exchangeRateService.getExchangeRateSummary();

      // Assert
      expect(result.liquidityStatus.status).toBe('FALLBACK_MODE');
      expect(result.rateDisplay).toContain('Fallback Rate');
    });

    test('should handle liquidity errors with fallback', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockRejectedValue(new Error('liquidity pool unavailable'));

      // Act
      const result = await exchangeRateService.getExchangeRateSummary();

      // Assert
      expect(result.liquidityStatus.status).toBe('FALLBACK_MODE');
    });

    test('should throw error for non-system issues', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockRejectedValue(new Error('validation failed'));

      // Act & Assert
      await expect(exchangeRateService.getExchangeRateSummary()).rejects.toThrow('validation failed');
    });

    test('should handle authentication errors without fallback', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockRejectedValue(new Error('authentication required'));

      // Act & Assert
      await expect(exchangeRateService.getExchangeRateSummary()).rejects.toThrow('authentication required');
    });
  });

  describe('validateSwapFeasibility', () => {
    test('should validate feasible swap', async () => {
      // Arrange
      const mvtAmount = 100;
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 10000.0,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 20000.0
      });

      // Act
      const result = await exchangeRateService.validateSwapFeasibility(mvtAmount);

      // Assert
      expect(result.isValid).toBe(true);
      expect(result.conversionData.mvtAmount).toBe(100);
      expect(result.conversionData.usdcAmount).toBe(49.0); // 100 * 0.49
      expect(result.conversionData.exchangeRate).toBe(0.49);
    });

    test('should reject swap with insufficient USDC liquidity', async () => {
      // Arrange
      const mvtAmount = 1000;
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 100.0, // Only 100 USDC available
        status: 'LOW_LIQUIDITY'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 20000.0
      });

      // Act
      const result = await exchangeRateService.validateSwapFeasibility(mvtAmount);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Insufficient USDC liquidity');
    });

    test('should validate MVT amount parameter', async () => {
      // Act & Assert
      await expect(exchangeRateService.validateSwapFeasibility(0)).rejects.toThrow('MVT amount must be greater than 0');
      await expect(exchangeRateService.validateSwapFeasibility(-100)).rejects.toThrow('MVT amount must be greater than 0');
      await expect(exchangeRateService.validateSwapFeasibility(1.5)).rejects.toThrow('MVT amount must be an integer');
    });

    test('should handle exchange rate calculation failures', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockRejectedValue(new Error('Rate calculation failed'));

      // Act & Assert
      await expect(exchangeRateService.validateSwapFeasibility(100)).rejects.toThrow('Failed to validate swap feasibility');
    });

    test('should handle edge case amounts', async () => {
      // Arrange
      const mvtAmount = 1; // Minimum amount
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 10000.0,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 20000.0
      });

      // Act
      const result = await exchangeRateService.validateSwapFeasibility(mvtAmount);

      // Assert
      expect(result.isValid).toBe(true);
      expect(result.conversionData.mvtAmount).toBe(1);
      expect(result.conversionData.usdcAmount).toBe(0.49);
    });

    test('should handle very large swap amounts', async () => {
      // Arrange
      const mvtAmount = 10000;
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 10000.0,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 20000.0
      });

      // Act
      const result = await exchangeRateService.validateSwapFeasibility(mvtAmount);

      // Assert
      expect(result.isValid).toBe(false); // Would require 4900 USDC but only 10000 available
      expect(result.error).toContain('Insufficient USDC liquidity');
    });
  });

  describe('Rate Calculation Edge Cases', () => {
    test('should handle precision issues with small numbers', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 0.000001,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 1000000.0
      });

      // Act
      const result = await exchangeRateService.calculateExchangeRate();

      // Assert
      expect(result.rate).toBeGreaterThan(0);
      expect(Number.isFinite(result.rate)).toBe(true);
    });

    test('should handle precision issues with large numbers', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 999999999.99,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 1.0
      });

      // Act
      const result = await exchangeRateService.calculateExchangeRate();

      // Assert
      expect(Number.isFinite(result.rate)).toBe(true);
      expect(result.rate).toBeLessThan(Number.MAX_SAFE_INTEGER);
    });

    test('should round rates to appropriate precision', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 1.0,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 3.0
      });

      // Act
      const result = await exchangeRateService.calculateExchangeRate();

      // Assert
      expect(result.rate.toString()).toMatch(/^\d+\.\d{1,6}$/); // Max 6 decimal places
      expect(result.baseRate.toString()).toMatch(/^\d+\.\d{1,6}$/);
    });
  });

  describe('Concurrent Operations', () => {
    test('should handle concurrent rate calculations', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 10000.0,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 20000.0
      });

      // Act
      const promises = Array(5).fill().map(() => exchangeRateService.calculateExchangeRate());
      const results = await Promise.all(promises);

      // Assert
      expect(results).toHaveLength(5);
      expect(results.every(result => result.rate === 0.49)).toBe(true);
    });

    test('should handle concurrent swap validations', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 10000.0,
        status: 'AVAILABLE'
      });
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 20000.0
      });

      // Act
      const promises = [
        exchangeRateService.validateSwapFeasibility(100),
        exchangeRateService.validateSwapFeasibility(200),
        exchangeRateService.validateSwapFeasibility(300)
      ];
      const results = await Promise.all(promises);

      // Assert
      expect(results).toHaveLength(3);
      expect(results.every(result => result.isValid)).toBe(true);
      expect(results[0].conversionData.usdcAmount).toBe(49.0);
      expect(results[1].conversionData.usdcAmount).toBe(98.0);
      expect(results[2].conversionData.usdcAmount).toBe(147.0);
    });
  });

  describe('Error Recovery and Resilience', () => {
    test('should recover from temporary service failures', async () => {
      // Arrange
      usdcService.getUSDCLiquidityPool
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockResolvedValueOnce({
          totalReserves: 10000.0,
          status: 'AVAILABLE'
        });
      
      walletService.getCentralWalletBalance.mockResolvedValue({
        mvtBalance: 20000.0
      });

      // Act
      const firstCall = exchangeRateService.getExchangeRateSummary();
      await expect(firstCall).resolves.toMatchObject({
        liquidityStatus: { status: 'FALLBACK_MODE' }
      });

      const secondCall = await exchangeRateService.calculateExchangeRate();

      // Assert
      expect(secondCall.rate).toBe(0.49);
    });
  });
});
