const { 
  validateUSDCAmount, 
  isNonEmptyString 
} = require('../../shared/utils/validationUtils');

/**
 * Validate USDC deposit input (USDC amounts can be floats)
 * @param {object} input - USDC deposit input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateUSDCDepositInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { amount, description } = input;

  // Use USDC amount validation (allows floats)
  const amountValidation = validateUSDCAmount(amount);
  if (!amountValidation.isValid) {
    return amountValidation;
  }

  if (description && !isNonEmptyString(description)) {
    return {
      isValid: false,
      error: "Description must be a non-empty string if provided"
    };
  }

  return { isValid: true };
}

/**
 * Validate USDC withdrawal input (USDC amounts can be floats)
 * @param {object} input - USDC withdrawal input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateUSDCWithdrawalInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { amount, description } = input;

  // Use USDC amount validation (allows floats)
  const amountValidation = validateUSDCAmount(amount);
  if (!amountValidation.isValid) {
    return amountValidation;
  }

  if (description && !isNonEmptyString(description)) {
    return {
      isValid: false,
      error: "Description must be a non-empty string if provided"
    };
  }

  return { isValid: true };
}

/**
 * Validate USDC amount for operations
 * @param {number} amount - Amount to validate
 * @param {string} operation - Operation name for error messages
 * @returns {object} - Validation result
 */
function validateUSDCOperationAmount(amount, operation = 'operation') {
  if (amount === undefined || amount === null) {
    return { isValid: false, error: `Amount is required for ${operation}` };
  }
  
  if (typeof amount !== 'number') {
    return { isValid: false, error: `Amount must be a number for ${operation}` };
  }
  
  if (amount <= 0) {
    return { isValid: false, error: `Amount must be greater than 0 for ${operation}` };
  }
  
  // USDC allows decimal amounts (unlike MVT which requires integers)
  if (!isFinite(amount)) {
    return { isValid: false, error: `Amount must be a valid number for ${operation}` };
  }
  
  // Check for reasonable precision (USDC has 6 decimals)
  const decimalPlaces = (amount.toString().split('.')[1] || '').length;
  if (decimalPlaces > 6) {
    return { isValid: false, error: `Amount cannot have more than 6 decimal places for ${operation}` };
  }
  
  return { isValid: true };
}

/**
 * Validate liquidity pool request parameters
 * @param {object} params - Request parameters
 * @returns {object} - Validation result
 */
function validateLiquidityPoolRequest(params) {
  // No specific validation needed for getting liquidity pool data
  // Admin authorization is handled at the handler level
  return { isValid: true };
}

module.exports = {
  validateUSDCDepositInput,
  validateUSDCWithdrawalInput,
  validateUSDCOperationAmount,
  validateLiquidityPoolRequest
};
